CREATE VIEW hr.v_emp_end_of_service
WITH ( security_barrier = true, security_invoker = true) AS 

Select
  ee.emp_id as emp_id,
  ee.emp_name as emp_name,
  ee.active_ as active_,
  ee.emp_type_id as emp_type_id,
  ee.emp_type_description as emp_type_description,

  ot.name_ as decision_type,

  d.date_ as decision_date,
  d.branch_id as decision_branch_id,
  d.ref_ as decision_ref,
  
  demp.date_ as effective_date

From hr.v_emp_employment ee 
  inner join hr."decisionExtend" demp on ee.emp_id = demp.emp_id
  inner join hr.decision d on demp.decision_id = d.pk
  inner join enterprise."opTypes" ot on d."opType_id" = ot.pk;

ALTER TABLE hr.v_emp_end_of_service owner to #POSTGRES_DEFAULT_ADMIN#;