CREATE TABLE "enterprise"."legalForm" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."legalForm_id_seq"'::regclass) ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 CONSTRAINT "legalForm_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."legalForm" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."legalForm_id_seq" OWNED BY "enterprise"."legalForm"."pk";
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('0', 'غير مخصص');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('1', 'مؤسسة تجارية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('2', 'شركة مساهمة لبنانية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('3', 'شركة محدودة المسؤولية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('4', 'شركة التوصية البسيطة');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('5', 'شركة التضامن');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('6', 'شركة الأوف شور');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('7', 'شركة الهولدنغ');
