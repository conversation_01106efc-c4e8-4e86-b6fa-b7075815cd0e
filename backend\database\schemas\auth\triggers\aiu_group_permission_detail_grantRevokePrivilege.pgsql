CREATE OR REPLACE FUNCTION auth.au_group_permission_detail_grantRevokePrivilege()
RETURNS TRIGGER AS $$
DECLARE
    _rolename TEXT;
    _privilege TEXT;
    _schema_name TEXT;
    _object_name TEXT;
BEGIN
     -- Fetch the role name from the group table using the group_id from group_permission
        SELECT g.rolename
        INTO _rolename
        FROM auth.group g
        INNER JOIN auth.group_permission gp ON g.pk = gp.group_id
        WHERE gp.pk = NEW.group_permission_id;    

    -- Fetch the privilege from the permission table using permission_name_id
        SELECT p.privilege
        INTO _privilege
        FROM auth.permission p
        WHERE p.name_ = NEW.permission_name_id;

    -- Fetch the schema name from the appObjects table using appObjects_id in group_permission
        SELECT ao.schema_ , ao.name_
        INTO _schema_name , _object_name
        FROM auth.appObjects ao
        INNER JOIN auth.group_permission gp ON ao.pk = gp.appObjects_id
        WHERE gp.pk = NEW.group_permission_id;

    IF NEW.allow = false THEN -- don't need to check fieldlist
        IF OLD.allow = true THEN
            EXECUTE 'REVOKE ' || _privilege || ' ON ' || _schema_name || '.' || _object_name || ' FROM ' || _rolename;
        ELSE
            RAISE Warning 'Field List is not applicable when allow is false';
        END IF;
        NEW.fieldlist := Null;
        -- revoke usage on schema if neccessary
        perform auth.f_check_and_revoke_schema_usage_if_no_object_privs(_schema_name, _rolename);
    
    END IF;

    IF OLD.allow = true THEN
        EXECUTE 'REVOKE ' || _privilege || ' ON ' || _schema_name || '.' || _object_name || ' FROM ' || _rolename;

        IF NEW.fieldlist IS NOT NULL THEN
            EXECUTE 'GRANT ' || _privilege || ' (' || NEW.fieldlist || ') ON ' || _schema_name || '.' || _object_name ||' TO ' || _rolename;
        ELSE
            Execute format('GRANT %I ON %s.%s TO %I', _privilege, _schema_name, _object_name, _role_name);
        END IF;

        Execute format('GRANT USAGE ON %I TO I%;', _schema_name, _role_name);
    END IF;
  
    EXECUTE 'COMMIT;';
    RETURN NEW;

END;
$$ LANGUAGE plpgsql;

alter function auth.au_group_permission_detail_grantRevokePrivilege() owner to #POSTGRES_DEFAULT_ADMIN#;
CREATE TRIGGER trg_aiu_group_permission_detail_grantRevokePrivilege
after UPDATE of allow, fieldlist ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.au_group_permission_detail_grantRevokePrivilege();
