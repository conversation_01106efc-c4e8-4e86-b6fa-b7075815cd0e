CREATE TABLE "person"."personContact" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('person."personContact_id_seq"'::regclass) ,
 "person_id" BIGINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDA<PERSON> CASCADE ON DELETE CASCADE,
 "name_" VARCHAR(50) NOT NULL ,
 "post" VARCHAR(150) ,
 "phone" VARCHAR(18) NOT NULL ,
 CONSTRAINT "personContact_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."personContact" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "person"."personContact_id_seq" OWNED BY "person"."personContact"."pk";
