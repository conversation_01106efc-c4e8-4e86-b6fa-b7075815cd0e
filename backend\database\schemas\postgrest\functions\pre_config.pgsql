create or replace function postgrest.pre_config()
returns void 
SECURITY INVOKER
as $$
  select
      set_config('pgrst.db_schemas', 'api', false)
    , set_config('pgrst.jwt_secret', current_setting('app.jwt_secret'), false)
    , set_config('pgrst.jwt_cache_max_lifetime', current_setting('app.jwt_expiry'), false)
    , set_config('pgrst.db_anon_role', #DB_ANONYMOUS_USER_LITERAL#, false)
    , set_config('pgrst.jwt_secret_is_base64', false::text, false)
    , set_config('pgrst.db_pre_request', #PRE_REQUEST#, false);
$$ language sql;

alter function postgrest.pre_config() owner to #POSTGRES_DEFAULT_ADMIN#;
grant execute on function postgrest.pre_config() to #DB_AUTHENTICATOR_USER#;
