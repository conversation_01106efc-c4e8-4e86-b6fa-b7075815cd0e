CREATE TABLE "hr"."decisionEmployment" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionEmployment_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "decisionEmployment_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionEmployment" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."decisionEmployment_id_seq" OWNED BY "hr"."decisionEmployment"."pk";
