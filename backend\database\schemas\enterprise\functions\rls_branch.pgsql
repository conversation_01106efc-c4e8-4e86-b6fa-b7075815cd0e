CREATE OR REPLACE FUNCTION enterprise.rls_branch(p_pk SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY INVOKER AS $$
BEGIN

  IF current_setting('db.single_branch')::BOOLEAN = True THEN
    raise EXCEPTION 'Single branch is enabled this function should not be called';
  END IF;

  IF EXISTS ( SELECT 1 
              FROM auth.user_branch ub 
              WHERE ub.user_id = current_setting('myapp.user_id')::SMALLINT and ub.branch_id = p_pk
            ) THEN        
    RETURN TRUE;        
  END IF;

  RETURN FALSE;

END;
$$;

alter function enterprise.rls_branch(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#; --rls_branch