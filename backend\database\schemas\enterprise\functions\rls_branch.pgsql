CREATE OR REPLACE FUNCTION enterprise.rls_branch(pkp SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
  user_group_ids SMALLINT[];
  v_has_access BOOLEAN:=False;
BEGIN
    -- Get all group_ids associated with the user
    SELECT ARRAY_AGG(group_id)
    INTO user_group_ids
    FROM auth.user_group
    WHERE user_id = current_setting('myapp.user_id')::SMALLINT;

    -- Check if the user belongs to any groups
    IF user_group_ids IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Check for direct branch access
    IF EXISTS (
        SELECT 1 FROM auth.user_branch WHERE group_id = ANY(user_group_ids) AND branch_id = pkp
    ) THEN
        RETURN TRUE;
    END IF;

    IF current_setting('db.single_branch')::BOOLEAN = True THEN
      RETURN FALSE;
    ELSE  
      WITH RECURSIVE child_branches AS (
      SELECT pkp AS id, ARRAY[pkp] AS path
      FROM enterprise.branch
      WHERE pk = pkp

      UNION ALL
      SELECT b.pk, cb.path || b.pk
      FROM enterprise.branch b
      JOIN child_branches cb ON b.parent_id = cb.id
      )
      SELECT TRUE INTO v_has_access
      FROM auth.group_branch gb
      WHERE gb.group_id = ANY(user_group_ids)
        AND gb.branch_id = ANY(SELECT id FROM child_branches);
        
    END IF;

    RETURN v_has_access;

END;
$$;

alter function enterprise.rls_branch(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#; --rls_branch