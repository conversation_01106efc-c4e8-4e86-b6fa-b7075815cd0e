CREATE TABLE "hr"."emp" (
 "pk" BIGINT NOT NULL DEFAULT nextval('person."person_id_seq"'::regclass) REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "social_security" BIGINT UNIQUE ,
 "financial_number" BIGINT UNIQUE ,
 "birth_date" DATE ,
 "start_date" DATE ,
 "end_date" DATE ,
 "first_name" VARCHA<PERSON>(50) ,
 "father_name" VARCHAR(50) ,
 "family_name" VARCHAR(50) ,
 "mother_fullname" VARCHA<PERSON>(100) ,
 CONSTRAINT "emp_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."emp" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
