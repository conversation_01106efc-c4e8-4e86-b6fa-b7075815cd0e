CREATE TABLE "enterprise"."post" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."post_id_seq"'::regclass) ,
 "chefLevel" SMALLINT ,
 "authority" BOOLEAN ,
 "active_" BOOLEAN ,
 "name_" VARCHAR(150) NOT NULL UNIQUE ,
 "post_role" TEXT ,
 CONSTRAINT "post_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."post" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."post_id_seq" OWNED BY "enterprise"."post"."pk";
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('0', 'FALSE', 'TRUE', 'غير ملحوظ في النظام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('1', 'TRUE', 'TRUE', 'طبيب رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('2', 'TRUE', 'TRUE', 'طبيب رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('3', 'FALSE', 'TRUE', 'محرر او كاتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('4', 'FALSE', 'TRUE', 'محاسب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('5', 'FALSE', 'TRUE', 'مراقب صحي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('6', 'FALSE', 'TRUE', 'مأمور صحي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('7', 'FALSE', 'TRUE', 'حاجب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('8', 'FALSE', 'TRUE', 'ممرضة قابلة قانونبة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('9', 'TRUE', 'TRUE', 'مهندس صحي رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('10', 'FALSE', 'TRUE', 'مراقب صحي للابنية');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('11', 'FALSE', 'TRUE', 'مراقب صحي للمياه و المجاري');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('12', 'FALSE', 'TRUE', 'مراقب صحي للمحلات المصنفة و الاندية و المسابح');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('13', 'TRUE', 'TRUE', 'رئيس دائرة المعلوماتية');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('14', 'FALSE', 'TRUE', 'مهندس نظام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('15', 'FALSE', 'TRUE', 'محلل مبرمج');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('16', 'FALSE', 'TRUE', 'مبرمج');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('17', 'FALSE', 'TRUE', 'اخصائي فني');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('18', 'FALSE', 'TRUE', 'مدخل معلومات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('19', 'FALSE', 'TRUE', 'مستكتب (اول او ثان)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('20', 'TRUE', 'TRUE', 'رئيس مصلحة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('21', 'TRUE', 'TRUE', 'رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('22', 'FALSE', 'TRUE', 'امين صندوق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('23', 'FALSE', 'TRUE', 'معاون امين صندوق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('24', 'TRUE', 'TRUE', 'رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('25', 'FALSE', 'TRUE', 'مراقب ضرائب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('26', 'FALSE', 'TRUE', 'امين مستودع');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('27', 'FALSE', 'TRUE', 'مراقب عام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('28', 'TRUE', 'TRUE', 'مهندس رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('29', 'FALSE', 'TRUE', 'مراقب تنظيفات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('30', 'TRUE', 'TRUE', 'رئيس مراقبي التنظيفات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('31', 'FALSE', 'TRUE', 'مراقب صحي في فرقة مكافحة الحشرات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('32', 'FALSE', 'TRUE', 'ملاحق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('33', 'FALSE', 'TRUE', 'جاب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('34', 'TRUE', 'TRUE', 'مدير مركز رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('35', 'FALSE', 'TRUE', 'امين محفوظات(محرر)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('36', 'TRUE', 'TRUE', 'مهندس رئيس مصلحة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('37', 'FALSE', 'TRUE', 'رسام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('38', 'FALSE', 'TRUE', 'مدرب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('39', 'FALSE', 'TRUE', 'مناظر');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('40', 'FALSE', 'TRUE', 'مهندس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('41', 'FALSE', 'TRUE', 'معاون مهندس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('42', 'FALSE', 'TRUE', 'طوبوغراف');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('43', 'FALSE', 'TRUE', 'مراقب(محلف)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('44', 'FALSE', 'TRUE', 'مدرب ممتاز');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('45', 'FALSE', 'TRUE', 'مهندس زراعي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('46', 'FALSE', 'TRUE', 'حارس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('47', 'FALSE', 'TRUE', 'خادم جنيناتي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('48', 'TRUE', 'TRUE', 'مهندس ميكانيك-رئيس مرآب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('49', 'FALSE', 'TRUE', 'سائق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('50', 'FALSE', 'TRUE', 'سائق رئيس السائقين');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('51', 'FALSE', 'TRUE', 'سائق ونش و خلافه');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('53', 'FALSE', 'TRUE', 'مشرف عمال');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('54', 'FALSE', 'TRUE', 'عامل');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('55', 'FALSE', 'TRUE', 'معلم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('56', 'FALSE', 'TRUE', 'مستكتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('57', 'FALSE', 'TRUE', 'كاتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('58', 'FALSE', 'TRUE', 'مستكتب (كاتب)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('59', 'TRUE', 'TRUE', 'المشرف');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('62', 'FALSE', 'TRUE', 'عمال الورشة الدائمة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('63', 'FALSE', 'TRUE', 'معلم او عامل اختصاص');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('64', 'FALSE', 'TRUE', 'شرطي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('65', 'TRUE', 'TRUE', 'قائد الشرطة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('66', 'FALSE', 'TRUE', 'متدرب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('67', 'FALSE', 'TRUE', 'معاون امين مستودع');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('68', 'TRUE', 'TRUE', 'رئيس المراقبين الصحيين');
