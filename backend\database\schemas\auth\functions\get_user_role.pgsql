create or replace function auth.get_user_role(username VA<PERSON>HA<PERSON>(150), pass_ text) returns name
  language plpgsql
  as $$
begin
  return (
  select rolename from auth.user as u
   where u.username = username
     and u.pass_word_ = #DB_SCHEMA_EXTENSION#.crypt(pass_, u.password_ )
     and u.is_active = true
  );
end;
$$;

alter function auth.get_user_role(username VARCHAR(150), pass_ text) owner to #POSTGRES_DEFAULT_ADMIN#;