CREATE VIEW hr.v_emp_employment_transfer
WITH ( security_barrier = true,  security_invoker = true ) AS

Select 
    coalesce(s.emp_id, f.emp_id) as emp_id,
    coalesce(s.emp_name, f.emp_name) as emp_name,
    coalesce(s.emp_persontype_id, f.emp_persontype_id) as emp_persontype_id,
    coalesce(s.decision_branch_id, f.decision_branch_id) as decision_branch_id,
    coalesce(s.target_department_id, f.target_department_id) as target_department_id,
    coalesce(s.target_post_id, f.target_post_id) as target_post_id

From hr.v_emp_employment f
Left Join (
  Select Distinct On (emp_id)
    emp_id,
    emp_name,
    emp_persontype_id,
    decision_branch_id,
    target_department_id,
    target_post_id
  From hr.v_emp_transfer
  Order By emp_id, decision_date Desc
) s on f.emp_id = s.emp_id;

ALTER TABLE hr.v_emp_employment_transfer owner to #POSTGRES_DEFAULT_ADMIN#;