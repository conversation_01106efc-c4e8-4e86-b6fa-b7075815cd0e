import csv
import os
import logging
import psycopg2

from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from contextlib import contextmanager
from dotenv import load_dotenv
from logging import ERROR, INFO, WARNING, DEBUG

from colorlog import ColoredFormatter

def strtobool(val):
    '''
    Convert a string representation of truth to true (1) or false (0).
    True values are 'y', 'yes', 't', 'true', 'on', and '1';
    false values are 'n', 'no', 'f', 'false', 'off', and '0'.
    Raises ValueError if 'val' is anything else.
    '''
    val = val.lower()
    if val in ('y', 'yes', 't', 'true', 'on', '1'):
        return True
    elif val in ('n', 'no', 'f', 'false', 'off', '0'):
        return False
    else:
        raise ValueError(f"Invalid boolean value: {val}")

# Get the environment variable
env_file = os.getenv("ENV_FILE")
if env_file:
    load_dotenv(dotenv_path=env_file)

loglvl_str = os.getenv("LOG_LEVEL", "INFO").upper()

# Set the log level based on the environment variable
if loglvl_str == "DEBUG":
    loglvl = DEBUG
elif loglvl_str == "WARNING":
    loglvl = WARNING
elif loglvl_str == "ERROR":
    loglvl = ERROR
else:
    loglvl = INFO  # Default to INFO if the environment variable is invalid

class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]

class Logger(metaclass=SingletonMeta):
    def __init__(self):
        # Set up logging
        self.log_file = os.path.join(os.path.dirname(__file__), 'database.log')

        # Create formatter with color
        formatter = ColoredFormatter(
            "%(log_color)s%(levelname)s: %(message)s%(reset)s",
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'bold_red',
            }
        )
        
        ch = logging.StreamHandler()
        ch.setFormatter(formatter)
        
        logging.basicConfig(
            level = loglvl,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                ch #logging.StreamHandler().setFormatter(formatter)  # Keep console output as well
            ]
        )
        self._Clog = logging.getLogger(__name__)
    
    def info(self, message):
        self._Clog.info(message)

    def error(self, message):
        self._Clog.error(message)
        
    def warning(self, message):
        self._Clog.warning(message)
    
    def debug(self, message):
        self._Clog.debug(message)
                
class Config(Logger):
    # Class attributes for shared state
    _APP_LIST_DATA: dict = {}
    _app_list_loaded: bool = False
    
    def __init__(self):
        super().__init__()  # Call the parent class constructor
        self.DB_SCHEMA_EXTENSION = 'extschema' 
        #self.ROLE_PREFIX = os.environ.get('DB_NAME', "") + '_role_'
        #self.GROUP_PREFIX = os.environ.get('DB_NAME', "") + '_group_'
        self.SCHEMA_DIR = os.path.join(os.path.dirname(__file__), 'database', 'schemas')
        
        # features
        self.SINGLE_BRANCH = bool(strtobool(os.environ.get('SINGLE_BRANCH', "True")))
        self.SINGLE_DEPARTMENT = bool(strtobool(os.environ.get('SINGLE_DEPARTMENT', "True")))
        
        # Initialize APP_LIST as an empty dict before loading
        self.APP_LIST : dict = {}
        
        if not Config._app_list_loaded:
            # _load_app_list_from_csv will use self.SCHEMA_DIR
            # and populate Config._APP_LIST_DATA
            self._load_app_list_from_csv()
            Config._app_list_loaded = True
            
        # All instances will refer to the same centrally loaded data
        self.APP_LIST = Config._APP_LIST_DATA
        
        
    # Method to load app.csv
    def _load_app_list_from_csv(self):
        """
        Loads application labels and their primary keys from auth/app.csv
        into self.APP_LIST.
        This method should be called during Config initialization.
        """
        # Assuming self.SCHEMA_DIR is already correctly defined in your Config class
        # (e.g., self.SCHEMA_DIR = r"c:\Projects\AAS\database\sql")
        app_csv_path = os.path.join(self.SCHEMA_DIR, 'auth', 'tables','app.csv')
        
        if not os.path.exists(app_csv_path):
            self.error(f"CRITICAL: Application CSV file not found at: {app_csv_path}. APP_LIST will be empty.")
            raise FileNotFoundError(f"Application CSV file not found at: {app_csv_path}")


        temp_app_list : dict = {}
        try:
            with open(app_csv_path, mode='r', encoding='utf-8', newline='') as csvfile:
                reader = csv.DictReader(csvfile, delimiter='|')
                
                 # Debug the fieldnames to see what's available
                self.debug(f"CSV headers: {reader.fieldnames}")
                
                if 'pk' not in reader.fieldnames or 'appLable' not in reader.fieldnames:
                    self.error(f"CRITICAL: CSV file {app_csv_path} is missing 'pk' or 'appLable' headers. APP_LIST will be empty.")
                    raise ValueError(f"CSV file {app_csv_path} is missing 'pk' or 'appLable' headers.")
                
                for row_num, row in enumerate(reader, 1):
                    app_lable = row.get('appLable')
                    pk_str = row.get('pk')

                    if not app_lable or pk_str is None:
                        self.warning(f"Skipping row {row_num} in {app_csv_path} due to missing appLable or pk: {row}")
                        continue
                    
                    try:
                        pk_value = int(pk_str)
                        if app_lable.lower() in temp_app_list:
                            self.warning(f"Duplicate appLable '{app_lable}' found in {app_csv_path} (row {row_num}). Using first encountered pk ({temp_app_list[app_lable]}).")
                        else:
                            temp_app_list[app_lable.lower()] = pk_value
                    except ValueError:
                        self.error(f"Invalid 'pk' value '{pk_str}' for appLable '{app_lable}' in {app_csv_path} (row {row_num}). Skipping this entry.")
            
            Config._APP_LIST_DATA = temp_app_list # Assign to the class attribute
            self.info(f"Successfully loaded {temp_app_list.keys().__len__()} app configurations into APP_LIST from {app_csv_path}.")
            
        except Exception as e:
            self.error(f"CRITICAL: An unexpected error occurred while loading app configurations from {app_csv_path}: {e}")
            self.APP_LIST = {} # Ensure APP_LIST is empty on critical failure
                

class DBConfig(Config):
       
    def __init__(self):
        super().__init__()
        """Validate required database parameters"""
        required_params = {
            'DB_NAME': os.environ.get('DB_NAME', None),
            'POSTGRES_DEFAULT_PASSWORD': os.environ.get('POSTGRES_DEFAULT_PASSWORD', None)
        }
        
        missing_params = [param for param, value in required_params.items() if not value]
        
        if missing_params:
            self.error(f"Missing required parameters: {', '.join(missing_params)}")
            quit()
        
        # accessing Clog from Logger grand parent class
        self.info(f"Database connection required parameters met")
        #self.Clog.info(f"Database connection required parameters met")
        # Database connection parameters
        self.DB_HOST = os.environ.get('DB_HOST', 'localhost')
        self.DB_PORT = os.environ.get('DB_PORT', '5432')
        self.DB_NAME = os.environ.get('DB_NAME', None) # required
        self.POSTGRES_DEFAULT_PASSWORD = os.environ.get('POSTGRES_DEFAULT_PASSWORD', None) # required
        self.POSTGRES_DEFAULT_ADMIN = os.environ.get('POSTGRES_DEFAULT_ADMIN', 'postgres')

        self.DB_ADMIN_USER = os.environ.get('DB_ADMIN_USER', 'admin')
        self.DB_ADMIN_PASSWORD = os.environ.get('DB_ADMIN_PASSWORD', 'ABmakingdbpass')     

        self.DB_CONNECT_TIMEOUT = int(os.environ.get('DB_CONNECT_TIMEOUT', '10'))
        
        # Database connection pooling parameters
        self.MAX_RETRIES = int(os.environ.get('DB_MAX_RETRIES', '3'))
        self.RETRY_DELAY = int(os.environ.get('DB_RETRY_DELAY', '2'))
        
        self.USERS_PREFIX = os.environ.get('DB_NAME', None) + os.environ.get('DB_USER_ROLES_PREFIX', '_usr_')
        self.GROUPS_PREFIX = os.environ.get('DB_NAME', None) + os.environ.get('DB_GROUP_ROLES_PREFIX', '_gr_')
        
        # for later applying
        self.APP_APPLIED_LIST = os.environ.get('APP_APPLIED_LIST', 'AuthApp,EnterpriseApp,PersonApp').split(',')
        
        self.info(f"Database connection parameters set")
        
    
    @contextmanager
    def get_postgres_connection(self , dbname: str = 'postgres'):
        """Context manager for database connections"""
        conn = None
        try:
            conn = psycopg2.connect(
                host = config.DB_HOST,
                port = config.DB_PORT,
                database = dbname,
                user = config.POSTGRES_DEFAULT_ADMIN,
                password = config.POSTGRES_DEFAULT_PASSWORD,
                connect_timeout = config.DB_CONNECT_TIMEOUT,
                client_encoding = 'utf8',
                options = f'-c search_path={self.DB_SCHEMA_EXTENSION}'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            yield conn
        except psycopg2.OperationalError as e:
            config.error(f"Connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()    
                
config = DBConfig()

class PostgRESTConfig(Config):
    def __init__(self):
        super().__init__()
        """Validate required PostgREST parameters"""
        import secrets
        
        jwt_secret = secrets.token_hex(32)
        self.JWT_SECRET =  jwt_secret
        self.JWT_CACHE = '1 day'
        self.DB_ANONYMOUS_USER_LITERAL = config.DB_NAME + '_' + os.environ.get('DB_ANONYMOUS_USER' , '_anonymous')
        self.DB_ANONYMOUS_USER = config.DB_NAME + '_' + os.environ.get('DB_ANONYMOUS_USER' , '_anonymous') 
        self.DB_AUTHENTICATOR_USER = config.DB_NAME + '_' + os.environ.get('DB_AUTHENTICATOR_USER' , '_authenticator')
        self.DB_AUTHENTICATOR_PASSWORD = os.environ.get('DB_AUTHENTICATOR_PASSWORD' , 'ABmakingdbpass')
        self.PRE_REQUEST = 'postgrest.f_pre_request'
        self.IDENTIFIERS_LIST= ['DB_ANONYMOUS_USER','DB_AUTHENTICATOR_USER','DB_SCHEMA_EXTENSION',
                                'DB_ADMIN_USER','POSTGRES_DEFAULT_ADMIN']
        self.DB_ADMIN_USER = config.DB_ADMIN_USER
        self.info(f"PostgREST parameters set")   
        
    def get_attribute(self, attr_name):
        """Return the value of the given attribute name"""
        return getattr(self, attr_name, None)

postgrestConf = PostgRESTConfig()
