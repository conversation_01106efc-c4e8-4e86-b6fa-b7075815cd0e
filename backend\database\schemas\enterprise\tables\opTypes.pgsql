CREATE TABLE "enterprise"."opTypes" (
 "pk" SMALLINT NOT NULL ,
 "name_" VARCHAR(90) NOT NULL UNIQUE ,
 "prefix" VARCHAR(7) NOT NULL UNIQUE ,
 "appLable" VARCHAR(100) NOT NULL ,
 "filter" JSONB ,
 CONSTRAINT "opTypes_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."opTypes" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8001', 'HRO', 'إذن خروج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "exit"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8002', 'HRT', 'أمر مهمة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "exit"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8003', 'HRAL', 'إجازة إدارية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8004', 'HRHL', 'إجازة صحية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8005', 'HRDL', 'إجازة وفاة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8006', 'HRML', 'إجازة زواج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8007', 'HRMaL', 'إجازة أمومة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8008', 'HRSL', 'إجازة بدون راتب', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8009', 'HRBL', 'إجازة خارج البلاد', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8013', 'HRAL_M', 'تعديل إجازة إدارية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8014', 'HRHL_M', 'تعديل إجازة صحية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8015', 'HRDL_M', 'تعديل إجازة وفاة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8016', 'HRML_M', 'تعديل إجازة زواج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8017', 'HRMaL_M', 'تعديل إجازة أمومة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8018', 'HRSL_M', 'تعديل إجازة بدون راتب', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8019', 'HRBL_M', 'تعديل إجازة خارج البلاد', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8101', 'HRDT', 'نقل', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8102', 'HRDEnd', 'نهاية خدمة', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8103', 'HRDE', 'تعيين', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8104', 'HRDA', 'تكليف', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8105', 'HRDed', 'ترقين', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8111', 'HRNT', 'مذكرة نقل', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8112', 'HRNEnd', 'مذكرة نهاية خدمة', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8113', 'HRNE', 'مذكرة تعيين', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8114', 'HRNA', 'مذكرة تكليف', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8115', 'HRNed', 'مذكرة ترقين', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
