CREATE MATERIALIZED VIEW IF NOT EXISTS enterprise.v_enterprise_branch_department_posts
AS
SELECT
  ent.pk AS enterprise_id, 
  ent.name_ AS enterprise_name, 

  bdp.pk as branch_department_post_id,
  
  bd.branch_id AS branch_id, 
  br.name_ AS branch_name, 
  br.active_ AS branch_active,
  br.level_ AS branch_level,
  br.parent_id AS branch_parent_id, 
  
  bd.department_id AS department_id, 
  d.name_ AS department_name, 
  d.active_ AS department_active,
  d.level_ AS department_level,
  d.parent_id AS department_parent_id, 

  bdp.post_id AS post_id, 
  p.name_ AS post_name, 
  p.active_ AS post_active, 
  p."chefLevel" AS post_chef_level,
  p.authority AS post_authority

FROM 
enterprise.enterprise ent
  inner join enterprise.branch br on br.enterprise_id = ent.pk
  inner join enterprise.branch_department bd on br.pk = bd.branch_id
  inner join enterprise.department d on bd.department_id = d.pk
  inner join enterprise.branch_department_post bdp on bd.pk = bdp.branch_department_id
  inner join enterprise.post p on bdp.post_id = p.pk
WITH DATA;

ALTER TABLE enterprise.v_enterprise_branch_department_posts OWNER TO #POSTGRES_DEFAULT_ADMIN#;