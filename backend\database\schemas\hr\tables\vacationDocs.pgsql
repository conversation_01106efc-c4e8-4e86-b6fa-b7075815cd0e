CREATE TABLE "hr"."vacationDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationDocs_id_seq"'::regclass) ,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "daysCount" SMALLINT NOT NULL ,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "editowner_id" SMALLINT NOT NULL ,
 "fromDate" DATE NOT NULL ,
 "toDate" DATE NOT NULL ,
 "state" hr.docstate NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "note" TEXT NOT NULL ,
 CONSTRAINT "vacationDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationDocs" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."vacationDocs_id_seq" OWNED BY "hr"."vacationDocs"."pk";
