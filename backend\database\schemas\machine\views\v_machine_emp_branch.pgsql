CREATE OR REPLACE VIEW machine.v_machine_emp_branch
    WITH (
        security_barrier = true, 
        security_invoker = true
        )
    AS
    -- the rls must take care of user specific records
SELECT 
    br.pk as branch_id, 
    br.name_ as branch_name, 
    emp.pk as emp_id, 
    COALESCE(emp.first_name,'') || COALESCE(emp.father_name,' ') || COALESCE(emp.family_name,'') as empfullname,
    p.nickname,
    p.gender
FROM enterprise.branch br 
    inner join person.person_branch pb on br.pk = pb.branch_id
    inner join hr.emp emp on pb.person_id = emp.pk
    inner join person.person p on emp.pk = p.pk
;

ALTER TABLE machine.v_machine_emp_branch owner to #POSTGRES_DEFAULT_ADMIN#;