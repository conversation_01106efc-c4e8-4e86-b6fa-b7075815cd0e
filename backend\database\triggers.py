import os
import glob
from psycopg2 import sql, <PERSON>rror

from config import config, postgrestConf, get_config_attribute
from .schemas import check_schema_exists, get_schema_list_from_dir, SCHEMA_DIR

created_triggers: list[str] = []

def create_trigger_from_file(cur, file_path: str) -> bool:
    """
    Executes a SQL script to create a trigger function and trigger.

    Args:
        cur: Database cursor
        schema_name: Name of the schema where the trigger belongs
        file_path: Path to the SQL file containing the trigger definition

    Returns:
        bool: True if trigger was created successfully or already exists, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # Extract trigger name from file name (assuming file name is trigger_name.pgsql)
        trigger_file_name = os.path.splitext(os.path.basename(file_path))[0]
        schema_name = os.path.basename(os.path.dirname(os.path.dirname(file_path)))
        
        # Replace placeholders with actual values
        for key in postgrestConf.IDENTIFIERS_LIST:
            sql_script = sql_script.replace(f"#{key}#", get_config_attribute(key))

        config.debug(f"Executing SQL for trigger {schema_name}.{trigger_file_name}:\n{sql_script}")
        config.cumulativeSQL(sql_script)

        run_sql = sql.SQL(sql_script)
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)

        config.info(f"Successfully created/verified trigger {schema_name}.{trigger_file_name}")
        created_triggers.append(f"{schema_name}.{trigger_file_name}")
        return True

    except (Error, IOError) as e:
        config.error(f"Error creating trigger from file {file_path}: {e}")
        return False

def process_triggers_for_schema(cur, schema_name: str) -> bool:
    """
    Helper function to process all .pgsql files in a given schema's 'triggers' directory.

    Args:
        cur: Database cursor
        schema_name: The name of the schema to process.

    Returns:
        bool: True if all triggers were processed successfully, False otherwise
    """

    if not check_schema_exists(cur, schema_name):
        config.warning(f"Schema {schema_name} does not exist.")
        return False

    config.info(f"--- Processing triggers for schema: {schema_name} ---")
    triggers_dir = os.path.join(SCHEMA_DIR, schema_name, 'triggers')

    if not os.path.isdir(triggers_dir):
        config.warning(f"No 'triggers' directory found for schema {schema_name}. Skipping.")
        return True  # Not an error, just no triggers to process

    # Find all .pgsql files in the triggers directory
    pgsql_files = glob.glob(os.path.join(triggers_dir, '*.pgsql'))

    if not pgsql_files:
        config.info(f"No .pgsql files found in triggers directory for schema {schema_name}")
        return True

    for file_path in pgsql_files:
        if not create_trigger_from_file(cur, file_path):
            return False

    return True

def create_triggers() -> bool:
    """
    Main function to set up all triggers from triggers directory structure.

    Returns:
        bool: True if all triggers were processed successfully, False otherwise
    """
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            schema_list = get_schema_list_from_dir()
            config.info("--- Starting triggers creation process ---")

            for schema_name in schema_list:
                if not process_triggers_for_schema(cur, schema_name):
                    return False

            config.info("--- Triggers creation process finished. ---")
            return True

        except Exception as e:
            config.error(f"Error creating triggers: {e}")
            return False

def drop_trigger(cur, schema_name: str, function_name: str) -> bool:
    """
    Drop a trigger function.

    Args:
        cur: Database cursor
        schema_name: Schema name where the function exists
        function_name: Name of the function to drop

    Returns:
        bool: True if function was dropped successfully
    """
    try:
        run_sql = sql.SQL("DROP FUNCTION IF EXISTS {}.{} CASCADE").format(
            sql.Identifier(schema_name),
            sql.Identifier(function_name)
        )
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Dropped trigger function {schema_name}.{function_name}")
        return True

    except Exception as e:
        config.error(f"Error dropping trigger function {schema_name}.{function_name}: {e}")
        return False

def drop_triggers() -> bool:
    """
    Drop all created triggers and their functions.

    Returns:
        bool: True if all triggers were dropped successfully, False otherwise
    """
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            config.info("--- Starting triggers drop process ---")

            # Process triggers in reverse order
            for trigger_info in created_triggers[::-1]:
                schema_name, trigger_name = trigger_info.split('.', 1)

                # Extract function name from trigger name (common pattern)
                # Most trigger functions follow pattern: trigger_name -> function_name
                function_name = trigger_name.replace('trg_', '').replace('_trigger', '')

                if not drop_trigger(cur, schema_name, function_name):
                    config.warning(f"Failed to drop trigger function for {trigger_info}")
                    # Continue with other triggers even if one fails

            config.info("--- Triggers drop process finished. ---")
            return True

        except Exception as e:
            config.error(f"Error dropping triggers: {e}")
            return False
