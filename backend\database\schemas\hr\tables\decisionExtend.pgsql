CREATE TABLE "hr"."decisionExtend" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionExtend_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL UNIQUE REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 CONSTRAINT "decisionExtend_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionExtend" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."decisionExtend_id_seq" OWNED BY "hr"."decisionExtend"."pk";
