CREATE TABLE "hr"."vacationMAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationMAuthority_id_seq"'::regclass) ,
 "vacationDocs_id" BIGINT NOT NULL REFERENCES "hr"."vacationMDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT NOT NULL ,
 "priority" SMALLINT NOT NULL ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "vacationMAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationMAuthority" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."vacationMAuthority_id_seq" OWNED BY "hr"."vacationMAuthority"."pk";
