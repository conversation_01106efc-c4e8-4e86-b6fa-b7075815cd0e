CREATE TABLE "auth"."group_permission" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."group_permission_seq"'::regclass) ,
 "group_id" SMALLINT NOT NULL REFERENCES "auth"."group" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObjects_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "group_permission_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group_permission" ADD CONSTRAINT "group_permission_group_id_appObjects_id_unique" UNIQUE ("group_id", "appObjects_id");
ALTER TABLE "auth"."group_permission" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."group_permission_seq" OWNED BY "auth"."group_permission"."pk";
