CREATE TABLE "machine"."machinelogs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('machine."machinelogs_id_seq"'::regclass) ,
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UP<PERSON>TE CASCADE ON DELETE CASCADE,
 "start_date" TIMESTA<PERSON> WITHOUT TIME ZONE NOT NULL ,
 "end_date" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "records_count" BIGINT NOT NULL ,
 "created" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "notes" TEXT ,
 CONSTRAINT "machinelogs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "machine"."machinelogs" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "machine"."machinelogs_id_seq" OWNED BY "machine"."machinelogs"."pk";
