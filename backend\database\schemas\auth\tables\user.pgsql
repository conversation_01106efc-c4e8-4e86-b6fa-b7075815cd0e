CREATE TABLE "auth"."user" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_id_seq"'::regclass) ,
 "password_" TEXT ,
 "last_login" TIMESTAMP WITH TIME ZONE ,
 "date_joined" TIMESTAMP WITH TIME ZONE DEFAULT now() ,
 "is_active" BOOLEAN NOT NULL DEFAULT 'False'::<PERSON><PERSON><PERSON><PERSON><PERSON> ,
 "mustChangePassword" BOOLEAN DEFAULT 'True'::BO<PERSON><PERSON><PERSON> ,
 "username" VA<PERSON><PERSON><PERSON>(150) NOT NULL UNIQUE ,
 "first_name" <PERSON><PERSON><PERSON><PERSON>(30) ,
 "last_name" VA<PERSON><PERSON><PERSON>(30) ,
 "email" VARCHAR(254) ,
 "phone" VARCHAR(15) ,
 "rolename" NAME UNIQUE ,
 CONSTRAINT "user_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."user_id_seq" OWNED BY "auth"."user"."pk";
