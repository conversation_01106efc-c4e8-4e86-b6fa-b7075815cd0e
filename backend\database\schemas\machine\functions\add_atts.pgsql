CREATE OR R<PERSON>LACE FUNCTION machine.add_atts(
    config jsonb,
    p_records machine.attendance_record_type[] -- Input array of attendance records (without machine_id or is_bulk)
)
RETURNS VOID AS $$
DECLARE
    p_machine_id SMALLINT;
    v_record_count INT;
    MAX_RECORDS_LIMIT INT; -- Define a constant for the maximum allowed records
BEGIN
    -- Get the actual count of records in the input array
    v_record_count := COALESCE(array_length(p_records, 1),0);
    Select machine.f_MAX_ATTS_RECORDS() into MAX_RECORDS_LIMIT;
    p_machine_id := machine.f_get_machine_id(config);

    IF v_record_count = 0 THEN
        RAISE EXCEPTION 'Input array is empty.';
    END IF;

    -- Enforce the limit: if the array exceeds the maximum, raise an exception
    IF v_record_count > MAX_RECORDS_LIMIT THEN
        RAISE EXCEPTION 'Input array exceeds maximum allowed records. Limit is % records.', MAX_RECORDS_LIMIT;
    END IF;

    -- check if machine_id is null 
    IF p_machine_id IS NULL THEN
        RAISE EXCEPTION 'Machine ID cannot be null.';
    END IF;

    -- Perform a batch insert using UNNEST to expand the array into rows
    -- The ON CONFLICT clause handles cases where (att_id, attendance_date) might already exist,
    -- updating the existing record instead of throwing an error.
    -- IMPORTANT: For partitioned tables, the conflict target *must* include the partition key (`attendance_date`).
    INSERT INTO machine.attendance (machine_id, att_id, attendance_date, "attOptions")
    SELECT
        p_machine_id, -- Use the separately passed p_machine_id for all records in this batch
        r.att_id,
        r.attendance_date,
        r.options -- 'options' from record_type maps to "attOptions" in table
    FROM
        UNNEST(p_records) AS r;

    -- NOW, log the bulk operation directly within the function
    -- This calculates min/max attendance_date and count for the specified p_machine_id from the input batch.
    INSERT INTO machine.machinelogs (machine_id, start_date, end_date, records_count, created)
    SELECT
        p_machine_id, -- Use the p_machine_id for the log entry
        MIN(grouped_records.attendance_date) AS min_attendance_date,
        MAX(grouped_records.attendance_date) AS max_attendance_date,
        COUNT(*) AS total_records_for_machine,
        NOW() AS log_created_at
    FROM
        UNNEST(p_records) AS grouped_records
    GROUP BY
        p_machine_id -- Group by the specific machine ID for this batch
    ; 

    RAISE NOTICE 'Successfully inserted/updated % attendance records for machine % via bulk function.', v_record_count, p_machine_id;
END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION machine.add_atts(SMALLINT, machine.attendance_record_type[]) owner to #POSTGRES_DEFAULT_ADMIN#;