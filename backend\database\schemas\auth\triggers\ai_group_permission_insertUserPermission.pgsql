CREATE OR REPLACE FUNCTION auth.ai_group_permission_insertUserPermission()
RETURNS TRIGGER AS $$
BEGIN

  -- Insert into the "group_permission_detail" table
  INSERT INTO auth.user_permission_detail (group_permission_id, permission_name_id)
    SELECT NEW.pk, ap.permission_name_id
    FROM  auth."appObjects_allowed_permission" ap join auth.permission per on ap.permission_name_id = per.name_
    WHERE ap."appObjects_id" = new."appObjects_id"
    order by per.order_; 

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_group_permission_insertUserPermission() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_ai_group_permission_insertUserPermission
AFTER INSERT ON auth.group_permission
FOR EACH ROW
EXECUTE FUNCTION auth.ai_group_permission_insertUserPermission();