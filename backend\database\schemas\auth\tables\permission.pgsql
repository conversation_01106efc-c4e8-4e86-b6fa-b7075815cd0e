CREATE TABLE "auth"."permission" (
 "name_" VARCHAR(10) NOT NULL ,
 "privilege" VARCHAR(10) NOT NULL ,
 "binaryCode" BIT(4) NOT NULL ,
 "order_" SMALLINT ,
 "objectType" auth.objecttype NOT NULL ,
 CONSTRAINT "permission_pkey" PRIMARY KEY ("name_")
);
ALTER TABLE "auth"."permission" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Select', 'Table', 'SELECT', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0010', 'Insert', 'Table', 'INSERT', 2);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0100', 'Update', 'Table', 'UPDATE', 3);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('1000', 'Delete', 'Table', 'DELETE', 4);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'View', 'View', 'SELECT', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0010', 'Append', 'View', 'INSERT', 2);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0100', 'Change', 'View', 'UPDATE', 3);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('1000', 'Remove', 'View', 'DELETE', 4);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Run_P', 'Procedure', 'EXECUTE', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Run_F', 'Function', 'EXECUTE', 1);
