CREATE TABLE "enterprise"."opType_context" (
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "context_" TEXT,
 CONSTRAINT "opType_context_pkey" PRIMARY KEY ("opType_id", "context_")
);
ALTER TABLE "enterprise"."opType_context" OWNER TO #POSTGRES_DEFAULT_ADMIN#;

-- this function needed to be created with table creation
CREATE OR REPLACE FUNCTION enterprise."opType_applicable"(p_type_id SMALLINT, p_ctx TEXT)
  RETURNS BOOLEAN
  LANGUAGE SQL
  STABLE SECURITY INVOKER
AS $$
  SELECT EXISTS (
    SELECT 1
      FROM enterprise."opType_context" oc
     WHERE oc.opType_id = p_type_id
       AND oc.context_  = p_ctx
  );
$$;

INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8001', "exit");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8002', "exit");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8003', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8004', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8005', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8006', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8007', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8008', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8009', "vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8013', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8014', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8015', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8016', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8017', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8018', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8019', "m_vacation");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8101', "decision");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8102', "decision");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8103', "decision");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8104', "decision");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8105', "decision");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8111', "note");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8112', "note");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8113', "note");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8114', "note");
INSERT INTO "enterprise"."opType_context" ("opType_id", "context_") VALUES ('8115', "note");
