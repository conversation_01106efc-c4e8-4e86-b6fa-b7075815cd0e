CREATE TABLE "auth"."userToken" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."userToken_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "revoked" TIMESTAMP WITHOUT TIME ZONE ,
 "expires" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "created" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "token_text" TEXT NOT NULL ,
 CONSTRAINT "userToken_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."userToken" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."userToken_id_seq" OWNED BY "auth"."userToken"."pk";
