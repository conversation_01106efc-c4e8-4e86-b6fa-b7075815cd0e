CREATE TABLE "enterprise"."branch_currencies" (
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "branch_currencies_pkey" PRIMARY KEY ("branch_id", "currency_id")
);
ALTER TABLE "enterprise"."branch_currencies" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
INSERT INTO "enterprise"."branch_currencies" ("branch_id", "currency_id") VALUES (0, 0);
INSERT INTO "enterprise"."branch_currencies" ("branch_id", "currency_id") VALUES (0, 1);
