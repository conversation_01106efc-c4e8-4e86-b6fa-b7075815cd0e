CREATE TABLE "enterprise"."enterprise" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."enterprise_id_seq"'::regclass) ,
 "financial_number" BIGINT UNIQUE ,
 "commercial_register_number" BIGINT UNIQUE ,
 "official_legalform_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "legalform_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "enterprise_accounting_level" BOOLEAN ,
 "social_media" VARCHAR(30)[] ,
 "email" VARCHAR(50) ,
 "TVA_registartion_number" VARCHAR(20) UNIQUE ,
 "phone" VARCHAR(18) ,
 "fax" VARCHAR(18) ,
 "website" VARCHAR(100) ,
 "name_" VARCHAR(200) NOT NULL UNIQUE ,
 "address" TEXT ,
 "logo" BYTEA ,
 CONSTRAINT "enterprise_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."enterprise" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."enterprise_id_seq" OWNED BY "enterprise"."enterprise"."pk";
INSERT INTO "enterprise"."enterprise" ("pk", "official_legalform_id", "legalform_id", "name_") VALUES (0, 0, 0, 'TEST');
