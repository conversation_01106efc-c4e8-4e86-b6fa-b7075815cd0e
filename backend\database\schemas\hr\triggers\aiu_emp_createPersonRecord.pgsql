CREATE OR REPLACE FUNCTION person.aiu_emp_createPersonRecord()
RETURNS TRIGGER AS $$
declare
    new_name varchar(255);
BEGIN
    new_name := COALESCE(NEW.first_name,'') || COALESCE(NEW.father_name,'') || COALESCE(NEW.family_name,'')

  insert into person.person(pk, name_)
  values(new.pk , new_name)
  on conflict(pk)
  do  update
   set name_ = case 
                        when EXISTS ( SELECT 1 FROM person.person WHERE name_ = new_name)
                          then new_name || ' (' || COALESCE(NEW.mother_fullname,'') || ')'
                          else new_name
                      end;
   
  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function person.aiu_emp_createPersonRecord() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_aiu_emp_createPersonRecord
AFTER INSERT OR UPDATE OF first_name, father_name, family_name ON hr.emp
FOR EACH ROW
EXECUTE FUNCTION person.aiu_emp_createPersonRecord();