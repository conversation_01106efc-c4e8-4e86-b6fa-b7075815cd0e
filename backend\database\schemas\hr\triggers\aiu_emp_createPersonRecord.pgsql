CREATE OR REPLACE FUNCTION hr.aiu_emp_createPersonRecord()
RETURNS TRIGGER AS $$
declare
    v_new_name varchar(255);
BEGIN
    v_new_name := COALESCE(NEW.first_name,'') || COALESCE(NEW.father_name,'') || COALESCE(NEW.family_name,'');

  insert into person.person(pk, name_)
  values(new.pk , v_new_name)
  on conflict(pk)
  do  update
   set name_ = case 
                  when EXISTS ( SELECT 1 FROM person.person WHERE name_ = v_new_name)
                       then v_new_name || ' (' || COALESCE(NEW.mother_fullname,'') || ')'
                       else v_new_name
                end;
   
  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function hr.aiu_emp_createPersonRecord() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_aiu_emp_createPersonRecord
AFTER INSERT OR UPDATE OF first_name, father_name, family_name ON hr.emp
FOR EACH ROW
EXECUTE FUNCTION hr.aiu_emp_createPersonRecord();