CREATE OR REPLACE FUNCTION auth.ai_group_permission_detail_grantPrivilege()
RETURNS TRIGGER AS $$
DECLARE
    v_rolename NAME;
    v_privilege TEXT;
    v_sch_name TEXT;
    v_object_name TEXT;
    v_sql_inject TEXT;
BEGIN
     -- Fetch the role name from the group table using the group_id from group_permission
        SELECT g.rolename
        INTO v_rolename
        FROM auth.group g
        INNER JOIN auth.group_permission gp ON g.pk = gp.group_id
        WHERE gp.pk = NEW.group_permission_id;    

    -- Fetch the privilege from the permission table using permission_name_id
        SELECT p.privilege , p."objectType"
        INTO v_privilege , v_sql_inject
        FROM auth.permission p
        WHERE p.name_ = NEW.permission_name_id;

    -- Fetch the schema name from the appObjects table using appObjects_id in group_permission
        SELECT ao.schema_ , ao.name_
        INTO v_sch_name , v_object_name
        FROM auth."appObjects" ao
        INNER JOIN auth.group_permission gp ON ao.pk = gp."appObjects_id"
        WHERE gp.pk = NEW.group_permission_id;

    IF lower(v_sql_inject) = 'view' or lower(v_sql_inject) = 'table' THEN
        v_sql_inject := 'Table' || ' ' || quote_ident(v_sch_name) || '.' || quote_ident(v_object_name);
    ELSE
        v_sql_inject := v_sql_inject || ' ' || quote_ident(v_sch_name) || '.' || quote_ident(substring(v_object_name FROM '^[^(]+')) || substring(v_object_name FROM '\(.*') ;
    END IF;

    IF NEW.allow = true THEN
        IF NEW.fieldlist IS NOT NULL THEN
            EXECUTE 'GRANT ' || v_privilege || ' (' || NEW.fieldlist || ') ON ' ||  v_sql_inject || ' TO ' || v_rolename;
        ELSE
            EXECUTE 'GRANT ' || v_privilege || ' ON ' ||  v_sql_inject || ' TO ' || v_rolename;
        END IF;

        EXECUTE FORMAT('GRANT USAGE ON SCHEMA %I TO %I', v_sch_name, v_rolename);
    END IF;
  
    RETURN NULL;

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_group_permission_detail_grantPrivilege() owner to #POSTGRES_DEFAULT_ADMIN#;
CREATE TRIGGER trg_ai_group_permission_detail_grantPrivilege
after insert ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.ai_group_permission_detail_grantPrivilege();