import argparse
import re
import sys

def normalize_whitespace_in_text(text_content):
    """
    Replaces sequences of two or more space characters with a single space.

    This uses a regular expression for precision. The pattern ' {2,}' specifically
    targets two or more space characters (' '), leaving other whitespace like
    tabs and newlines untouched. This is often safer for structured text or code.

    If you wanted to collapse ALL sequences of whitespace (spaces, tabs, newlines)
    into a single space, you would use:
    # return re.sub(r'\s+', ' ', text_content).strip()

    Args:
        text_content (str): The string to process.

    Returns:
        str: The processed string with normalized whitespace.
    """
    return re.sub(r' {2,}', ' ', text_content)

def process_file(input_path, output_path=None, in_place=False):
    """
    Reads a file, normalizes its whitespace, and writes the result.

    Args:
        input_path (str): The path to the input file.
        output_path (str, optional): The path for the output file. Prints to console if None.
        in_place (bool, optional): If True, modifies the input file directly.
    """
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_path}'", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error reading file '{input_path}': {e}", file=sys.stderr)
        sys.exit(1)

    processed_content = normalize_whitespace_in_text(content)

    if in_place:
        # If in-place, the output path is the same as the input
        write_path = input_path
    else:
        write_path = output_path

    if write_path:
        try:
            with open(write_path, 'w', encoding='utf-8') as f:
                f.write(processed_content)
            print(f"Successfully processed '{input_path}' and saved to '{write_path}'.")
        except Exception as e:
            print(f"Error writing to file '{write_path}': {e}", file=sys.stderr)
            sys.exit(1)
    else:
        # If no write_path, print to standard output
        sys.stdout.write(processed_content)

def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(
        description="A script to replace multiple consecutive spaces with a single space in a text file.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument('input_file', help='The path to the input text file to process.')

    output_group = parser.add_mutually_exclusive_group()
    output_group.add_argument('-o', '--output', dest='output_file', help='The path to save the output file.')
    output_group.add_argument('-i', '--in-place', action='store_true', help='Modify the file in-place.')

    args = parser.parse_args()

    process_file(args.input_file, args.output_file, args.in_place)

if __name__ == "__main__":
    main()