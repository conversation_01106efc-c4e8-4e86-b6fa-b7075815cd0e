CREATE TABLE "auth"."app" (
 "pk" SMALLINT NOT NULL ,
 "appLable" VARCHAR(100) NOT NULL UNIQUE ,
 "default_" BOOLEAN NOT NULL DEFAULT 'False'::B<PERSON><PERSON><PERSON>N ,
 "active_" BOOLEAN NOT NULL DEFAULT 'False'::<PERSON><PERSON><PERSON><PERSON><PERSON> ,
 "personTypeList" SMALLINT[] ,
 CONSTRAINT "app_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."app" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('0', 'all', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('1', 'postgrestApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('2', 'apiApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('3', 'AuthApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('4', 'EnterpriseApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('5', 'PersonApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('6', 'MachineApp', 'FALSE', 'TRUE', '{1,2,3,4,5}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('7', 'HRApp', 'FALSE', 'TRUE', '{1,2,3,4,5,6,7}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('8', 'AccountingApp', 'FALSE', 'FALSE', '{1,2,3,4,5,6,7,8,9,10,11,12,13}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('9', 'CashApp', 'FALSE', 'FALSE', '{1,2,3,4,5,6,7,8,9,10,11,12,13}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('10', 'InventoryApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('11', 'SalesApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('12', 'PurchaseApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('13', 'POSApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('14', 'ServiceApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('15', 'MarketingApp', 'FALSE', 'FALSE', NULL);
