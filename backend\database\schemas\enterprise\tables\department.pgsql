CREATE TABLE "enterprise"."department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."department_id_seq"'::regclass) ,
 "code_" SMALLINT NOT NULL ,
 "level_" SMALLINT NOT NULL ,
 "active_" BOOLEAN DEFAULT 'True'::<PERSON><PERSON><PERSON><PERSON><PERSON> ,
 "parent_id" SMALLINT REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED,
 "prefix" CHARACTER(6) UNIQUE ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 "dep_role" TEXT ,
 CONSTRAINT "department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."department" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."department_id_seq" OWNED BY "enterprise"."department"."pk";
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('1', '0', '0', 'TRUE', NULL, NULL, 'ROOT');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('2', '1', '1', 'TRUE', '1', 'DMA', 'دائرة المعلوماتية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('3', '2', '1', 'TRUE', '1', 'MSI', 'مصلحة الشؤون الادارية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('4', '3', '1', 'TRUE', '1', 'MHN', 'مصلحة الهندسة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('5', '4', '1', 'TRUE', '1', 'MMA', 'المصلحة المالية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('6', '5', '1', 'TRUE', '1', 'DSH', 'دائرة الصحة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('7', '6', '1', 'TRUE', '1', 'SHR', 'الشرطة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('8', '7', '1', 'TRUE', '1', 'DNM', 'دائرة النظافة العامة و المكافحة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('9', '8', '1', 'TRUE', '1', 'MRA', 'المراقبة العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('10', '9', '1', 'TRUE', '1', 'OUT', 'خارج الملاك الاداري');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('11', '10', '1', 'TRUE', '1', 'MIS', 'مكتب استقبال+');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('12', '21', '2', 'TRUE', '3', 'DKK', 'دائرة القضايا القانونية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('13', '22', '2', 'TRUE', '3', 'DTF', 'دائرة التفتيش');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('14', '23', '2', 'TRUE', '3', 'DAM', 'دائرة امانة المجلس البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('15', '24', '2', 'TRUE', '3', 'DSA', 'دائرة الشؤون الادارية و العلاقات العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('16', '30', '2', 'TRUE', '4', 'DIM', 'الدائرة الادارية و المحاسبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('17', '31', '2', 'TRUE', '4', 'DMG', 'دائرة المجارير');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('18', '32', '2', 'TRUE', '4', 'DIS', 'دائرة الاستملاك');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('19', '33', '2', 'TRUE', '4', 'DDT', 'دائرة الدروس و التجميل');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('20', '34', '2', 'TRUE', '4', 'DMB', 'دائرة المباني');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('21', '35', '2', 'TRUE', '4', 'DMR', 'دائرة المراقبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('22', '36', '2', 'TRUE', '4', 'DTN', 'دائرة التنفيذ');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('23', '41', '2', 'TRUE', '5', 'DTH', 'دائرة التحصيل');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('24', '42', '2', 'TRUE', '5', 'KTS', 'قسم التصفية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('25', '43', '2', 'TRUE', '5', 'KKH', 'قسم الخزينة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('26', '44', '2', 'TRUE', '5', 'KMR', 'قسم المحاسبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('27', '45', '2', 'TRUE', '5', 'KSR', 'قسم الصرفيات');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('28', '46', '2', 'TRUE', '5', 'KLW', 'قسم اللوازم');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('29', '47', '2', 'TRUE', '5', 'KAB', 'قسم الاملاك البلدية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('30', '48', '2', 'TRUE', '5', 'DTT', 'دائرة التحقق و تسجيل العقود');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('31', '51', '2', 'TRUE', '6', 'KSI', 'قسم الصحة و الاسعاف');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('32', '52', '2', 'TRUE', '6', 'KHS', 'قسم الهندسة الصحية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('33', '53', '2', 'TRUE', '6', 'KWI', 'قسم الوقاية و الارشاد');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('34', '73', '2', 'TRUE', '8', NULL, 'دائرة النظافة - مراقب +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('35', '74', '2', 'TRUE', '8', NULL, 'براحات +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('36', '75', '2', 'TRUE', '8', NULL, 'دائرة النظافة - عمال +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('37', '241', '3', 'TRUE', '15', 'KDI', 'قسم الديوان');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('38', '242', '3', 'TRUE', '15', 'KMM', 'قسم الموظفين و المحفوظات');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('39', '243', '3', 'TRUE', '15', 'KAA', 'قسم العلاقات العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('40', '244', '3', 'TRUE', '15', 'KMS', 'قسم المركز الثقافي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('41', '331', '3', 'TRUE', '19', 'KDT', 'قسم الدروس و التنظيم و التخطيط');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('42', '332', '3', 'TRUE', '19', 'KSY', 'قسم السير');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('43', '361', '3', 'TRUE', '22', 'KHM', 'قسم الحدائق العامة و الملعب البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('44', '362', '3', 'TRUE', '22', 'KMB', 'قسم المرآب البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('45', '363', '3', 'TRUE', '22', 'KAM', 'قسم الاشغال بالامانة و الاعمال الملزمة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('46', '364', '3', 'TRUE', '22', 'KAI', 'قسم الاعمال الامتيازية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('47', '365', '3', 'TRUE', '22', NULL, 'تنفيذ - الورشة الدائمة +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('48', '366', '3', 'TRUE', '22', NULL, 'تنفيذ - ورشة طوارئ+');
