# Database credentials , required
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aas_db_test
POSTGRES_DEFAULT_ADMIN=postgres
POSTGRES_DEFAULT_PASSWORD=adminpass

DB_ADMIN_USER=admin
DB_ADMIN_PASSWORD=ABmakingdbpass

# suffix this with DB_NAME
DB_USER_ROLES_PREFIX=_usr_
DB_GROUP_ROLES_PREFIX=_gr_

# project features , required
SINGLE_BRANCH=True
SINGLE_DEPARTMENT=False

# for future version
APP_APPLIED_LIST="AuthApp,EnterpriseApp,PersonApp,MachineApp,HRApp"

# needed for PostgREST , Info in tutorial no password required for authenticator user
<PERSON>_ANONYMOUS_USER=anonymous
DB_AUTHENTICATOR_USER=authenticator
DB_AUTHENTICATOR_PASSWORD=UFTQgUis79k0JvLExr59nRksrTcRa2AJ # it may be used to authenticate application with database

DB_CONNECT_TIMEOUT=10
MAX_RETRIES=3
RETRY_DELAY=2