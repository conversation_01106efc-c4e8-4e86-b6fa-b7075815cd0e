CREATE TABLE "machine"."machine" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('machine."machine_id_seq"'::regclass) ,
 "type_id" SMALLINT NOT NULL REFERENCES "machine"."type" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELE<PERSON> CASCADE,
 "branch_id" SMALLINT ,
 "recordsCount" BIGINT ,
 "lastRead" TIMESTAMP WITHOUT TIME ZONE ,
 "name_" VARCHAR(25) NOT NULL UNIQUE ,
 "conf" JSONB UNIQUE ,
 CONSTRAINT "machine_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "machine"."machine" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "machine"."machine_id_seq" OWNED BY "machine"."machine"."pk";
