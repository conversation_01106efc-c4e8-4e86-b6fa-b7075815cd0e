create or replace function auth.bi_group_createGroupRole() returns trigger as $$
declare
    v_group_name name;
begin

  IF new.app_id = 0 THEN
    RAISE EXCEPTION 'Invalid input for app id';
    return null;
  END IF;  
  
  IF NEW.rolename IS NULL THEN
    -- generate a group name based on name_
    v_group_name := current_setting('db.group_prefix') || quote_ident(new.name_);
    -- create the group
    execute 'create role '|| quote_ident(v_group_name);
    new.rolename = v_group_name;
  END IF;

  return new;

end;
$$ language plpgsql;

alter function auth.bi_group_createGroupRole() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_bi_bi_group_createGroupRole
  before insert on auth.group
  for each row
  execute procedure auth.bi_group_createGroupRole();