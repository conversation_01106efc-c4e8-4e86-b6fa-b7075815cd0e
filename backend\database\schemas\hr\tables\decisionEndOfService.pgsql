CREATE TABLE "hr"."decisionEndOfService" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionEndOfService_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL UNIQUE REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 CONSTRAINT "decisionEndOfService_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionEndOfService" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."decisionEndOfService_id_seq" OWNED BY "hr"."decisionEndOfService"."pk";
