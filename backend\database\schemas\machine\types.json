{"Composites": [{"name": "zkrecord", "fields": [{"name": "uid", "type": "integer"}, {"name": "user_id", "type": "text"}, {"name": "timestamp", "type": "timestamp without time zone"}, {"name": "status", "type": "integer"}, {"name": "punch", "type": "integer"}]}, {"name": "attendance_record_type", "fields": [{"name": "att_id", "type": "bigint"}, {"name": "attendance_date", "type": "TIMESTAMP WITHOUT TIME ZONE"}, {"name": "options", "type": "JSONB"}]}]}