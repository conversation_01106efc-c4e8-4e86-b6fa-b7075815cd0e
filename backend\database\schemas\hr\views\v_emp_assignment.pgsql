CREATE VIEW hr.v_emp_assignment
WITH (security_barrier = true,  security_invoker = true) AS

Select
  vemp.pk as emp_id,
  vemp.empfullname as emp_name,
  vemp.active_ as active_,

  ee.emp_persontype_id as emp_type_id,
  ee.description as emp_type_description,

  ot.name_ as decision_type,

  d.date_ as decision_date,
  d.branch_id as decision_branch_id,
  d.ref_ as decision_ref,

  old_bdp.branch_name as source_branch_name,
  old_bdp.department_id as source_department_id,
  old_bdp.department_name as source_department_name,
  old_bdp.post_id as source_post_id,
  old_bdp.post_name as source_post_name,

  new_bdp.department_id as target_department_id,
  new_bdp.department_name as target_department_name,
  new_bdp.post_id as target_post_id,
  new_bdp.post_name as target_post_name,

  demp.old_ops -- 3 status 
  
From hr.v_emp vemp 
  inner join hr."decisionAssignment" demp on vemp.pk = demp.emp_id
  inner join hr.decision d on demp.decision_id = d.pk
  inner join enterprise."opTypes" ot on d."opType_id" = ot.pk
  inner join enterprise.v_enterprise_branch_department_posts old_bdp on 
    d.branch_id = old_bdp.branch_id and demp.old_department_id = old_bdp.department_id and demp.old_post_id = old_bdp.post_id

  inner join enterprise.v_enterprise_branch_department_posts new_bdp on 
    d.branch_id = new_bdp.branch_id  and demp.new_department_id = new_bdp.department_id and demp.new_post_id = new_bdp.post_id

  inner join hr.v_emp_employment ee on vemp.pk = ee.emp_id;

ALTER TABLE hr.v_emp_assignment owner to #POSTGRES_DEFAULT_ADMIN#;