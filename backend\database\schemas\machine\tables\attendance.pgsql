CREATE TABLE "machine"."attendance" (
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "att_id" BIGINT ,
 "attendance_date" TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE ,
 "created" TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "attOptions" JSONB ,
 CONSTRAINT "attendance_pkey" PRIMARY KEY ("att_id", "attendance_date")
) PARTITION BY RANGE (attendance_date);
ALTER TABLE "machine"."attendance" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
