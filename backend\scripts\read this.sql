-- Filename: flexible_doctype_schema.sql
-- Description: Flexible Context-Driven DocType Model with Role-Grouping, Caching, RLS, Audit Trails & Versioning

-- ===================================================================
-- 1. Core Schema
-- ===================================================================

-- 1.1 Document Type Definitions
CREATE TABLE docs_type (
  id          SERIAL    PRIMARY KEY,
  name        TEXT      NOT NULL UNIQUE,
  description TEXT
);

-- 1.2 Map Doc Types to Contexts
CREATE TABLE docs_type_context (
  docs_type_id INTEGER REFERENCES docs_type(id) ON DELETE CASCADE,
  context      TEXT    NOT NULL,          -- e.g. 'docs', 'exit_docs', 'invoices'
  PRIMARY KEY (docs_type_id, context)
);

-- 1.3 User-Specific DocType Permissions
CREATE TYPE doc_operation AS ENUM ('view', 'create', 'edit', 'delete');

CREATE TABLE user_docs_type (
  user_id       INTEGER NOT NULL,                               -- references users(id)
  docs_type_id  INTEGER NOT NULL REFERENCES docs_type(id) ON DELETE CASCADE,
  operation     doc_operation NOT NULL,
  PRIMARY KEY (user_id, docs_type_id, operation)
);

-- ===================================================================
-- 2. Context-Validation Function
-- ===================================================================
CREATE FUNCTION docs_type_applicable(p_type_id INTEGER, p_ctx TEXT)
  RETURNS BOOLEAN
  LANGUAGE SQL
  STABLE
AS $$
  SELECT EXISTS (
    SELECT 1
      FROM docs_type_context
     WHERE docs_type_id = p_type_id
       AND context       = p_ctx
  );
$$;

-- ===================================================================
-- 3. Sample Context Tables
-- ===================================================================

-- 3.1 docs
CREATE TABLE docs (
  id          SERIAL    PRIMARY KEY,
  doc_type_id INTEGER   NOT NULL REFERENCES docs_type(id) ON DELETE RESTRICT
               CHECK (docs_type_applicable(doc_type_id, 'docs')),
  title       TEXT      NOT NULL,
  content     TEXT,
  created_by  INTEGER   NOT NULL  -- references users(id)
);

-- 3.2 exit_docs
CREATE TABLE exit_docs (
  id          SERIAL    PRIMARY KEY,
  doc_type_id INTEGER   NOT NULL REFERENCES docs_type(id) ON DELETE RESTRICT
               CHECK (docs_type_applicable(doc_type_id, 'exit_docs')),
  employee_id INTEGER   NOT NULL,  -- references employees(id)
  exit_date   DATE      NOT NULL
);

-- 3.3 invoices
CREATE TABLE invoices (
  id          SERIAL    PRIMARY KEY,
  doc_type_id INTEGER   NOT NULL REFERENCES docs_type(id) ON DELETE RESTRICT
               CHECK (docs_type_applicable(doc_type_id, 'invoices')),
  customer_id INTEGER   NOT NULL,  -- references customers(id)
  amount      NUMERIC   NOT NULL
);



-- ===================================================================
-- 4. Role-Grouping Enhancement
-- ===================================================================

-- 4.1 Roles and User-Roles
CREATE TABLE roles (
  id   SERIAL PRIMARY KEY,
  name TEXT   NOT NULL UNIQUE
);

CREATE TABLE user_roles (
  user_id INTEGER NOT NULL,              -- references users(id)
  role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  PRIMARY KEY (user_id, role_id)
);

-- 4.2 Role-Based DocType Permissions
CREATE TABLE role_docs_type (
  role_id       INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  docs_type_id  INTEGER NOT NULL REFERENCES docs_type(id) ON DELETE CASCADE,
  operation     doc_operation NOT NULL,
  PRIMARY KEY (role_id, docs_type_id, operation)
);



-- ===================================================================
-- 5. Caching Enhancement (Materialized View)
-- ===================================================================

CREATE MATERIALIZED VIEW user_context_types AS
SELECT ud.user_id,
       c.context,
       ud.docs_type_id
  FROM user_docs_type ud
  JOIN docs_type_context c ON c.docs_type_id = ud.docs_type_id

UNION

SELECT ur.user_id,
       c.context,
       rdt.docs_type_id
  FROM user_roles ur
  JOIN role_docs_type rdt      ON rdt.role_id = ur.role_id
  JOIN docs_type_context c     ON c.docs_type_id = rdt.docs_type_id
;

-- Refresh function and scheduling (requires pg_cron or external cron)
CREATE FUNCTION refresh_user_context_types()
  RETURNS VOID
  LANGUAGE SQL
AS $$
  REFRESH MATERIALIZED VIEW user_context_types;
$$;

-- Example pg_cron job (uncomment and adjust schedule as needed):
-- SELECT cron.schedule('refresh_user_context_every_5min',
--   '*/5 * * * *', $$SELECT refresh_user_context_types()$$);



-- ===================================================================
-- 6. Row-Level Security (RLS)
-- ===================================================================

-- Enable RLS on each context table
ALTER TABLE docs      ENABLE ROW LEVEL SECURITY;
ALTER TABLE exit_docs ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices  ENABLE ROW LEVEL SECURITY;

-- Policy helper: checks rights in materialized view
-- 6.1 SELECT Policy for docs
CREATE POLICY docs_select ON docs
  FOR SELECT USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'docs'
         AND uct.docs_type_id = docs.doc_type_id
    )
  );

-- 6.2 MOD (INSERT/UPDATE/DELETE) Policy for docs
CREATE POLICY docs_mod ON docs
  FOR ALL USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'docs'
         AND uct.docs_type_id = docs.doc_type_id
         AND uct.operation    IN (
           CASE WHEN (TG_OP = 'INSERT') THEN 'create'
                WHEN (TG_OP = 'UPDATE') THEN 'edit'
                WHEN (TG_OP = 'DELETE') THEN 'delete'
           END
         )
    )
  );

-- Repeat policies for exit_docs
CREATE POLICY exit_docs_select ON exit_docs
  FOR SELECT USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'exit_docs'
         AND uct.docs_type_id = exit_docs.doc_type_id
    )
  );

CREATE POLICY exit_docs_mod ON exit_docs
  FOR ALL USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'exit_docs'
         AND uct.docs_type_id = exit_docs.doc_type_id
         AND uct.operation    IN (
           CASE WHEN (TG_OP = 'INSERT') THEN 'create'
                WHEN (TG_OP = 'UPDATE') THEN 'edit'
                WHEN (TG_OP = 'DELETE') THEN 'delete'
           END
         )
    )
  );

-- Repeat policies for invoices
CREATE POLICY invoices_select ON invoices
  FOR SELECT USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'invoices'
         AND uct.docs_type_id = invoices.doc_type_id
    )
  );

CREATE POLICY invoices_mod ON invoices
  FOR ALL USING (
    EXISTS (
      SELECT 1
        FROM user_context_types uct
       WHERE uct.user_id      = current_setting('app.current_user')::INT
         AND uct.context      = 'invoices'
         AND uct.docs_type_id = invoices.doc_type_id
         AND uct.operation    IN (
           CASE WHEN (TG_OP = 'INSERT') THEN 'create'
                WHEN (TG_OP = 'UPDATE') THEN 'edit'
                WHEN (TG_OP = 'DELETE') THEN 'delete'
           END
         )
    )
  );



-- ===================================================================
-- 7. Audit Trails
-- ===================================================================

-- 7.1 Event Log Table
CREATE TABLE event_log (
  id          SERIAL       PRIMARY KEY,
  context     TEXT         NOT NULL,
  doc_id      INTEGER      NOT NULL,
  user_id     INTEGER      NOT NULL,
  operation   doc_operation NOT NULL,
  changed_at  TIMESTAMPTZ  NOT NULL DEFAULT now(),
  old_value   JSONB,
  new_value   JSONB
);

-- 7.2 Audit Trigger Function
CREATE FUNCTION log_event() RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  INSERT INTO event_log(context, doc_id, user_id, operation, old_value, new_value)
  VALUES (
    TG_TABLE_NAME,
    COALESCE(OLD.id, NEW.id),
    current_setting('app.current_user')::INT,
    TG_OP::doc_operation,
    to_jsonb(OLD),
    to_jsonb(NEW)
  );
  RETURN NEW;
END;
$$;

-- 7.3 Attach Audit Triggers Dynamically
DO $$
DECLARE
  tbl TEXT;
BEGIN
  FOR tbl IN ARRAY['docs','exit_docs','invoices'] LOOP
    EXECUTE format($f$
      CREATE TRIGGER %1$:_audit
      AFTER INSERT OR UPDATE OR DELETE ON %1$I
      FOR EACH ROW EXECUTE FUNCTION log_event();
    $f$, tbl);
  END LOOP;
END;
$$;



-- ===================================================================
-- 8. Versioning
-- ===================================================================

-- 8.1 History Tables
CREATE TABLE docs_history (
  history_id  SERIAL      PRIMARY KEY,
  valid_from  TIMESTAMPTZ NOT NULL DEFAULT now(),
  valid_to    TIMESTAMPTZ,
  id          INTEGER,
  doc_type_id INTEGER,
  title       TEXT,
  content     TEXT,
  created_by  INTEGER
);

CREATE TABLE exit_docs_history (
  history_id  SERIAL      PRIMARY KEY,
  valid_from  TIMESTAMPTZ NOT NULL DEFAULT now(),
  valid_to    TIMESTAMPTZ,
  id          INTEGER,
  doc_type_id INTEGER,
  employee_id INTEGER,
  exit_date   DATE
);

CREATE TABLE invoices_history (
  history_id  SERIAL      PRIMARY KEY,
  valid_from  TIMESTAMPTZ NOT NULL DEFAULT now(),
  valid_to    TIMESTAMPTZ,
  id          INTEGER,
  doc_type_id INTEGER,
  customer_id INTEGER,
  amount      NUMERIC
);

-- 8.2 Versioning Trigger Function
CREATE FUNCTION versioning() RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
    INSERT INTO (
      CASE TG_TABLE_NAME
        WHEN 'docs'       THEN docs_history
        WHEN 'exit_docs'  THEN exit_docs_history
        WHEN 'invoices'   THEN invoices_history
      END
    )
    VALUES (
      now(),
      NULL,
      OLD.*
    );
  END IF;

  IF TG_OP = 'UPDATE' THEN
    UPDATE (
      CASE TG_TABLE_NAME
        WHEN 'docs'       THEN docs_history
        WHEN 'exit_docs'  THEN exit_docs_history
        WHEN 'invoices'   THEN invoices_history
      END
    )
       SET valid_to = now()
     WHERE history_id = (
       SELECT max(history_id)
         FROM (
           SELECT * FROM docs_history      WHERE id = OLD.id AND valid_to IS NULL
           UNION ALL
           SELECT * FROM exit_docs_history WHERE id = OLD.id AND valid_to IS NULL
           UNION ALL
           SELECT * FROM invoices_history  WHERE id = OLD.id AND valid_to IS NULL
         ) x
     );
  END IF;

  RETURN NEW;
END;
$$;

-- 8.3 Attach Versioning Triggers
DO $$
DECLARE
  tbl TEXT;
BEGIN
  FOR tbl IN ARRAY['docs','exit_docs','invoices'] LOOP
    EXECUTE format($v$
      CREATE TRIGGER %1$:_versioning
      BEFORE UPDATE OR DELETE ON %1$I
      FOR EACH ROW EXECUTE FUNCTION versioning();
    $v$, tbl);
  END LOOP;
END;
$$;

-- End of flexible_doctype_schema.sql
