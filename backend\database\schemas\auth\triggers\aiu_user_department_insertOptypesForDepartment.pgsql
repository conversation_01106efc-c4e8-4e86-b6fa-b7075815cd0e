CREATE OR REPLACE FUNCTION auth.aiu_user_department_insertOptypesForDepartment()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_id smallint;
BEGIN

  IF TG_OP = 'UPDATE' THEN
    -- if allow changed , update group_department_optypes table to set allow = false
    IF NEW.allow = false and OLD.allow = true and NEW.department_id = OLD.department_id THEN
      UPDATE auth.user_department_optypes SET allow = false
      WHERE group_department_id = OLD.pk;
    END IF;

    IF NEW.department_id <> OLD.department_id THEN
      -- if department changed, delete from group_department table and insert new data
      DELETE FROM auth.group_department_optypes
      WHERE group_department_id = OLD.pk;
    END IF;

  END IF;

  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.department_id <> OLD.department_id) THEN
    -- get branch_id from group_branch table
    SELECT branch_id INTO v_branch_id FROM auth.user_branch WHERE pk = NEW.user_branch_id;

    INSERT INTO auth.user_department_optypes (user_department_id, optype_id, allow)
      SELECT DISTINCT NEW.pk, optype_id, NEW.allow
      FROM enterprise.v_branch_department_optypes
      WHERE branch_id = v_branch_id AND department_id = NEW.department_id;
  END IF;

  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function auth.aiu_user_department_insertOptypesForDepartment owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_aiu_user_department_insertOptypesForDepartment
AFTER INSERT OR UPDATE OF department_id, allow ON auth.user_department
FOR EACH ROW
EXECUTE FUNCTION auth.aiu_user_department_insertOptypesForDepartment();
