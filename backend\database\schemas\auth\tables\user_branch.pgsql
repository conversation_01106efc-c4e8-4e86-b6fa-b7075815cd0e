CREATE TABLE "auth"."user_branch" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_branch_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "user_branch_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_branch" ADD CONSTRAINT "user_branch_user_id_branch_id_unique" UNIQUE ("user_id", "branch_id");
ALTER TABLE "auth"."user_branch" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
