[{"table_name": "appObjects", "seq_name": "appObjects_id_seq", "seq_type": "SMALLINT", "primary_key": "pk"}, {"table_name": "group", "seq_name": "group_id_seq", "seq_type": "SMALLINT", "primary_key": "pk"}, {"table_name": "group_permission", "seq_name": "group_permission_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "user", "seq_name": "user_id_seq", "seq_type": "SMALLINT", "primary_key": "pk"}, {"table_name": "userLog", "seq_name": "userLog_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "userToken", "seq_name": "userToken_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "user_permission", "seq_name": "user_permission_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "group_permission_detail", "seq_name": "group_permission_detail_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "user_department", "seq_name": "user_department_id_seq", "seq_type": "SMALLINT", "primary_key": "pk"}, {"table_name": "user_branch", "seq_name": "user_branch_id_seq", "seq_type": "SMALLINT", "primary_key": "pk"}, {"table_name": "user_permission_detail", "seq_name": "user_permission_detail_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "audit_log", "seq_name": "audit_log_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "audit_user", "seq_name": "audit_user_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}]