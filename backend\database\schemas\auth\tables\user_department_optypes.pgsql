CREATE TABLE "auth"."user_department_optypes" (
 "user_department_id" SMALLINT NOT NULL REFERENCES "auth"."user_department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "optype_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "user_department_optypes_pkey" PRIMARY KEY ("user_department_id", "optype_id")
);
ALTER TABLE "auth"."user_department_optypes" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
