CREATE TABLE "enterprise"."chartOfAccountRef" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."chartOfAccountRef_id_seq"'::regclass) ,
 "accountCategory_id" SMALLINT NOT NULL REFERENCES "enterprise"."accountCategory" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "num_" VARCHAR(6) NOT NULL ,
 "description" VARCHAR(250) NOT NULL ,
 CONSTRAINT "chartOfAccountRef_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."chartOfAccountRef" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."chartOfAccountRef_id_seq" OWNED BY "enterprise"."chartOfAccountRef"."pk";
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('1', '1', '1', 'الفئة الاولى - حسابات الرساميل الدائمة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('2', '1', '10', '                       - رأس المال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('3', '1', '101', '                      رأس المال (للشركة او للشخص)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('4', '1', '1011', '                     رأس المال المكتتب غير المستدعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('5', '1', '1012', '                     رأس المال المكتتب, المستدعي غير المدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('6', '1', '1013', '                     رأس المال المكتتب, المستدعي والمدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('7', '1', '102', '                      علاوات الاصدار والاندماج والمقدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('8', '1', '1021', '                     علاوات الاصدار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('9', '1', '1022', '                     علاوات الاندماج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('10', '1', '1023', '                     علاوات المقدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('11', '1', '1024', '                     علاوات تحويل السندات الى اسهم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('12', '1', '103', '                      فروقات اعادة التخمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('13', '1', '1031', '       فروقات اعادة تخمين الاصول الثابتة غير القابلة للاستهلاك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('14', '1', '1035', '                     فروقات اعادة تخمين الاصول الثابتة القابلة للاستهلاك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('15', '1', '109', '                      الحساب الشخصي لصاحب المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('16', '1', '11', '                       - الاحتياطات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('17', '1', '111', '                      احتياطي قانوني');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('18', '1', '112', '                      احتياطيات نظامية وتعاقدية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('19', '1', '119', '                      احتياطيات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('20', '1', '12', '                       - نتائج سابقة مدورة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('21', '1', '121', '                      نتائج سابقة مدورة دائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('22', '1', '125', '                      نتائج سابقة مدورة مدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('23', '1', '13', '                        - النتيجة الصافية للدورة المالية (ربح او خسارة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('24', '1', '131', '                       الهامش التجاري القائم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('25', '1', '132', '                       القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('26', '1', '133', '                       الفائض غير الصافي للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('27', '1', '134', '                       نتيجة الاستثمار (خارج الاعباء والايرادات المالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('28', '1', '135', '                       النتيجة الجارية قبل الضريبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('29', '1', '136', '                       النتيجة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('30', '1', '138', '                       نتيجة الدورة - ارباح');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('31', '1', '139', '                       نتيجة الدوره - خسائر');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('32', '1', '14', '                        - اعانات للتوظيفات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('33', '1', '141', '                       اعانات للتوظيفات مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('34', '1', '145', '                       اعانات للتوظيفات محولة للنتائج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('35', '1', '15', '                        - مؤونات لمواجهة اخطار واعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('36', '1', '151', '                       مؤونات لمواجهة اخطار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('37', '1', '1511', '                      مؤونات للمنازعات والاحتمالات المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('38', '1', '1512', '                      مؤونات لقاء الضمانات المعطاة للزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('39', '1', '1513', '                      مؤونات خسائر سعر الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('40', '1', '1514', '                      مؤونات خسائر على عقود لاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('41', '1', '1515', '                      مؤونات الغرامات والجزاءات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('42', '1', '155', '                       مؤونات لمواجهة اعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('43', '1', '1551', '                      مؤونات الاعباء الواجب توزيعها على عدة دورات مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('44', '1', '1552', '                      مؤونات لمواجهة معاشات التقاعد والموجبات المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('45', '1', '1553', '                      مؤونات للضرائب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('46', '1', '16', '                        - دوين مالية طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('47', '1', '161', '                       قروض لقاء سندات دين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('48', '1', '162', '                       قروض من مؤسسات التسليف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('49', '1', '168', '                       قروض وديون مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('50', '1', '1681', '                      اوراق دفع ناجمة عن شراء المؤسسة التجارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('51', '1', '1682', '                      ودائع وكفالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('52', '1', '1683', '                      سلفات  الدولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('53', '1', '1684', '                      دخل لمدى الحيارة متراكم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('54', '1', '1689', '                      ديون اخرى طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('55', '1', '18', '                        -حسابات ارتباط المؤسسات والفروع والشركات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('56', '1', '181', '                       حسابات ارتباط المؤسسات والفروع وشركات المشاركة(حساب لكل مؤسسة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('57', '1', '1811', '                      رصيد مدور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('58', '1', '1815', '                      حركات الدورة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('59', '1', '186', '                       الاموال والخدمات المتبادلة بين الفروع ( اعباء )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('60', '1', '187', '                       الاموال والخدمات المتبادلة بين الفروع ( ايرادات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('61', '1', '19', '                       - حسابات تجميع الاعباء والايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('62', '1', '191', '                      تحديد الهامش التجاري القائم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('63', '1', '192', '                      تحديد القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('64', '1', '193', '                      تحديد الفائض غير الصافي للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('65', '1', '194', '                      تحديد نتيجة الاستثمار (قبل الاعباء والايرادات المالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('66', '1', '195', '                      تحديد النتيجة الجارية قبل الضريبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('67', '1', '196', '                      تحديد النتيجة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('68', '1', '197', '                      تحديد نتيجة الدورة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('69', '2', '2', '               الفئة الثانية - حسابات الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('70', '2', '21', '                       - الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('71', '2', '211', '                      المؤسسة التجارية ( الخلو , الشهرة,  الزبائن...)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('72', '2', '212', '                      مصاريف التأسيس');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('73', '2', '213', '                      مصاريف البحوث والتطوير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('74', '2', '214', '                      براءات الاختراع - الاجازات  العلامات والقيم المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('75', '2', '219', '                      اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('76', '2', '2191', '               ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('77', '2', '2198', '                     سلفات ودفعات على حساب تقديم   اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('78', '2', '22', '                       - الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('79', '2', '221', '                      الاراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('80', '2', '2211', '                     الاراضي الفراغ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('81', '2', '2212', '                     الاراضي المبنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('82', '2', '2213', '                     الاراضي برسم الاستثمار الجوفي ( مناجم - مقالع.. )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('83', '2', '2214', '                     استصلاح وتنظيم الاراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('84', '2', '223', '                      الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('85', '2', '2231', '                     الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('86', '2', '2232', '                     التجهيزات العامة - استصلاح وتنظيم الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('87', '2', '2233', 'انشاءات البنية التحتية (سدود , مدارج المطارات ... )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('88', '2', '2234', '                     انشاءات على اراضي الغير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('89', '2', '224', '                      التجهيزات الفنية, والالات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('90', '2', '2241', '                     تجهيزات متخصصة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('91', '2', '2242', '                     تجهيزات ذات طبيعة خاصة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('92', '2', '2243', '                     المعدات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('93', '2', '2244', '                     الادوات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('94', '2', '225', '                      اليات النقل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('95', '2', '226', '                      اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('96', '2', '2261', '                     تجهيزات عامة, استصلاحات وتحسينات مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('97', '2', '2262', '                     ادوات مكتبية ومعلوماتية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('98', '2', '2263', '                     اثاث');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('99', '2', '2264', '                     استثمارات زراعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('100', '2', '2265', '                     عبوات قابلة لاعادة الاستعمال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('101', '2', '227', '                      اصول ثابتة مادية قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('102', '2', '2271', '                     اراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('103', '2', '2273', '                     ابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('104', '2', '2274', '                     تجهيزات فنية, معدات وادوات صناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('105', '2', '2276', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('106', '2', '228', '                      سلفات ودفعات على حساب  شراء اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('107', '2', '25', '                       - الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('108', '2', '251', '                      سندات مشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('109', '2', '252', '                      ذمم مدينة مرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('110', '2', '253', '                      سندات اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('111', '2', '2531', '                     سندات ملكية ( اسهم, حصص  شراكة )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('112', '2', '2535', '                     سندات دين (سندات, قسائم )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('113', '2', '255', '                      قروض طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('114', '2', '2551', '                     قروض للشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('115', '2', '2552', '                     قروض للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('116', '2', '2558', '                     قروض اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('117', '2', '259', '                      ذمم مدينة اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('118', '2', '28', '                       استهلاكات الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('119', '2', '280', '                      استهلاكات المؤسسة التجارية(الشهرة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('120', '2', '281', '                      استهلاكات الاصول الثابتة غير المادية الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('121', '2', '2811', '                     مصاريف التأسيس');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('122', '2', '2812', '                     مصاريف البحوث والتطوير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('123', '2', '2813', '                     براءات الاختراع - الاجازات و القيم المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('124', '2', '2819', '                     اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('125', '2', '282', '                      استهلاكات الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('126', '2', '2821', '                     اراضي برسم الاستثمار الجوفي ( مناجم - مقالع... )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('127', '2', '2823', '                     الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('128', '2', '2824', '                     التجهيزات الفنية المعدات والادوات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('129', '2', '2825', '                     آليات النقل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('130', '2', '2826', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('131', '2', '29', '                       مؤونات هبوط اسعار الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('132', '2', '290', '                      مؤونات هبوط قيمة (المؤسسة التجارية )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('133', '2', '291', '                      مؤونات هبوط قيم الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('134', '2', '2911', '                     علامات وقيم مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('135', '2', '2919', '                     اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('136', '2', '292', '                      مؤونات هبوط اسعار الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('137', '2', '2921', '                     الاراضي (غير اراضي الاستثمار الجوفي )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('138', '2', '2926', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('139', '2', '295', '                      مؤونات هبوط اسعار الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('140', '2', '2951', '                     سندات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('141', '2', '2952', '                     ذمم مدينة مرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('142', '2', '2953', '                     سندات اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('143', '2', '2955', '                     قروض ممنوحة طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('144', '2', '2959', '                     ذمم مدينة اخرى مجمده');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('145', '3', '3', '                   الفئة الثالثة - المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('146', '3', '31', '                       مواد اولية او استهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('147', '3', '311', '                      مواد اولية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('148', '3', '315', '                      مواد ولوازم استهلاكية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('149', '3', '33', '                       قيد الصنع (سلغ واشغال وخدمات )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('150', '3', '331', '                      منتجات قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('151', '3', '332', '                      اشغال قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('152', '3', '335', '                      دراسات قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('153', '3', '336', '                      خدمات قيد التقديم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('154', '3', '35', '                       منتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('155', '3', '351', '                      منتجات وسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('156', '3', '355', '                      منتجات تامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('157', '3', '358', '                      فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('158', '3', '37', '                       البضائع (المعدة للبيع)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('159', '3', '371', '                      ....................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('160', '3', '372', '                      ....................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('161', '3', '39', '                       مؤونات هبوط اسعار المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('162', '3', '391', '                      مؤونات هبوط اسعار مخزون المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('163', '3', '393', '                      مؤونات هبوط اسعار الانتاج قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('164', '3', '395', '                      مؤونات هبوط اسعار الانتاج المخزون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('165', '3', '397', '                      مؤونات هبوط اسعار البضاعة المخزونة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('166', '4', '4', 'الفئة الرابعة - حسابات الذمم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('167', '4', '40', '                       الموردون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('168', '4', '401', '                      ذمم دائنة (موردو الاستثمار)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('169', '4', '4011', '                     فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('170', '4', '4015', '                     اوراق دفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('171', '4', '4018', '                     فواتير لم تصل بعد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('172', '4', '4019', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('173', '4', '403', '                      موردو الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('174', '4', '4031', '                     فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('175', '4', '4035', '                     اوراق دفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('176', '4', '4038', '                     فواتير لم تصل بعد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('177', '4', '4039', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('178', '4', '409', '                      سلفات ودفعات على حساب طلبيات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('179', '4', '41', '                       الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('180', '4', '411', '                      فواتير الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('181', '4', '4111', '                     زبائن عاديون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('182', '4', '4115', '                     زبائن مشكوك بتحصيل ديونهم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('183', '4', '4119', '                     حسومات ممنوحة من المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('184', '4', '413', '                      اوراق قبض - زبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('185', '4', '415', '                      ذمم مدينة على اشغال لم تبلغ مرحلة تحرير فواتير بها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('186', '4', '418', '                      فواتير قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('187', '4', '419', '                      سلفات ومقبوضات على حساب طلبيات قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('188', '4', '42', '                       المستخدمون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('189', '4', '421', '                      مدفوعات متوجبة للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('190', '4', '428', '                      حسابات المستخدمين المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('191', '4', '4281', '                     سلفات ودفعات للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('192', '4', '4282', '                     حجوزات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('193', '4', '43', '                       مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('194', '4', '431', '                      ذمم دائنة تجاه مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('195', '4', '4311', '                     تقديمات برسم الدفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('196', '4', '4315', '                     اوراق الدفع - مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('197', '4', '4318', '                     اعباء يجب لحظها - مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('198', '4', '438', '                      ذمم مدينة على مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('199', '4', '44', '                       الدولة والمؤسسات العامة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('200', '4', '441', '                      ضرائب متوجبة على الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('201', '4', '4411', '                     ضرائب ورسوم متوجبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('202', '4', '4415', '                     اوراق دفع - ضرائب ورسوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('203', '4', '4418', '                    - اعباء يجب لحظها - ضرائب ورسوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('204', '4', '442', 'الدولة والمؤسسات العامة - الضريبة على القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('205', '4', '4421', 'الضريبة على القيمة المضافة المدفوعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('206', '4', '44210', 'الضريبة على القيمة المضافة المدفوعة على المشتريات المستعملة فقط لعمليات تتيح حق الحسم.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('207', '4', '44211', 'الضريبة على القيمة المضافة المدفوعة على المشتريات المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('208', '4', '44212', 'الضريبة على القيمة المضافة المدفوعة على المشتريات التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('209', '4', '44213', 'الضريبة على القيمة المضافة المدفوعة على مشتريات الأصول الثابتة المستعملة فقط لعميات تتيح حق الحسم.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('210', '4', '44214', 'الضريبة على القيمة المضافة المدفوعة على مشتريات الأصول الثابتة المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('211', '4', '44215', 'الضريبة على القيمة المضافة المدفوعة على الأصول الثابتة التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('212', '4', '44216', 'الضريبة على القيمة المضافة المدفوعة على الأعباء المستعملة فقط لعمليات تتيح حق الحسم. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('213', '4', '44217', 'الضريبة على القيمة المضافة المدفوعة على الأعباء المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('214', '4', '44218', 'الضريبة على القيمة المضافة المدفوعة على الأعباء التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('215', '4', '44219', 'الضريبة على القيمة المضافة المدفوعة على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('216', '4', '4422', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('217', '4', '44221', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) على المشتريات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('218', '4', '44222', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على المشتريات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('219', '4', '44223', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) مشتريات الأصول الثابتة. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('220', '4', '44224', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على مشتريات الأصول الثابتة. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('221', '4', '44225', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) على الأعباء. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('222', '4', '44226', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('223', '4', '4425', 'رصيد الضريبة على القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('224', '4', '44251', 'الضريبة على القيمة المضافة المستحقة للدفع.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('225', '4', '44252', 'الضريبة على القيمة المضافة المدورة القابلة للاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('226', '4', '4426', 'الضريبة على القيمة المضافة القابلة للحسم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('227', '4', '44261', 'الضريبة على القيمة المضافة القابلة للحسم على المشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('228', '4', '44262', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على المشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('229', '4', '44263', 'الضريبة على القيمة المضافة القابلة للحسم على مشتريات الأصول الثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('230', '4', '44264', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على مشتريات الأصول الثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('231', '4', '44265', 'الضريبة على القيمة المضافة القابلة للحسم على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('232', '4', '44266', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('233', '4', '44267', 'الضريبة على القيمة المضافة القابلة للحسم على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('234', '4', '44268', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('235', '4', '4427', 'الضريبة على القيمة المضافة المحصلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('236', '4', '44270', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('237', '4', '44271', 'الضريبة على القيمة المضافة المحصلة على عمليات تقديم الخدمات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('238', '4', '44272', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال على الأسس النقدية. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('239', '4', '44273', 'الضريبة على القيمة المضافة المحصلة على عمليات تقديم الخدمات على الأسس النقدية.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('240', '4', '44274', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم المجوهرات المحتسبة ضريبتها على أساس هامش الربح الإجمالي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('241', '4', '44275', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال للسياح.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('242', '4', '44276', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال وتقديم الخدمات للذات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('243', '4', '44277', 'الضريبة على القيمة المضافة المحصلة على مبالغ مستحقة لغير المقيمين.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('244', '4', '44278', 'الضريبة على القيمة المضافة المحصلة على مبيع أصول ثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('245', '4', '44279', 'الضريبة على القيمة المضافة المحصلة على السلفات والمقبوضات على حساب طلبيات قيد التنفيذ.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('246', '4', '4428', 'لضريبة على القيمة المضافة القابلة للتسوية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('247', '4', '44281', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('248', '4', '442811', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة على السلفات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('249', '4', '442812', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة على الحسابات الأخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('250', '4', '44282', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('251', '4', '442821', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة على السلفات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('252', '4', '442822', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة على الحسابات الأخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('253', '4', '4429', ' الضريبة على القيمة المضافة المطلوب استردادها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('254', '4', '44291', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد فصلي. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('255', '4', '44292', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد نصف سنوي.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('256', '4', '44293', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد سنوي.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('257', '4', '44294', 'الضريبة على القيمة المضافة المطلوب استردادها - مادة 59 .');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('258', '4', '443', '                       ضرائب متوجبة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('259', '4', '4431', '                     الضرائب على الارباح');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('260', '4', '445', '                      الدولة والمؤسسات العامة (ذمم دائنة اخرى)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('261', '4', '449', '                      الدولة والمؤسسات العامة (ذمم مدينة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('262', '4', '4491', '                     اعانات مستحقة غير مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('263', '4', '4497', '                     ذمم مدينة اخرى (الدولة والمؤسسات العامة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('264', '4', '4498', '                     ايرادات مستحقة غير مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('265', '4', '45', '                       الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('266', '4', '451', '                      حسابات الشركاء الجارية المدينة او الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('267', '4', '4511', '                     شركات شقيقة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('268', '4', '45111', '                           الشركة الام');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('269', '4', '45112', '                           الشركات التابعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('270', '4', '45113', '                           الشركات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('271', '4', '45114', '                           الشركات الداخلة في عدة مجموعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('272', '4', '4515', '                     شركاء آخرون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('273', '4', '4518', '                     عمليات مشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('274', '4', '453', '                      انصبة ارباح برسم الدفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('275', '4', '455', '                      ذمم دائنة اخرى للشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('276', '4', '4551', '                     حسابات المقدمات للشركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('277', '4', '4552', '                     رأس المال المقرر استرداده من الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('278', '4', '4557', '                     ذمم دائنة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('279', '4', '459', '                      ذمم الشركاء المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('280', '4', '4591', '                     رأس المال المكتتب غير المستدعى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('281', '4', '4592', '                     رأس المال المكتتب المستدعى وغير المدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('282', '4', '4597', '                     ذمم مدينة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('283', '4', '46', '                       ذمم مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('284', '4', '461', '                      ذمم الاستثمار الدائنة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('285', '4', '4611', '                     ذمم دائنة متعلقة بالعبوات والمعدات الموضوعة بالامانة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('286', '4', '4619', '                     حسابات دائنة مختلفة - استثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('287', '4', '463', '                      اقساط برسم الدفع على اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('288', '4', '4631', '                     سندات مشاركة غير محرره');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('289', '4', '4633', '                     سندات اخرى مجمدة غير محرره');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('290', '4', '465', '                      دائنون مختلفون خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('291', '4', '4651', '                     ذمم دائنة على امتلاك سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('292', '4', '4652', '                     سندات الدين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('293', '4', '4653', '                     اقساط برسم الدفع على سندات  توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('294', '4', '4659', '                     دائنون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('295', '4', '468', '                      مدينون مختلفون للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('296', '4', '4681', '                     ذمم مدينة متعلقة بالعبوات والمعدات الواجب اعادتها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('297', '4', '4689', '                     حسابات اخرى مدينة مختلفة - للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('298', '4', '469', '                      مدينون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('299', '4', '4691', '                     ذمم مدينة على بيع اصول ثابتة وسندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('300', '4', '4699', '                     حسابات اخرى مدينة مختلفة - خارج الاستثمار -');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('301', '4', '47', '                       حسابات التسوية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('302', '4', '471', '                      الاعباء الواجب توزيعها على عدة دورات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('303', '4', '4711', '                     اعباء ما قبل الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('304', '4', '4712', '                     التصليحات الكبير الواجب استهلاكها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('305', '4', '4713', '                     علاوات تسديد السندات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('306', '4', '4719', '                     اعباء اخرى واجب توزيعها على عدة دورات مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('307', '4', '472', '                      اعباء محتسبة مسبقا');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('308', '4', '473', '                      ايرادات محتسبة مسبقا');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('309', '4', '475', '                      فروقات صرف  - خصوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('310', '4', '4751', '                     الزيادة في الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('311', '4', '4752', '                     التدني في الذمم الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('312', '4', '4758', '                     فروقات معوضة بفرق الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('313', '4', '476', '                      فروقات صرف  - اصول');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('314', '4', '4761', '                    التدني في الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('315', '4', '4762', '                     الزيادة في الذمم الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('316', '4', '4768', '                     فروقات معوضة بفرق الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('317', '4', '48', '                       الحسابات المؤقتة وقيد التسوية حسابات التوزيع الدوري للاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('318', '4', '481', '                             (اشتراكات) حسابات التوزيع الدوري للايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('319', '4', '482', '                             (اشتراكات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('320', '4', '483', '                      ...................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('321', '4', '49', '                       مؤونات لمواجهة هبوط قيم حسابات الذمم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('322', '4', '491', '                      مؤونات هبوط قيم الذمم المدينة الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('323', '4', '495', '                      مؤونات هبوط قيم حسابات الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('324', '4', '496', '                      مؤونات هبوط قيم حسابات الذمم المدينة المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('325', '4', '4968', '                     مدينون مختلفون - للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('326', '4', '4969', '                     مدينون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('327', '5', '5', '       الفئة الخامسة - الحسابات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('328', '5', '50', '                       سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('329', '5', '501', '                      اسهم صادرة عن الشركة ومعاد شراؤها من قبلها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('330', '5', '502', '                      سندات تمنح حامليها حق الملكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('331', '5', '505', '                      سندات دين وقسائم صادرة عن الشركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('332', '5', '506', '                      سندات تمنح حامليها حقوق الدائنين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('333', '5', '51', '                       المؤسسات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('334', '5', '511', '                      شكات وقسائم برسم القبض');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('335', '5', '512', '                      بنوك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('336', '5', '519', '                      مؤسسات التمويل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('337', '5', '53', '                       الصندوق');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('338', '5', '58', '                       التحويلات الداخلية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('339', '5', '59', '                       مؤونات هبوط اسعار سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('340', '6', '6', '    الفئة السادسة - حسابات الاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('341', '6', '60', '                       مشتريات البضاعة وقيمة التغيير في المخزون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('342', '6', '601', '                            مشتريات البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('343', '6', '6011', '                     البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('344', '6', '6012', '                     العبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('345', '6', '6018', '                     نفقات اضافية على شراء البضائع والعبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('346', '6', '60181', '                        نقليات للمشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('347', '6', '60182', '                        سمسرة وعمولات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('348', '6', '60183', '                        تأمين على الشحن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('349', '6', '60184', '                        اتعاب مخلصي البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('350', '6', '60185', '                        رسوم جمركية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('351', '6', '6019', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('352', '6', '605', '                      قيمة التغيير في مخزون البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('353', '6', '6051', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('354', '6', '6052', '                     مخزون اخر المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('355', '6', '61', '                       مواد اولية واستهلاكية مستخدمة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('356', '6', '611', '                      مشتريات المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('357', '6', '6111', '                     شراء مواد اولية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('358', '6', '61111', '                        مواد اولية أ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('359', '6', '61112', '                        مود اولية ب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('360', '6', '6112', '                     مواد ولوازم استهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('361', '6', '61121', '                        محروقات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('362', '6', '61122', '                        مواد الصيانة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('363', '6', '61123', '                        لوازم للمشغل والمصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('364', '6', '61124', '                        لوازم للمخزن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('365', '6', '61125', '                        لوازم مكتبية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('366', '6', '6113', '                     شراء عبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('367', '6', '61131', '                        عبوات لا تسترد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('368', '6', '61132', '                        عبوات تسترد ويعاد استعمالها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('369', '6', '61133', '                        عبوات تستعمل على اوجه مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('370', '6', '6118', '                     مصاريف اضافية على شراء مواد اولية واستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('371', '6', '61181', '                        نقليات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('372', '6', '61182', '                        سمسرة وعمولات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('373', '6', '61183', '                        تامين على الشحن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('374', '6', '61184', '                        اتعاب مخلصي البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('375', '6', '61185', '                        رسوم جمركية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('376', '6', '6119', '                     حسومات مكتسبة (بالتفصيل)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('377', '6', '615', '                      قيمة التغيير في مخزون المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('378', '6', '6151', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('379', '6', '6152', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('380', '6', '62', '                       اعباء خارجية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('381', '6', '621', '                      مشتريات من ملتزمين ثانويين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('382', '6', '6211', '                     ملتزم ثانوي أ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('383', '6', '6212', '                     ملتزم ثانوي ب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('384', '6', '6219', '                     حسومات مكتسبة على مشتريات من ملتزمين ثانويين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('385', '6', '625', '                      الاتاوى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('386', '6', '6251', '                     ايجار - قرض اموال منقولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('387', '6', '6252', '                     ايجار - قرض اموال غير منقولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('388', '6', '6253', '                     حقوق الامتياز');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('389', '6', '6254', '                     براءات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('390', '6', '6255', '                     اجازات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('391', '6', '6256', '                     علامات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('392', '6', '6257', '                     اساليب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('393', '6', '6258', '                     حقوق وقيم مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('394', '6', '626', '                      الخدمات الخارجية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('395', '6', '6261', '                     نقليات واتصالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('396', '6', '62611', '                        نفقات نقل للموجودات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('397', '6', '62612', '                        نفقات النقل المشترك للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('398', '6', '62615', '                        نفقات البريد والاتصالات السلكية واللاسلكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('399', '6', '6262', '                     الصيانة والتصليحات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('400', '6', '6263', '                     الايجارات (خدمات الابنية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('401', '6', '62631', '                        الايجارات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('402', '6', '62632', '                        اعباء تأجيرية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('403', '6', '6264', '                     خدمات الفنادق والمطاعم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('404', '6', '62641', '                        تشريفات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('405', '6', '62642', '                        نقل وانتقال للمبيعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('406', '6', '62643', '                        اطعام المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('407', '6', '6265', '                     خدمات المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('408', '6', '62651', '                        المستخدمون المؤقتون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('409', '6', '62652', '                        اجور الوسطاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('410', '6', '62653', '                        بدلات اتعاب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('411', '6', '6266', '                     خدمات تعليمية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('412', '6', '62661', '                        الاعداد والتدريب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('413', '6', '62662', '                        التوثيق');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('414', '6', '6267', '                     الدراسات والبحوث');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('415', '6', '6268', '                     اقساط التأمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('416', '6', '6269', '                     خدمات خارجية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('417', '6', '62691', '                        خدمات صحية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('418', '6', '62692', '                        خدمات مالية (مصاريف على سندات واوراق مالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('419', '6', '63', '                       اعباء المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('420', '6', '631', '                      رواتب واجور المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('421', '6', '6311', '                     الرواتب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('422', '6', '6312', '                     الاجور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('423', '6', '6314', '                     العمولات الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('424', '6', '6316', '                     البدلات المدفوعة للمديرين الذين يتمتعون بأغلبية في ملكية المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('425', '6', '6317', '                     البدلات المدفوعة لاعضاء مجلس الادارة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('426', '6', '635', '                      اعباء اجتماعية (ضمان اجتاعي ...)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('427', '6', '64', '                       ضرائب ورسوم ومدفوعات مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('428', '6', '641', '                      على الرواتب والاجور وبدلات الاتعاب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('429', '6', '642', '                      ضرائب ورسوم للبلديات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('430', '6', '643', '                      ضرائب على المبيعات غير قابلة للاسترداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('431', '6', '644', '                      رسوم التسجيل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('432', '6', '645', '                      ضرائب ورسوم ومدفوعات مماثلة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('433', '6', '65', '                       مخصصات الاستهلاكات والمؤونات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('434', '6', '651', '                      مخصصات الاستهلاكات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('435', '6', '6511', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('436', '6', '6512', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('437', '6', '6515', '                     اعباء للتوزيع على عدة دورات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('438', '6', '655', '                      مخصصات المؤونة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('439', '6', '6551', '                     مؤونات هبوط اسعار الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('440', '6', '6552', '                     مؤونات هبوط اسعار الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('441', '6', '6553', '                     مؤونات هبوط اسعار المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('442', '6', '6554', '                     مؤونات هبوط قيم الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('443', '6', '6555', '                     مؤونات لمواجهة المخاطر والاعباء العائدة للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('444', '6', '66', '                       اعباء ادارية عادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('445', '6', '661', '                      اعباء ادارية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('446', '6', '6611', '                     بدلات الحضور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('447', '6', '6612', '                     خسارة على ذمم الاستثمار المدينة التي ثبت هلاكها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('448', '6', '665', '                      حصة المؤسسة من الخسائر على عمليات مشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('449', '6', '67', '                       الاعباء المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('450', '6', '673', '                      فوائد واعباء مشابهه');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('451', '6', '6731', '                     فوائد على الذمم الدائنة والقروض');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('452', '6', '6732', '                     فوائد على الحسابات  الجارية والودائع الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('453', '6', '6733', '                     فوائد السندات المربوطة بكفالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('454', '6', '6734', '                     فوائد الذمم الدائنة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('455', '6', '6735', '                     الخصم الممنوح من المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('456', '6', '6736', '                     فوائد مصرفية وفوائد على عمليات التمويل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('457', '6', '675', '                      فروقات صرف سلبية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('458', '6', '6751', '                     فروقات على عمليات عادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('459', '6', '6752', '                     فروقات على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('460', '6', '676', '                      اعباء صافية على عمليات التفرغ عن سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('461', '6', '679', '                      مخصصات الاستهلاكات والمؤونات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('462', '6', '6791', '                     استهلاك علاوات التسديد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('463', '6', '6792', '                     مؤونات هبوط اسعار الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('464', '6', '6794', '                     مؤونات هبوط اسعار سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('465', '6', '6795', '                     مؤونات لمواجهة المخاطر والاعباء المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('466', '6', '68', '                       اعباء خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('467', '6', '681', '                      القيمة الدفترية للاصول الثابتة المتفرغ عنها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('468', '6', '6811', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('469', '6', '6812', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('470', '6', '6815', '                     اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('471', '6', '685', '                      اعباء اخرى خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('472', '6', '6851', 'اعباء على عمليات ادارة المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('473', '6', '68511', '                        هبات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('474', '6', '68512', '                        اعانات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('475', '6', '68513', 'غرامات ضريبية وجزائية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('476', '6', '68514', 'غرامات على صفقات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('477', '6', '68515', 'ذمم مدينة اصبحت هالكة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('478', '6', '68516', 'عمليات ادارية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('479', '6', '6855', '                     اعباء على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('480', '6', '68551', '                        اعباء ناتجة عن موجب تطبيق مؤشر اسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('481', '6', '68552', '                        جوائز تسديد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('482', '6', '68553', '                        اعباء ناتجة عن استرداد المؤسسة لاسهم وسندات صادرة عنها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('483', '6', '689', '                      مخصصات الاستهلاكات والمؤونات خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('484', '6', '6891', '                     استهلاك استثنائي على اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('485', '6', '6892', '                     مؤونات هبوط اسعار استثنائي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('486', '6', '6895', '                     مؤونات لمواجهة مخاطر واعباء خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('487', '6', '69', '                       الضرائب على الارباح ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('488', '7', '7', '                الفئة السابعة - حسابات الايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('489', '7', '70', '                       مبيعات البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('490', '7', '701', '                      فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('491', '7', '709', '                      حسومات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('492', '7', '71', '                       المنتجات المباعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('493', '7', '711', '                      مبيعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('494', '7', '7111', '                     مبيعات المنتجات التامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('495', '7', '7112', '                     مبيعات المنتجات الوسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('496', '7', '7113', '                     مبيعات فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('497', '7', '712', '                      اشغال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('498', '7', '713', '                      خدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('499', '7', '717', '                      ايرادات النشاطات الفرعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('500', '7', '719', '                      حسومات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('501', '7', '7191', '                     على بيع المنتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('502', '7', '7192', '                     على بيع الاشغال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('503', '7', '7193', '                     على بيع الخدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('504', '7', '7197', '                     على بيع نشاطات فرعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('505', '7', '72', '                       الانتاج المخزون (قيمة التغيير)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('506', '7', '721', '                      قيد الصنع (اموال)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('507', '7', '7211', '                     منتجات قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('508', '7', '7212', '                     اشغال قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('509', '7', '722', '                      قيد الصنع (خدمات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('510', '7', '7225', '                     دراسات قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('511', '7', '7226', '                     خدمات قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('512', '7', '725', '                      منتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('513', '7', '7251', '                     منتجات وسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('514', '7', '7255', '                     منتجات تامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('515', '7', '7258', '                     فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('516', '7', '73', '                       منتجات لها طابع الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('517', '7', '731', '                      اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('518', '7', '732', '                      اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('519', '7', '74', '                       اعانات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('520', '7', '741', '                      للبضائع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('521', '7', '742', '                      للانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('522', '7', '75', '                       استردادات من المؤونات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('523', '7', '752', ' للاستثمار                       استردادات من مؤونات  هبوط اسعار الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('524', '7', '753', '                      استردادات من مؤونات هبوط الاسعار الاصول المتداولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('525', '7', '76', '                       ايرادات اخرى ناتجة عن الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('526', '7', '761', '                      ايرادات عادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('527', '7', '7611', '                     اتاوى الامتيازات, البراءات, الاجازات, العلامات والاساليب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('528', '7', '7612', '                     ايرادات الابنية غير المخصصة للنشاط المهني');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('529', '7', '7613', '                     بدلات حضور وتعويضات اعضاء مجلس الادارة, المديرين . . . .');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('530', '7', '7619', '                     اعباء استثمار محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('531', '7', '765', '                      حصص ارباح العمليات المشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('532', '7', '7651', '                     حصص الاعباء الصافية التي جرى تحويلها (محاسبة المؤسسة التي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('533', '7', '7655', '                     حصص الايرادات الصافية المقررة (محاسبة المؤسسة التي لم تتولى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('534', '7', '77', '                       الايرادات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('535', '7', '771', '                      ايرادات سندات  المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('536', '7', '7711', '                     عائدات سندات  التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('537', '7', '7716', '                     عائدات المشاركات  الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('538', '7', '7717', '                     عائدات الذمم المدينة المرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('539', '7', '772', '                      ايرادات القيم المنقولة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('540', '7', '7721', '                     عائدات سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('541', '7', '7723', '                     عائدات السندات المجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('542', '7', '7725', '                     عائدات الذمم المدينة المجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('543', '7', '7726', '                     عائدات الذمم المدينة التجارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('544', '7', '7727', '                     عائدات الذمم المدينة المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('545', '7', '773', '                      فوائد وايرادات مشابهة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('546', '7', '7731', '                     عائدات القروض الممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('547', '7', '7733', '                     الخصم الممنوح للمؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('548', '7', '775', '                      فروقات صرف ايجابية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('549', '7', '7751', '                     على عمليات جارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('550', '7', '7755', '                     على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('551', '7', '778', '                      ايرادات مالية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('552', '7', '7781', '                     ايرادات صافية على بيع سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('553', '7', '7789', '                     اعباء مالية محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('554', '7', '779', '                      استردادات من المؤونات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('555', '7', '7793', '                     استردادات من مؤونات هبوط الاسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('556', '7', '7795', '                     استردادات من مؤونات المخاطر والاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('557', '7', '78', '                       ايرادات التفرغ عن اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('558', '7', '781', '                      ايرادات التفرغ عن اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('559', '7', '7811', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('560', '7', '7812', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('561', '7', '7815', '                     اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('562', '7', '782', '                      اعانات للتوظيفات محولة الى نتيجة الدورة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('563', '7', '788', '                      ايرادات اخرى خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('564', '7', '7881', '                     ايرادات استثنائية على عمليات عادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('565', '7', '7888', '                     ايرادات اخرى على عمليات رأسمالية استثنائية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('566', '7', '7889', '                     اعباء استثنائية محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('567', '7', '789', '                      استردادات من مؤونات خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('568', '7', '7891', '                     استردادات من مؤونات هبوط الاسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('569', '7', '7895', '                     استردادات من مؤونات المخاطر');
