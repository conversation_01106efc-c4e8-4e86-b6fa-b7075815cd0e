CREATE OR REPLACE FUNCTION auth.check_and_revoke_schema_usage_if_no_object_privs(
    p_schema_name TEXT,
    p_rolename TEXT
)
RETURNS VOID
AS $$
DECLARE
    v_has_object_privileges BOOLEAN := FALSE;
    v_schema_exists BOOLEAN;
    v_role_exists BOOLEAN;
BEGIN
    -- Check if the specified schema exists
    SELECT EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = p_schema_name) INTO v_schema_exists;
    IF NOT v_schema_exists THEN
        RAISE WARNING 'Schema "%" does not exist.', p_schema_name;
        RETURN; -- Exit if schema doesn't exist
    END IF;

    -- Check if the specified role exists
    SELECT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = p_rolename) INTO v_role_exists;
    IF NOT v_role_exists THEN
        RAISE WARNING 'Role "%" does not exist.', p_rolename;
        RETURN; -- Exit if role doesn't exist
    END IF;

    -- Check for explicit privileges on tables, views, or sequences within the schema
    -- information_schema.role_table_grants covers these object types
    SELECT TRUE
    INTO v_has_object_privileges
    FROM information_schema.role_table_grants
    WHERE grantee = p_rolename
      AND table_schema = p_schema_name
    LIMIT 1; -- We only need to find one privilege to know they have some

    -- If no table/view/sequence privileges found, check for explicit privileges on routines (functions, procedures)
    -- information_schema.routine_privileges covers these object types
    IF NOT v_has_object_privileges THEN
        SELECT TRUE
        INTO v_has_object_privileges
        FROM information_schema.routine_privileges
        WHERE grantee = p_rolename
          AND routine_schema = p_schema_name
        LIMIT 1; -- We only need to find one privilege
    END IF;

    -- If after checking both common types, no explicit object privileges were found
    IF NOT v_has_object_privileges THEN
        RAISE NOTICE 'Role "%" has no explicit object privileges in schema "%". Revoking USAGE.', p_rolename, p_schema_name;
        -- Revoke the USAGE privilege on the schema for the role
        -- Using IF EXISTS prevents an error if the privilege is already not granted
        EXECUTE format('REVOKE USAGE ON SCHEMA %I FROM %I', p_schema_name, p_rolename);
    ELSE
        RAISE NOTICE 'Role "%" has explicit object privileges in schema "%". USAGE privilege retained.', p_rolename, p_schema_name;
    END IF;

END;
$$ LANGUAGE plpgsql SECURITY DEFINER; -- SECURITY DEFINER allows the function to run with the privileges of its owner (e.g., postgres), which is needed to perform REVOKE

alter function auth.check_and_revoke_schema_usage_if_no_object_privs(text, text) owner to #POSTGRES_DEFAULT_ADMIN#;
