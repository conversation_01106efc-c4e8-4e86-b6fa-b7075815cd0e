CREATE TABLE "machine"."machine_emp" (
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "att_id" BIGINT NOT NULL ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "options" JSONB ,
 CONSTRAINT "machine_emp_pkey" PRIMARY KEY ("machine_id", "att_id", "emp_id")
);
ALTER TABLE "machine"."machine_emp" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
