CREATE TABLE "person"."personType" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('person."personType_id_seq"'::regclass) ,
 "initial_acc" CHAR(5) ,
 "description" VARCHAR(20) NOT NULL UNIQUE ,
 CONSTRAINT "personType_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."personType" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "person"."personType_id_seq" OWNED BY "person"."personType"."pk";
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('1', 'موظف', '42001');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('2', 'متعاقد', '42002');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('3', 'عامل', '42003');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('4', 'شرطي', '42004');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('5', 'عامل فاتورة', '46191');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('6', 'مستشار', '46192');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('7', 'عضو مجلس', '46193');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('8', 'مسوق', '42005');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('9', 'زبون', '41100');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('10', 'مورد', '40100');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('11', 'مورد اعباء', '46590');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('12', 'مورد معاملات', '46591');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('13', 'شريك', '45100');
