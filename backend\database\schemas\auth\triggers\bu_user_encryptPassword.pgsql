create or replace function auth.bu_user_encryptPassword() returns trigger as $$
begin

  new.password_ := #DB_SCHEMA_EXTENSION#.crypt(new.password_, #DB_SCHEMA_EXTENSION#.gen_salt('bf'));
  return new;

end;
$$ language plpgsql;

alter function auth.bu_user_encryptPassword() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_bu_user_encryptPassword
  before update of password_ on auth.user
  for each row
  execute procedure auth.bu_user_encryptPassword();