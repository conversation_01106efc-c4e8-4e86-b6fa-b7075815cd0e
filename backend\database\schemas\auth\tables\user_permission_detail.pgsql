CREATE TABLE "auth"."user_permission_detail" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."user_permission_detail_id_seq"'::regclass) ,
 "user_permission_id" BIGINT NOT NULL REFERENCES "auth"."user_permission" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'False'::BOOLEAN ,
 "permission_name_id" VARCHAR(10) NOT NULL REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "fieldlist" VARCHAR(50)[] ,
 CONSTRAINT "user_permission_detail_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_permission_detail" ADD CONSTRAINT "user_permission_detail_check" CHECK (permission_name_id IN ('Select', 'Insert', 'Update','View','Append','Change') OR (permission_name_id NOT IN ('Select', 'Insert', 'Update','View','Append','Change') AND fieldlist IS NULL) OR (allow is false AND fieldlist is Null));
ALTER TABLE "auth"."user_permission_detail" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."user_permission_detail_id_seq" OWNED BY "auth"."user_permission_detail"."pk";
