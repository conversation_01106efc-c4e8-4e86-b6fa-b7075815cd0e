[{"table_name": "decision", "seq_name": "decision_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "decisionAssignment", "seq_name": "decisionAssignment_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "decisionEmployment", "seq_name": "decisionEmployment_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "decisionEndOfService", "seq_name": "decisionEndOfService_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "decisionTransfer", "seq_name": "decisionTransfer_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "exitDocs", "seq_name": "exitDocs_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "exitDocsAuthority", "seq_name": "exitDocsAuthority_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "vacationAuthority", "seq_name": "vacationAuthority_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "vacationDocs", "seq_name": "vacationDocs_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "vacationMAuthority", "seq_name": "vacationMAuthority_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "vacationMDocs", "seq_name": "vacationMDocs_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}, {"table_name": "decisionExtend", "seq_name": "decisionExtend_id_seq", "seq_type": "BIGINT", "primary_key": "pk"}]