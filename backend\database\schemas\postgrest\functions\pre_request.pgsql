create or replace function postgrest.pre_request()
returns void 
SECURITY INVOKER
as $$
DECLARE
  change_pass boolean := current_setting('request.jwt.claims', true)::json->>'change_pass'::boolean;
  user_id smallint;
BEGIN
    -- Check if the software is trial
    -- Check if the token has been revoked

    -- Check if the user must change password
    if change_pass = 'true' then
        RAISE EXCEPTION 'User Must Change Password'
            USING HINT = 'Please change your password before proceeding.';

        -- Redirect to change password page
        -- Set a cookie to remember the redirect
        -- Redirect to the change password page
        -- Redirect to the login page
    end if;

    select pk into user_id from 
    auth.user where rolename = current_setting('request.jwt.claims', true)::json->>'role';
    
    set local myapp.user_id = user_id;

end;
$$ language plpgsql;

alter function postgrest.pre_request() owner to #POSTGRES_DEFAULT_ADMIN#;
grant execute on function postgrest.pre_request() to #DB_AUTHENTICATOR_USER#;