CREATE TABLE "person"."person" (
 "pk" BIGINT NOT NULL DEFAULT nextval('person."person_id_seq"'::regclass) ,
 "active_" BOOLEAN NOT NULL DEFAULT 'True'::BOOLEAN ,
 "nickname" VARCHAR(15) ,
 "name_" VARCHAR(150) UNIQUE ,
 "social_media" jsonb ,
 "gender" CHAR ,
 CONSTRAINT "person_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."person" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "person"."person_id_seq" OWNED BY "person"."person"."pk";
