import os

from psycopg2 import sql , errors
from config import config

from .db_operations import get_postgres_server_os

extension = {
    "Windows" : ["pgcrypto"],
    "Linux" : ["pgcrypto","pgaudit"],
    "Darwin" : ["pgcrypto","pgaudit"]
}

extensions = ("pgcrypto","pgaudit")
offline_extensions = [
    { "name" : "pgjwt", "sql_file_name" :"pgjwt--0.2.0.sql" }
    ]

            
def create_extension(cur, extension_name: str) -> bool:
    try:
        if extension_exists(cur, extension_name):
            return True
        
        if not extension_available(cur, extension_name):
            return False
        
        run_sql = sql.SQL("""
                          CREATE EXTENSION {} SCHEMA {};
                          """).format(sql.Identifier(extension_name), sql.Identifier(config.DB_SCHEMA_EXTENSION))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Extension {extension_name} created successfully")
        return True
    
    except errors.InvalidSchemaName as e:
        config.error(f"Invalid schema name: {e}")
        return False
    
    except Exception as e:
        config.error(f"Error creating extension {extension_name}: {e}")
        return False

def extension_exists(cur, extension_name: str) -> bool:
    try:
        cur.execute(f"SELECT extname FROM pg_extension WHERE extname = '{extension_name}'")
        if cur.fetchone():
            config.warning(f"Extension {extension_name} already exists")
            return True
        else:
            return False
            
    except Exception as e:
        config.error(f"Error checking for extension {extension_name}: {e}")
        return False

def extension_available(cur, extension_name: str) -> bool:
    try:
        cur.execute(f"SELECT 1 FROM pg_available_extensions WHERE name = '{extension_name}'")
        if cur.fetchone():
            config.info(f"Extension {extension_name} is available for installation")
            return True
        else:
            config.warning(f"Extension {extension_name} is not available for installation")
            return False
            
    except Exception as e:
        config.error(f"Error checking for extension {extension_name}: {e.__module__}")
        return False         
            
def create_extensions() -> bool:
    try:
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:

                for ext in extension[get_postgres_server_os()]:
                    if not create_extension(cur, ext):
                       config.error(f"Error creating extension {ext}")
                       return False
                conn.commit()
                
                for ext in offline_extensions:
                    name = ext["name"]
                    sqlname = ext["sql_file_name"]
                    sf = os.path.join(os.path.dirname(__file__), 'offline_extensions', sqlname)
                    with open(sf, "r") as f:
                        sql_script = prepare_sql_script(f.read())
                        
                        try:
                            config.debug(sql_script)
                            config.cumulativeSQL(sql_script)
                            cur.execute(sql_script)
                            config.info(f"Offline Extension {name} installed successfully")
                            conn.commit()
                        except Exception as e:
                            config.error(f"Error installing extension {name}: {e}")
                            return False
        
        config.info("Built-in Extensions created successfully")
        return True
    
    except Exception as e:
        config.error(f"Error creating extensions: {e}")
        return False

def drop_extensions() -> bool:
    try:
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
                for ext in extension[get_postgres_server_os()]:
                    if not drop_extension(cur, ext):
                        config.error(f"Error dropping extension {ext}")
                        return False
                conn.commit()
        config.info("Built-in Extensions dropped successfully")
        return True
    
    except Exception as e:
        config.error(f"Error dropping extensions: {e}")
        return False

def drop_extension(cur, extension_name: str) -> bool:
    try:
        if not extension_exists(cur, extension_name):
            return True
        
        run_sql = sql.SQL("DROP EXTENSION {} CASCADE;").format(sql.Identifier(extension_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))        
        cur.execute(run_sql)
        config.info(f"Extension {extension_name} dropped successfully")
        return True
    
    except errors.InvalidSchemaName as e:
        config.error(f"Invalid schema name: {e}")
        return False

def prepare_sql_script(sql_script: str) -> str:
    return sql_script.replace("@extschema@", config.DB_SCHEMA_EXTENSION).replace("FUNCTION ", f"FUNCTION {config.DB_SCHEMA_EXTENSION}.")