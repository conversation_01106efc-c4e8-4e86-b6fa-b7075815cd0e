CREATE OR REPLACE FUNCTION auth.ai_appObjects_fillFields()
RETURNS TRIGGER AS $$
declare
 v_schema_name text;
 v_table text;
BEGIN
 -- Get schema and table names from the new row
 v_schema_name := NEW.schema_;
 v_table := NEW.name_; -- Assuming 'objects' holds the table name

 -- Check if both schema and table names are provided
   IF new."objectType"::text = 'Table' and NEW.options->>'sys_table' is not null and NEW.options->>'sys_table'::boolean = true THEN
      insert into auth."appObjects_allowed_permission"("appObjects_id", permission_name_id)
      values(new.pk , 'Select')

   ELSE
      insert into auth."appObjects_allowed_permission"("appObjects_id", permission_name_id)
      select new.pk , per.name_
      from auth.permission
      where per."objectType" = new."objectType";
   END IF;

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_appObjects_fillFields() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_ai_appObjects_fillFields
  after insert on auth."appObjects"
  for each row
  execute procedure auth.ai_appObjects_fillFields();