CREATE VIEW hr.v_emp_employment
WITH ( security_barrier = true,  security_invoker = true) AS 

Select
  vemp.pk as emp_id,
  vemp.empfullname as emp_name,
  vemp.active_ as active_,

  d."personType_id" as emp_type_id,
  et.description as emp_type_description,
  
  ot.name_ as decision_type,

  d.date_ as decision_date,
  d.branch_id as decision_branch_id,
  d.ref_ as decision_ref,

  bdp.branch_name as source_branch_name,
  bdp.department_id as target_department_id,
  bdp.department_name as target_department_name,
  bdp.post_id as target_post_id,
  bdp.post_name as target_post_name
  
From hr.v_emp vemp 
  inner join hr."decisionEmployment" demp on vemp.pk = demp.emp_id
  inner join person.v_emp_persontypes et on demp."personType_id" = et.pk
  inner join hr.decision d on demp.decision_id = d.pk
  inner join hr."decision_opType_personTypes" ot on d."opType_id" = ot.pk
  inner join enterprise.v_enterprise_branch_department_posts bdp on 
    d.branch_id = bdp.branch_id and demp.department_id = bdp.department_id and demp.post_id = bdp.post_id;

ALTER TABLE hr.v_emp_employment owner to #POSTGRES_DEFAULT_ADMIN#;