CREATE TABLE "enterprise"."branch_acc" (
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "chartOfAccountRef_id" SMALLINT NOT NULL REFERENCES "enterprise"."chartOfAccountRef" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allowRooting" BOOLEAN NOT NULL ,
 CONSTRAINT "branch_acc_pkey" PRIMARY KEY ("chartOfAccountRef_id", "branch_id")
);
ALTER TABLE "enterprise"."branch_acc" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
