-- SQL commands logged on 2025-07-24T09:19:39.777940

CREATE ROLE "aas_db_test_authenticator" WITH NOINHERIT NOCREATEDB NOCREATEROLE NOSUPERUSER NOREPLICATION NOBYPASSRLS LOGIN password 'UFTQgUis79k0JvLExr59nRksrTcRa2AJ';
CREATE ROLE "aas_db_test_anonymous" WITH NOLOGIN NOSUPERUSER NOINHERIT NOCREATEDB NOCREATEROLE NOREPLICATION NOBYPASSRLS;
GRANT "aas_db_test_anonymous" TO "aas_db_test_authenticator";
CREATE DATABASE "aas_db_test" WITH ENCODING 'UTF8' LC_COLLATE 'en_US.UTF-8' LC_CTYPE 'en_US.UTF-8' TEMPLATE template0;
ALTER DATABASE "aas_db_test" OWNER TO "postgres";
ALTER DATABASE "aas_db_test" SET app.jwt_secret TO 'a487a18cd30327f1dc59d5433cff9e82e022a758eadcbce98f46c9356642587c';
ALTER DATABASE "aas_db_test" SET app.jwt_expiry TO '1 day';
ALTER DATABASE "aas_db_test" SET db.user_prefix = 'aas_db_test_usr_';
ALTER DATABASE "aas_db_test" SET db.group_prefix = 'aas_db_test_gr_';
ALTER DATABASE "aas_db_test" SET db.single_branch = true;
ALTER DATABASE "aas_db_test" SET db.single_department = false;
GRANT ALL PRIVILEGES ON DATABASE "aas_db_test" TO "postgres";
REVOKE ALL PRIVILEGES ON DATABASE "aas_db_test" FROM public;
ALTER DEFAULT PRIVILEGES REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "accounting" AUTHORIZATION "postgres";
ALTER SCHEMA "accounting" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "accounting" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "accounting" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "accounting" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "api" AUTHORIZATION "postgres";
ALTER SCHEMA "api" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "api" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "api" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "api" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "auth" AUTHORIZATION "postgres";
ALTER SCHEMA "auth" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "auth" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "auth" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "auth" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "cash" AUTHORIZATION "postgres";
ALTER SCHEMA "cash" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "cash" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "cash" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "cash" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "enterprise" AUTHORIZATION "postgres";
ALTER SCHEMA "enterprise" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "enterprise" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "enterprise" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "enterprise" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "hr" AUTHORIZATION "postgres";
ALTER SCHEMA "hr" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "hr" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "hr" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "hr" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "inventory" AUTHORIZATION "postgres";
ALTER SCHEMA "inventory" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "inventory" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "inventory" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "inventory" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "machine" AUTHORIZATION "postgres";
ALTER SCHEMA "machine" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "machine" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "machine" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "machine" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "person" AUTHORIZATION "postgres";
ALTER SCHEMA "person" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "person" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "person" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "person" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "pos" AUTHORIZATION "postgres";
ALTER SCHEMA "pos" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "pos" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "pos" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "pos" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "postgrest" AUTHORIZATION "postgres";
ALTER SCHEMA "postgrest" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "postgrest" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "postgrest" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "postgrest" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "purchase" AUTHORIZATION "postgres";
ALTER SCHEMA "purchase" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "purchase" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "purchase" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "purchase" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "sales" AUTHORIZATION "postgres";
ALTER SCHEMA "sales" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "sales" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "sales" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "sales" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "service" AUTHORIZATION "postgres";
ALTER SCHEMA "service" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "service" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "service" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "service" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "extschema" AUTHORIZATION "postgres";
ALTER SCHEMA "extschema" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "extschema" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "extschema" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "extschema" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE SCHEMA IF NOT EXISTS "public" AUTHORIZATION "postgres";
ALTER SCHEMA "public" OWNER TO "postgres";
ALTER DEFAULT PRIVILEGES IN SCHEMA "public" REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "public" REVOKE ALL ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA "public" REVOKE USAGE ON SEQUENCES FROM PUBLIC;
CREATE TYPE "auth"."objecttype" AS ENUM ('Table', 'View', 'Procedure', 'Function', 'Sequence', 'Schema');
ALTER TYPE "auth"."objecttype" OWNER TO "postgres";
CREATE TYPE "auth"."action" AS ENUM ('INSERT', 'UPDATE', 'DELETE');
ALTER TYPE "auth"."action" OWNER TO "postgres";
CREATE TYPE "hr"."docstate" AS ENUM ('Pending', 'Accepted', 'Rejected', 'Modified', 'Canceled');
ALTER TYPE "hr"."docstate" OWNER TO "postgres";
CREATE TYPE "hr"."signstate" AS ENUM ('Pending', 'Accepted', 'Rejected');
ALTER TYPE "hr"."signstate" OWNER TO "postgres";
CREATE TYPE "machine"."zkrecord" AS ("uid" integer, "user_id" text, "timestamp" timestamp without time zone, "status" integer, "punch" integer);
CREATE TYPE "machine"."attendance_record_type" AS ("att_id" bigint, "attendance_date" TIMESTAMP WITHOUT TIME ZONE, "options" JSONB);
CREATE TYPE "person"."nationality" AS ENUM ('Lebanese', 'Syrian', 'Palestinian');
ALTER TYPE "person"."nationality" OWNER TO "postgres";
CREATE SEQUENCE "auth"."appObjects_id_seq" AS SMALLINT;
ALTER SEQUENCE "auth"."appObjects_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."appObjects_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."group_id_seq" AS SMALLINT;
ALTER SEQUENCE "auth"."group_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."group_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."group_permission_seq" AS BIGINT;
ALTER SEQUENCE "auth"."group_permission_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."group_permission_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."user_id_seq" AS SMALLINT;
ALTER SEQUENCE "auth"."user_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."user_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."userLog_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."userLog_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."userLog_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."userToken_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."userToken_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."userToken_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."user_permission_seq" AS BIGINT;
ALTER SEQUENCE "auth"."user_permission_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."user_permission_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."group_permission_detail_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."group_permission_detail_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."group_permission_detail_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."user_department_id_seq" AS SMALLINT;
ALTER SEQUENCE "auth"."user_department_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."user_department_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."user_branch_id_seq" AS SMALLINT;
ALTER SEQUENCE "auth"."user_branch_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."user_branch_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."user_permission_detail_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."user_permission_detail_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."user_permission_detail_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."audit_log_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."audit_log_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."audit_log_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "auth"."audit_user_id_seq" AS BIGINT;
ALTER SEQUENCE "auth"."audit_user_id_seq" RESTART WITH 1;
ALTER SEQUENCE "auth"."audit_user_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."accountCategory_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."accountCategory_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."accountCategory_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."accountingMethods_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."accountingMethods_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."accountingMethods_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."branch_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."branch_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."branch_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."branch_department_post_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."branch_department_post_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."branch_department_post_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."chartOfAccountRef_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."chartOfAccountRef_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."chartOfAccountRef_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."currency_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."currency_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."currency_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."department_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."department_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."department_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."enterprise_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."enterprise_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."enterprise_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."financialYear_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."financialYear_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."financialYear_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."legalForm_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."legalForm_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."legalForm_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."post_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."post_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."post_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "enterprise"."branch_department_id_seq" AS SMALLINT;
ALTER SEQUENCE "enterprise"."branch_department_id_seq" RESTART WITH 1;
ALTER SEQUENCE "enterprise"."branch_department_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decision_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decision_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decision_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decisionAssignment_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decisionAssignment_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decisionAssignment_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decisionEmployment_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decisionEmployment_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decisionEmployment_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decisionEndOfService_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decisionEndOfService_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decisionEndOfService_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decisionTransfer_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decisionTransfer_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decisionTransfer_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."exitDocs_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."exitDocs_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."exitDocs_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."exitDocsAuthority_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."exitDocsAuthority_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."exitDocsAuthority_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."vacationAuthority_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."vacationAuthority_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."vacationAuthority_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."vacationDocs_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."vacationDocs_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."vacationDocs_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."vacationMAuthority_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."vacationMAuthority_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."vacationMAuthority_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."vacationMDocs_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."vacationMDocs_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."vacationMDocs_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "hr"."decisionExtend_id_seq" AS BIGINT;
ALTER SEQUENCE "hr"."decisionExtend_id_seq" RESTART WITH 1;
ALTER SEQUENCE "hr"."decisionExtend_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "machine"."machine_id_seq" AS SMALLINT;
ALTER SEQUENCE "machine"."machine_id_seq" RESTART WITH 1;
ALTER SEQUENCE "machine"."machine_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "machine"."type_id_seq" AS SMALLINT;
ALTER SEQUENCE "machine"."type_id_seq" RESTART WITH 1;
ALTER SEQUENCE "machine"."type_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "machine"."machinelogs_id_seq" AS BIGINT;
ALTER SEQUENCE "machine"."machinelogs_id_seq" RESTART WITH 1;
ALTER SEQUENCE "machine"."machinelogs_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "person"."person_id_seq" AS BIGINT;
ALTER SEQUENCE "person"."person_id_seq" RESTART WITH 1;
ALTER SEQUENCE "person"."person_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "person"."personContact_id_seq" AS SMALLINT;
ALTER SEQUENCE "person"."personContact_id_seq" RESTART WITH 1;
ALTER SEQUENCE "person"."personContact_id_seq" OWNER TO "postgres";
CREATE SEQUENCE "person"."personType_id_seq" AS SMALLINT;
ALTER SEQUENCE "person"."personType_id_seq" RESTART WITH 1;
ALTER SEQUENCE "person"."personType_id_seq" OWNER TO "postgres";
CREATE EXTENSION "pgcrypto" SCHEMA "extschema";
CREATE EXTENSION "pgaudit" SCHEMA "extschema";
--\echo Use "CREATE EXTENSION pgjwt" to load this file. \quit

CREATE OR REPLACE FUNCTION extschema.url_encode(data bytea) RETURNS text LANGUAGE sql AS $$
    SELECT translate(encode(data, 'base64'), E'+/=\n', '-_');
$$ IMMUTABLE;


CREATE OR REPLACE FUNCTION extschema.url_decode(data text) RETURNS bytea LANGUAGE sql AS $$
WITH t AS (SELECT translate(data, '-_', '+/') AS trans),
     rem AS (SELECT length(t.trans) % 4 AS remainder FROM t) -- compute padding size
    SELECT decode(
        t.trans ||
        CASE WHEN rem.remainder > 0
           THEN repeat('=', (4 - rem.remainder))
           ELSE '' END,
    'base64') FROM t, rem;
$$ IMMUTABLE;


CREATE OR REPLACE FUNCTION extschema.algorithm_sign(signables text, secret text, algorithm text)
RETURNS text LANGUAGE sql AS $$
WITH
  alg AS (
    SELECT CASE
      WHEN algorithm = 'HS256' THEN 'sha256'
      WHEN algorithm = 'HS384' THEN 'sha384'
      WHEN algorithm = 'HS512' THEN 'sha512'
      ELSE '' END AS id)  -- hmac throws error
SELECT extschema.url_encode(extschema.hmac(signables, secret, alg.id)) FROM alg;
$$ IMMUTABLE;


CREATE OR REPLACE FUNCTION extschema.sign(payload json, secret text, algorithm text DEFAULT 'HS256')
RETURNS text LANGUAGE sql AS $$
WITH
  header AS (
    SELECT extschema.url_encode(convert_to('{"alg":"' || algorithm || '","typ":"JWT"}', 'utf8')) AS data
    ),
  payload AS (
    SELECT extschema.url_encode(convert_to(payload::text, 'utf8')) AS data
    ),
  signables AS (
    SELECT header.data || '.' || payload.data AS data FROM header, payload
    )
SELECT
    signables.data || '.' ||
    extschema.algorithm_sign(signables.data, secret, algorithm) FROM signables;
$$ IMMUTABLE;


CREATE OR REPLACE FUNCTION extschema.try_cast_double(inp text)
RETURNS double precision AS $$
  BEGIN
    BEGIN
      RETURN inp::double precision;
    EXCEPTION
      WHEN OTHERS THEN RETURN NULL;
    END;
  END;
$$ language plpgsql IMMUTABLE;


CREATE OR REPLACE FUNCTION extschema.verify(token text, secret text, algorithm text DEFAULT 'HS256')
RETURNS table(header json, payload json, valid boolean) LANGUAGE sql AS $$
  SELECT
    jwt.header AS header,
    jwt.payload AS payload,
    jwt.signature_ok AND tstzrange(
      to_timestamp(extschema.try_cast_double(jwt.payload->>'nbf')),
      to_timestamp(extschema.try_cast_double(jwt.payload->>'exp'))
    ) @> CURRENT_TIMESTAMP AS valid
  FROM (
    SELECT
      convert_from(extschema.url_decode(r[1]), 'utf8')::json AS header,
      convert_from(extschema.url_decode(r[2]), 'utf8')::json AS payload,
      r[3] = extschema.algorithm_sign(r[1] || '.' || r[2], secret, algorithm) AS signature_ok
    FROM regexp_split_to_array(token, '\.') r
  ) jwt
$$ IMMUTABLE;
CREATE TABLE "auth"."app" (
 "pk" SMALLINT NOT NULL ,
 "appLable" VARCHAR(100) NOT NULL UNIQUE ,
 "default_" BOOLEAN NOT NULL DEFAULT 'False'::BOOLEAN ,
 "active_" BOOLEAN NOT NULL DEFAULT 'False'::BOOLEAN ,
 "personTypeList" SMALLINT[] ,
 CONSTRAINT "app_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."app" OWNER TO postgres;
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('0', 'all', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('1', 'postgrestApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('2', 'apiApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('3', 'AuthApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('4', 'EnterpriseApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('5', 'PersonApp', 'TRUE', 'TRUE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('6', 'MachineApp', 'FALSE', 'TRUE', '{1,2,3,4,5}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('7', 'HRApp', 'FALSE', 'TRUE', '{1,2,3,4,5,6,7}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('8', 'AccountingApp', 'FALSE', 'FALSE', '{1,2,3,4,5,6,7,8,9,10,11,12,13}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('9', 'CashApp', 'FALSE', 'FALSE', '{1,2,3,4,5,6,7,8,9,10,11,12,13}');
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('10', 'InventoryApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('11', 'SalesApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('12', 'PurchaseApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('13', 'POSApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('14', 'ServiceApp', 'FALSE', 'FALSE', NULL);
INSERT INTO "auth"."app" ("pk", "appLable", "default_", "active_", "personTypeList") VALUES ('15', 'MarketingApp', 'FALSE', 'FALSE', NULL);
CREATE TABLE "auth"."permission" (
 "name_" VARCHAR(10) NOT NULL ,
 "privilege" VARCHAR(10) NOT NULL ,
 "binaryCode" BIT(4) NOT NULL ,
 "order_" SMALLINT ,
 "objectType" auth.objecttype NOT NULL ,
 CONSTRAINT "permission_pkey" PRIMARY KEY ("name_")
);
ALTER TABLE "auth"."permission" OWNER TO postgres;
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Select', 'Table', 'SELECT', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0010', 'Insert', 'Table', 'INSERT', 2);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0100', 'Update', 'Table', 'UPDATE', 3);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('1000', 'Delete', 'Table', 'DELETE', 4);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'View', 'View', 'SELECT', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0010', 'Append', 'View', 'INSERT', 2);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0100', 'Change', 'View', 'UPDATE', 3);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('1000', 'Remove', 'View', 'DELETE', 4);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Run_P', 'Procedure', 'EXECUTE', 1);
INSERT INTO "auth"."permission" ("binaryCode", "name_", "objectType", "privilege", "order_") VALUES ('0001', 'Run_F', 'Function', 'EXECUTE', 1);
CREATE TABLE "auth"."user" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_id_seq"'::regclass) ,
 "password_" TEXT ,
 "last_login" TIMESTAMP WITH TIME ZONE ,
 "date_joined" TIMESTAMP WITH TIME ZONE DEFAULT now() ,
 "is_active" BOOLEAN NOT NULL DEFAULT 'False'::BOOLEAN ,
 "mustChangePassword" BOOLEAN DEFAULT 'True'::BOOLEAN ,
 "username" VARCHAR(150) NOT NULL UNIQUE ,
 "first_name" VARCHAR(30) ,
 "last_name" VARCHAR(30) ,
 "email" VARCHAR(254) ,
 "phone" VARCHAR(15) ,
 "rolename" NAME UNIQUE ,
 CONSTRAINT "user_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user" OWNER TO postgres;
ALTER SEQUENCE "auth"."user_id_seq" OWNED BY "auth"."user"."pk";
CREATE TABLE "enterprise"."accountingMethods" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."accountingMethods_id_seq"'::regclass) ,
 "name_" VARCHAR(50) NOT NULL ,
 CONSTRAINT "accountingMethods_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."accountingMethods" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."accountingMethods_id_seq" OWNED BY "enterprise"."accountingMethods"."pk";
INSERT INTO "enterprise"."accountingMethods" ("pk", "name_") VALUES (1, 'الفرنسية');
CREATE TABLE "enterprise"."allowedSocialMedia" (
 "platform_" VARCHAR(30) NOT NULL ,
 CONSTRAINT "allowedSocialMedia_pkey" PRIMARY KEY ("platform_")
);
ALTER TABLE "enterprise"."allowedSocialMedia" OWNER TO postgres;
CREATE TABLE "enterprise"."currency" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."currency_id_seq"'::regclass) ,
 "name_" VARCHAR(20) NOT NULL UNIQUE ,
 "symbol" VARCHAR(3) NOT NULL UNIQUE ,
 CONSTRAINT "currency_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."currency" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."currency_id_seq" OWNED BY "enterprise"."currency"."pk";
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (0, 'ليرة لبنانية', 'LBL');
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (1, 'دولار امريكي', 'USD');
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (2, 'يورو اوروبي', 'EUR');
CREATE TABLE "enterprise"."legalForm" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."legalForm_id_seq"'::regclass) ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 CONSTRAINT "legalForm_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."legalForm" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."legalForm_id_seq" OWNED BY "enterprise"."legalForm"."pk";
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('0', 'غير مخصص');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('1', 'مؤسسة تجارية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('2', 'شركة مساهمة لبنانية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('3', 'شركة محدودة المسؤولية');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('4', 'شركة التوصية البسيطة');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('5', 'شركة التضامن');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('6', 'شركة الأوف شور');
INSERT INTO "enterprise"."legalForm" ("pk", "name_") VALUES ('7', 'شركة الهولدنغ');
CREATE TABLE "enterprise"."opTypes" (
 "pk" SMALLINT NOT NULL ,
 "name_" VARCHAR(90) NOT NULL UNIQUE ,
 "prefix" VARCHAR(7) NOT NULL UNIQUE ,
 "appLable" VARCHAR(100) NOT NULL ,
 "filter" JSONB ,
 CONSTRAINT "opTypes_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."opTypes" OWNER TO postgres;
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8001', 'HRO', 'إذن خروج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "exit"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8002', 'HRT', 'أمر مهمة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "exit"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8003', 'HRAL', 'إجازة إدارية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8004', 'HRHL', 'إجازة صحية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8005', 'HRDL', 'إجازة وفاة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8006', 'HRML', 'إجازة زواج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8007', 'HRMaL', 'إجازة أمومة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8008', 'HRSL', 'إجازة بدون راتب', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8009', 'HRBL', 'إجازة خارج البلاد', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8013', 'HRAL_M', 'تعديل إجازة إدارية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8014', 'HRHL_M', 'تعديل إجازة صحية', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8015', 'HRDL_M', 'تعديل إجازة وفاة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8016', 'HRML_M', 'تعديل إجازة زواج', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8017', 'HRMaL_M', 'تعديل إجازة أمومة', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8018', 'HRSL_M', 'تعديل إجازة بدون راتب', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8019', 'HRBL_M', 'تعديل إجازة خارج البلاد', 'HRApp', '{"personTypes": "[1,2,3,4]","doc": "m_vacation"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8101', 'HRDT', 'نقل', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8102', 'HRDEnd', 'نهاية خدمة', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8103', 'HRDE', 'تعيين', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8104', 'HRDA', 'تكليف', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8105', 'HRDed', 'ترقين', 'HRApp', '{"personTypes": "[1,2,4]","doc": "decision"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8111', 'HRNT', 'مذكرة نقل', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8112', 'HRNEnd', 'مذكرة نهاية خدمة', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8113', 'HRNE', 'مذكرة تعيين', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8114', 'HRNA', 'مذكرة تكليف', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
INSERT INTO "enterprise"."opTypes" ("pk", "prefix", "name_", "appLable", "filter") VALUES ('8115', 'HRNed', 'مذكرة ترقين', 'HRApp', '{"personTypes": "[3]","doc": "note"}');
CREATE TABLE "enterprise"."post" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."post_id_seq"'::regclass) ,
 "chefLevel" SMALLINT ,
 "authority" BOOLEAN ,
 "active_" BOOLEAN ,
 "name_" VARCHAR(150) NOT NULL UNIQUE ,
 "post_role" TEXT ,
 CONSTRAINT "post_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."post" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."post_id_seq" OWNED BY "enterprise"."post"."pk";
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('0', 'FALSE', 'TRUE', 'غير ملحوظ في النظام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('1', 'TRUE', 'TRUE', 'طبيب رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('2', 'TRUE', 'TRUE', 'طبيب رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('3', 'FALSE', 'TRUE', 'محرر او كاتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('4', 'FALSE', 'TRUE', 'محاسب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('5', 'FALSE', 'TRUE', 'مراقب صحي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('6', 'FALSE', 'TRUE', 'مأمور صحي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('7', 'FALSE', 'TRUE', 'حاجب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('8', 'FALSE', 'TRUE', 'ممرضة قابلة قانونبة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('9', 'TRUE', 'TRUE', 'مهندس صحي رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('10', 'FALSE', 'TRUE', 'مراقب صحي للابنية');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('11', 'FALSE', 'TRUE', 'مراقب صحي للمياه و المجاري');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('12', 'FALSE', 'TRUE', 'مراقب صحي للمحلات المصنفة و الاندية و المسابح');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('13', 'TRUE', 'TRUE', 'رئيس دائرة المعلوماتية');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('14', 'FALSE', 'TRUE', 'مهندس نظام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('15', 'FALSE', 'TRUE', 'محلل مبرمج');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('16', 'FALSE', 'TRUE', 'مبرمج');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('17', 'FALSE', 'TRUE', 'اخصائي فني');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('18', 'FALSE', 'TRUE', 'مدخل معلومات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('19', 'FALSE', 'TRUE', 'مستكتب (اول او ثان)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('20', 'TRUE', 'TRUE', 'رئيس مصلحة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('21', 'TRUE', 'TRUE', 'رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('22', 'FALSE', 'TRUE', 'امين صندوق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('23', 'FALSE', 'TRUE', 'معاون امين صندوق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('24', 'TRUE', 'TRUE', 'رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('25', 'FALSE', 'TRUE', 'مراقب ضرائب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('26', 'FALSE', 'TRUE', 'امين مستودع');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('27', 'FALSE', 'TRUE', 'مراقب عام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('28', 'TRUE', 'TRUE', 'مهندس رئيس دائرة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('29', 'FALSE', 'TRUE', 'مراقب تنظيفات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('30', 'TRUE', 'TRUE', 'رئيس مراقبي التنظيفات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('31', 'FALSE', 'TRUE', 'مراقب صحي في فرقة مكافحة الحشرات');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('32', 'FALSE', 'TRUE', 'ملاحق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('33', 'FALSE', 'TRUE', 'جاب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('34', 'TRUE', 'TRUE', 'مدير مركز رئيس قسم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('35', 'FALSE', 'TRUE', 'امين محفوظات(محرر)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('36', 'TRUE', 'TRUE', 'مهندس رئيس مصلحة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('37', 'FALSE', 'TRUE', 'رسام');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('38', 'FALSE', 'TRUE', 'مدرب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('39', 'FALSE', 'TRUE', 'مناظر');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('40', 'FALSE', 'TRUE', 'مهندس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('41', 'FALSE', 'TRUE', 'معاون مهندس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('42', 'FALSE', 'TRUE', 'طوبوغراف');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('43', 'FALSE', 'TRUE', 'مراقب(محلف)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('44', 'FALSE', 'TRUE', 'مدرب ممتاز');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('45', 'FALSE', 'TRUE', 'مهندس زراعي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('46', 'FALSE', 'TRUE', 'حارس');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('47', 'FALSE', 'TRUE', 'خادم جنيناتي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('48', 'TRUE', 'TRUE', 'مهندس ميكانيك-رئيس مرآب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('49', 'FALSE', 'TRUE', 'سائق');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('50', 'FALSE', 'TRUE', 'سائق رئيس السائقين');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('51', 'FALSE', 'TRUE', 'سائق ونش و خلافه');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('53', 'FALSE', 'TRUE', 'مشرف عمال');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('54', 'FALSE', 'TRUE', 'عامل');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('55', 'FALSE', 'TRUE', 'معلم');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('56', 'FALSE', 'TRUE', 'مستكتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('57', 'FALSE', 'TRUE', 'كاتب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('58', 'FALSE', 'TRUE', 'مستكتب (كاتب)');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('59', 'TRUE', 'TRUE', 'المشرف');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('62', 'FALSE', 'TRUE', 'عمال الورشة الدائمة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('63', 'FALSE', 'TRUE', 'معلم او عامل اختصاص');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('64', 'FALSE', 'TRUE', 'شرطي');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('65', 'TRUE', 'TRUE', 'قائد الشرطة');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('66', 'FALSE', 'TRUE', 'متدرب');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('67', 'FALSE', 'TRUE', 'معاون امين مستودع');
INSERT INTO "enterprise"."post" ("pk", "authority", "active_", "name_") VALUES ('68', 'TRUE', 'TRUE', 'رئيس المراقبين الصحيين');
CREATE TABLE "machine"."type" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('machine."type_id_seq"'::regclass) ,
 "company" VARCHAR(25) NOT NULL UNIQUE ,
 "parameters" JSONB ,
 CONSTRAINT "type_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "machine"."type" OWNER TO postgres;
ALTER SEQUENCE "machine"."type_id_seq" OWNED BY "machine"."type"."pk";
CREATE TABLE "person"."person" (
 "pk" BIGINT NOT NULL DEFAULT nextval('person."person_id_seq"'::regclass) ,
 "active_" BOOLEAN NOT NULL DEFAULT 'True'::BOOLEAN ,
 "nickname" VARCHAR(15) ,
 "name_" VARCHAR(150) UNIQUE ,
 "social_media" jsonb ,
 "gender" CHAR ,
 CONSTRAINT "person_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."person" OWNER TO postgres;
CREATE TABLE "person"."personType" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('person."personType_id_seq"'::regclass) ,
 "initial_acc" CHAR(5) ,
 "description" VARCHAR(20) NOT NULL UNIQUE ,
 CONSTRAINT "personType_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."personType" OWNER TO postgres;
ALTER SEQUENCE "person"."personType_id_seq" OWNED BY "person"."personType"."pk";
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('1', 'موظف', '42001');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('2', 'متعاقد', '42002');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('3', 'عامل', '42003');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('4', 'شرطي', '42004');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('5', 'عامل فاتورة', '46191');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('6', 'مستشار', '46192');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('7', 'عضو مجلس', '46193');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('8', 'مسوق', '42005');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('9', 'زبون', '41100');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('10', 'مورد', '40100');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('11', 'مورد اعباء', '46590');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('12', 'مورد معاملات', '46591');
INSERT INTO "person"."personType" ("pk", "description", "initial_acc") VALUES ('13', 'شريك', '45100');
CREATE TABLE "enterprise"."enterprise" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."enterprise_id_seq"'::regclass) ,
 "financial_number" BIGINT UNIQUE ,
 "commercial_register_number" BIGINT UNIQUE ,
 "official_legalform_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "legalform_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "enterprise_accounting_level" BOOLEAN ,
 "social_media" VARCHAR(30)[] ,
 "email" VARCHAR(50) ,
 "TVA_registartion_number" VARCHAR(20) UNIQUE ,
 "phone" VARCHAR(18) ,
 "fax" VARCHAR(18) ,
 "website" VARCHAR(100) ,
 "name_" VARCHAR(200) NOT NULL UNIQUE ,
 "address" TEXT ,
 "logo" BYTEA ,
 CONSTRAINT "enterprise_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."enterprise" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."enterprise_id_seq" OWNED BY "enterprise"."enterprise"."pk";
INSERT INTO "enterprise"."enterprise" ("pk", "official_legalform_id", "legalform_id", "name_") VALUES (0, 0, 0, 'TEST');
CREATE TABLE "enterprise"."branch" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."branch_id_seq"'::regclass) ,
 "enterprise_id" SMALLINT NOT NULL REFERENCES "enterprise"."enterprise" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "base_currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "tax_currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "emp_vacation_days_per_year" SMALLINT ,
 "emp_vacation_years" SMALLINT ,
 "level_" SMALLINT NOT NULL ,
 "parent_id" SMALLINT REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "prefix" CHARACTER(2) UNIQUE ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 "active_" BOOLEAN NOT NULL DEFAULT 'True'::BOOLEAN ,
 CONSTRAINT "branch_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."branch" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."branch_id_seq" OWNED BY "enterprise"."branch"."pk";
INSERT INTO "enterprise"."branch" ("pk", "enterprise_id", "base_currency_id", "tax_currency_id", "emp_vacation_days_per_year", "emp_vacation_years", "prefix", "name_", "level_") VALUES (0, 0, 0, 0, 20, 3, 'B0', 'Main Branch', 0);
CREATE TABLE "enterprise"."department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."department_id_seq"'::regclass) ,
 "code_" SMALLINT NOT NULL ,
 "level_" SMALLINT NOT NULL ,
 "active_" BOOLEAN DEFAULT 'True'::BOOLEAN ,
 "parent_id" SMALLINT REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED,
 "prefix" CHARACTER(6) UNIQUE ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 "dep_role" TEXT ,
 CONSTRAINT "department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."department" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."department_id_seq" OWNED BY "enterprise"."department"."pk";
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('1', '0', '0', 'TRUE', NULL, NULL, 'ROOT');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('2', '1', '1', 'TRUE', '1', 'DMA', 'دائرة المعلوماتية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('3', '2', '1', 'TRUE', '1', 'MSI', 'مصلحة الشؤون الادارية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('4', '3', '1', 'TRUE', '1', 'MHN', 'مصلحة الهندسة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('5', '4', '1', 'TRUE', '1', 'MMA', 'المصلحة المالية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('6', '5', '1', 'TRUE', '1', 'DSH', 'دائرة الصحة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('7', '6', '1', 'TRUE', '1', 'SHR', 'الشرطة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('8', '7', '1', 'TRUE', '1', 'DNM', 'دائرة النظافة العامة و المكافحة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('9', '8', '1', 'TRUE', '1', 'MRA', 'المراقبة العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('10', '9', '1', 'TRUE', '1', 'OUT', 'خارج الملاك الاداري');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('11', '10', '1', 'TRUE', '1', 'MIS', 'مكتب استقبال+');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('12', '21', '2', 'TRUE', '3', 'DKK', 'دائرة القضايا القانونية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('13', '22', '2', 'TRUE', '3', 'DTF', 'دائرة التفتيش');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('14', '23', '2', 'TRUE', '3', 'DAM', 'دائرة امانة المجلس البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('15', '24', '2', 'TRUE', '3', 'DSA', 'دائرة الشؤون الادارية و العلاقات العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('16', '30', '2', 'TRUE', '4', 'DIM', 'الدائرة الادارية و المحاسبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('17', '31', '2', 'TRUE', '4', 'DMG', 'دائرة المجارير');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('18', '32', '2', 'TRUE', '4', 'DIS', 'دائرة الاستملاك');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('19', '33', '2', 'TRUE', '4', 'DDT', 'دائرة الدروس و التجميل');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('20', '34', '2', 'TRUE', '4', 'DMB', 'دائرة المباني');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('21', '35', '2', 'TRUE', '4', 'DMR', 'دائرة المراقبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('22', '36', '2', 'TRUE', '4', 'DTN', 'دائرة التنفيذ');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('23', '41', '2', 'TRUE', '5', 'DTH', 'دائرة التحصيل');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('24', '42', '2', 'TRUE', '5', 'KTS', 'قسم التصفية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('25', '43', '2', 'TRUE', '5', 'KKH', 'قسم الخزينة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('26', '44', '2', 'TRUE', '5', 'KMR', 'قسم المحاسبة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('27', '45', '2', 'TRUE', '5', 'KSR', 'قسم الصرفيات');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('28', '46', '2', 'TRUE', '5', 'KLW', 'قسم اللوازم');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('29', '47', '2', 'TRUE', '5', 'KAB', 'قسم الاملاك البلدية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('30', '48', '2', 'TRUE', '5', 'DTT', 'دائرة التحقق و تسجيل العقود');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('31', '51', '2', 'TRUE', '6', 'KSI', 'قسم الصحة و الاسعاف');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('32', '52', '2', 'TRUE', '6', 'KHS', 'قسم الهندسة الصحية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('33', '53', '2', 'TRUE', '6', 'KWI', 'قسم الوقاية و الارشاد');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('34', '73', '2', 'TRUE', '8', NULL, 'دائرة النظافة - مراقب +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('35', '74', '2', 'TRUE', '8', NULL, 'براحات +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('36', '75', '2', 'TRUE', '8', NULL, 'دائرة النظافة - عمال +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('37', '241', '3', 'TRUE', '15', 'KDI', 'قسم الديوان');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('38', '242', '3', 'TRUE', '15', 'KMM', 'قسم الموظفين و المحفوظات');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('39', '243', '3', 'TRUE', '15', 'KAA', 'قسم العلاقات العامة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('40', '244', '3', 'TRUE', '15', 'KMS', 'قسم المركز الثقافي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('41', '331', '3', 'TRUE', '19', 'KDT', 'قسم الدروس و التنظيم و التخطيط');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('42', '332', '3', 'TRUE', '19', 'KSY', 'قسم السير');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('43', '361', '3', 'TRUE', '22', 'KHM', 'قسم الحدائق العامة و الملعب البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('44', '362', '3', 'TRUE', '22', 'KMB', 'قسم المرآب البلدي');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('45', '363', '3', 'TRUE', '22', 'KAM', 'قسم الاشغال بالامانة و الاعمال الملزمة');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('46', '364', '3', 'TRUE', '22', 'KAI', 'قسم الاعمال الامتيازية');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('47', '365', '3', 'TRUE', '22', NULL, 'تنفيذ - الورشة الدائمة +');
INSERT INTO "enterprise"."department" ("pk", "code_", "level_", "active_", "parent_id", "prefix", "name_") VALUES ('48', '366', '3', 'TRUE', '22', NULL, 'تنفيذ - ورشة طوارئ+');
CREATE TABLE "hr"."decision" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decision_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 "ref_" CHARACTER VARYING(10) NOT NULL ,
 "editor" CHARACTER VARYING(60) NOT NULL ,
 CONSTRAINT "decision_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decision" OWNER TO postgres;
ALTER SEQUENCE "hr"."decision_id_seq" OWNED BY "hr"."decision"."pk";
CREATE TABLE "hr"."emp" (
 "pk" BIGINT NOT NULL DEFAULT nextval('person."person_id_seq"'::regclass) REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "social_security" BIGINT UNIQUE ,
 "financial_number" BIGINT UNIQUE ,
 "birth_date" DATE ,
 "start_date" DATE ,
 "end_date" DATE ,
 "first_name" VARCHAR(50) ,
 "father_name" VARCHAR(50) ,
 "family_name" VARCHAR(50) ,
 "mother_fullname" VARCHAR(100) ,
 CONSTRAINT "emp_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."emp" OWNER TO postgres;
CREATE TABLE "enterprise"."accountCategory" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."accountCategory_id_seq"'::regclass) ,
 "name_" VARCHAR(100) NOT NULL ,
 "accountingMethods_id" SMALLINT NOT NULL REFERENCES "enterprise"."accountingMethods" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "accountCategory_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."accountCategory" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."accountCategory_id_seq" OWNED BY "enterprise"."accountCategory"."pk";
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (1, 'الفئة الاولى - حسابات الرسائل الدائمة', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (2, 'الفئة الثانية - حسابات الاصول الثابتة', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (3, 'الفئة الثالثة - المخزون وقيد الصنع', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (4, 'الفئة الرابعة - حسابات الذمم', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (5, 'الفئة الخامسة - الحسابات المالية', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (6, 'الفئة السادسة - حسابات الاعباء', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (7, 'الفئة السابعة - حسابات الايرادات', 1);
CREATE TABLE "auth"."appObjects" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."appObjects_id_seq"'::regclass) ,
 "app_id" SMALLINT NOT NULL REFERENCES "auth"."app" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "objectType" auth.objecttype NOT NULL ,
 "name_" TEXT NOT NULL ,
 "schema_" TEXT NOT NULL ,
 "options" JSONB ,
 CONSTRAINT "appObjects_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."appObjects" OWNER TO postgres;
ALTER SEQUENCE "auth"."appObjects_id_seq" OWNED BY "auth"."appObjects"."pk";
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('currency', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "name_", "symbol"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('accountingMethods', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('allowedSocialMedia', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["platform_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('legalForm', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('post', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "chefLevel", "authority", "active_", "name_", "post_role"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('department', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "code_", "level_", "active_", "parent_id", "prefix", "name_", "dep_role"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('accountCategory', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_", "accountingMethods_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('opTypes', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_", "prefix", "appLable", "filter"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('enterprise', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "financial_number", "commercial_register_number", "official_legalform_id", "legalform_id", "enterprise_accounting_level", "social_media", "email", "TVA_registartion_number", "phone", "fax", "website", "name_", "address", "logo"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "enterprise_id", "base_currency_id", "tax_currency_id", "emp_vacation_days_per_year", "emp_vacation_years", "level_", "parent_id", "prefix", "name_", "active_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('financialYear', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "start_", "end_", "accounting_info"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_department', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "department_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_department_post', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_department_id", "post_id", "legal_capacity", "occupied"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('department_optypes', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["department_id", "opType_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_currencies', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["branch_id", "currency_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('chartOfAccountRef', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "accountCategory_id", "num_", "description"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_acc', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["branch_id", "chartOfAccountRef_id", "allowRooting"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('person', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "active_", "nickname", "name_", "social_media", "gender"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('personContact', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "person_id", "name_", "post", "phone"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('personType', 'person', 'Table', 5, '{"sys_table": true, "fieldlist": ["pk", "initial_acc", "description"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('person_personTypes', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["person_id", "personType_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('financialInfo', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "financial_number", "commercial_register_number", "TVA_registration_number", "capital", "legalForm"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('address', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "phone", "fax", "email", "website", "address"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('emp', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "social_security", "financial_number", "birth_date", "start_date", "end_date", "first_name", "father_name", "family_name", "mother_fullname"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decision', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "opType_id", "personType_id", "date_", "ref_", "editor"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionTransfer', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "old_department_id", "old_post_id", "new_department_id", "new_post_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionEndOfService', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "date_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionEmployment', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "department_id", "post_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionAssignment', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "old_department_id", "old_post_id", "new_department_id", "new_post_id", "old_ops"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "decision_id", "emp_id", "opType_id", "daysCount", "ownerDepartment_id", "editowner_id", "fromDate", "toDate", "state", "editDate", "note"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "vacationDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationMDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "decision_id", "ownerDepartment_id", "editowner_id", "editDate", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationMAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "vacationDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('exitDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "opType_id", "minutesCount", "ownerDepartment_id", "editowner_id", "editDate", "fromDate", "toDate", "state", "note"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('exitDocsAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "exitDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('detailAddress', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "floor_", "nationality", "state_", "city", "region", "street", "building_project", "building", "addition_info"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionExtend', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "date_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('type', 'machine', 'Table', 6, '{"sys_table": true, "fieldlist": ["pk", "company", "parameters"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machine', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["pk", "type_id", "branch_id", "recordsCount", "lastRead", "name_", "conf"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('attendance', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["machine_id", "att_id", "attendance_date", "created", "attOptions"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machinelogs', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["pk", "machine_id", "start_date", "end_date", "records_count", "created", "notes"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machine_emp', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["machine_id", "att_id", "emp_id", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('permission', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["name_", "privilege", "binaryCode", "order_", "objectType"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('app', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "appLable", "default_", "active_", "personTypeList"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "password_", "last_login", "date_joined", "is_active", "mustChangePassword", "username", "first_name", "last_name", "email", "phone", "rolename"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "app_id", "name_", "description", "rolename"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('appObjects', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "app_id", "objectType", "name_", "schema_", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_group', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["user_id", "group_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_permission', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "appObjects_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_permission', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "group_id", "appObjects_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('appObjects_allowed_permission', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["appObject_id", "permission_name_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_permission_detail', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_permission_id", "allow", "permission_name_id", "fieldlist"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_permission_detail', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "group_permission_id", "allow", "permission_name_id", "fieldlist"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_branch', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "branch_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('userToken', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "revoked", "expires", "created", "token_text"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_personType', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["personType_id", "group_id", "allow"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('audit_user', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "user_id", "appObject_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('audit_log', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "user_id", "logged_at", "table_name", "row_id", "action_type", "old_data", "new_data"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_department', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_branch_id", "department_id", "allow"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_department_optypes', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["user_department_id", "optype_id", "allow"]}')
 RETURNING pk;
CREATE TABLE "auth"."user_branch" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_branch_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "user_branch_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_branch" ADD CONSTRAINT "user_branch_user_id_branch_id_unique" UNIQUE ("user_id", "branch_id");
ALTER TABLE "auth"."user_branch" OWNER TO postgres;
CREATE TABLE "auth"."group" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."group_id_seq"'::regclass) ,
 "app_id" SMALLINT NOT NULL REFERENCES "auth"."app" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "name_" VARCHAR(150) NOT NULL UNIQUE ,
 "description" TEXT UNIQUE ,
 "rolename" NAME NOT NULL UNIQUE ,
 CONSTRAINT "group_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group" OWNER TO postgres;
ALTER SEQUENCE "auth"."group_id_seq" OWNED BY "auth"."group"."pk";
CREATE TABLE "machine"."machine" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('machine."machine_id_seq"'::regclass) ,
 "type_id" SMALLINT NOT NULL REFERENCES "machine"."type" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "branch_id" SMALLINT ,
 "recordsCount" BIGINT ,
 "lastRead" TIMESTAMP WITHOUT TIME ZONE ,
 "name_" VARCHAR(25) NOT NULL UNIQUE ,
 "conf" JSONB UNIQUE ,
 CONSTRAINT "machine_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "machine"."machine" OWNER TO postgres;
ALTER SEQUENCE "machine"."machine_id_seq" OWNED BY "machine"."machine"."pk";
CREATE TABLE "hr"."vacationMDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationMDocs_id_seq"'::regclass) ,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "editowner_id" SMALLINT NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "options" JSONB NOT NULL ,
 CONSTRAINT "vacationMDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationMDocs" OWNER TO postgres;
ALTER SEQUENCE "hr"."vacationMDocs_id_seq" OWNED BY "hr"."vacationMDocs"."pk";
CREATE TABLE "hr"."vacationDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationDocs_id_seq"'::regclass) ,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "daysCount" SMALLINT NOT NULL ,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "editowner_id" SMALLINT NOT NULL ,
 "fromDate" DATE NOT NULL ,
 "toDate" DATE NOT NULL ,
 "state" hr.docstate NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "note" TEXT NOT NULL ,
 CONSTRAINT "vacationDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationDocs" OWNER TO postgres;
ALTER SEQUENCE "hr"."vacationDocs_id_seq" OWNED BY "hr"."vacationDocs"."pk";
CREATE TABLE "hr"."exitDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."exitDocs_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "minutesCount" SMALLINT ,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "editowner_id" SMALLINT NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE ,
 "fromDate" TIME WITHOUT TIME ZONE ,
 "toDate" TIME WITHOUT TIME ZONE ,
 "state" hr.docstate DEFAULT 'Pending'::hr.docstate ,
 "note" TEXT ,
 CONSTRAINT "exitDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."exitDocs" OWNER TO postgres;
ALTER SEQUENCE "hr"."exitDocs_id_seq" OWNED BY "hr"."exitDocs"."pk";
CREATE TABLE "enterprise"."branch_department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."branch_department_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "branch_department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."branch_department" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."branch_department_id_seq" OWNED BY "enterprise"."branch_department"."pk";
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('1', '1', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('2', '2', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('3', '6', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('4', '31', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('5', '33', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('6', '32', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('7', '8', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('8', '35', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('9', '36', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('10', '9', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('11', '5', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('12', '24', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('13', '27', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('14', '25', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('15', '26', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('16', '30', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('17', '29', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('18', '28', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('19', '23', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('20', '3', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('21', '14', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('22', '13', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('23', '15', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('24', '37', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('25', '38', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('26', '12', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('27', '40', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('28', '4', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('29', '16', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('30', '21', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('31', '19', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('32', '41', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('33', '42', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('34', '22', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('35', '45', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('36', '46', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('37', '43', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('38', '44', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('39', '20', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('40', '18', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('41', '17', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('43', '7', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('44', '47', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('45', '34', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('46', '48', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('47', '11', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('48', '39', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('49', '10', '0');
CREATE TABLE "enterprise"."chartOfAccountRef" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."chartOfAccountRef_id_seq"'::regclass) ,
 "accountCategory_id" SMALLINT NOT NULL REFERENCES "enterprise"."accountCategory" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "num_" VARCHAR(6) NOT NULL ,
 "description" VARCHAR(250) NOT NULL ,
 CONSTRAINT "chartOfAccountRef_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."chartOfAccountRef" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."chartOfAccountRef_id_seq" OWNED BY "enterprise"."chartOfAccountRef"."pk";
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('1', '1', '1', 'الفئة الاولى - حسابات الرساميل الدائمة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('2', '1', '10', '                       - رأس المال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('3', '1', '101', '                      رأس المال (للشركة او للشخص)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('4', '1', '1011', '                     رأس المال المكتتب غير المستدعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('5', '1', '1012', '                     رأس المال المكتتب, المستدعي غير المدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('6', '1', '1013', '                     رأس المال المكتتب, المستدعي والمدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('7', '1', '102', '                      علاوات الاصدار والاندماج والمقدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('8', '1', '1021', '                     علاوات الاصدار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('9', '1', '1022', '                     علاوات الاندماج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('10', '1', '1023', '                     علاوات المقدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('11', '1', '1024', '                     علاوات تحويل السندات الى اسهم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('12', '1', '103', '                      فروقات اعادة التخمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('13', '1', '1031', '       فروقات اعادة تخمين الاصول الثابتة غير القابلة للاستهلاك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('14', '1', '1035', '                     فروقات اعادة تخمين الاصول الثابتة القابلة للاستهلاك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('15', '1', '109', '                      الحساب الشخصي لصاحب المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('16', '1', '11', '                       - الاحتياطات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('17', '1', '111', '                      احتياطي قانوني');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('18', '1', '112', '                      احتياطيات نظامية وتعاقدية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('19', '1', '119', '                      احتياطيات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('20', '1', '12', '                       - نتائج سابقة مدورة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('21', '1', '121', '                      نتائج سابقة مدورة دائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('22', '1', '125', '                      نتائج سابقة مدورة مدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('23', '1', '13', '                        - النتيجة الصافية للدورة المالية (ربح او خسارة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('24', '1', '131', '                       الهامش التجاري القائم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('25', '1', '132', '                       القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('26', '1', '133', '                       الفائض غير الصافي للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('27', '1', '134', '                       نتيجة الاستثمار (خارج الاعباء والايرادات المالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('28', '1', '135', '                       النتيجة الجارية قبل الضريبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('29', '1', '136', '                       النتيجة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('30', '1', '138', '                       نتيجة الدورة - ارباح');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('31', '1', '139', '                       نتيجة الدوره - خسائر');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('32', '1', '14', '                        - اعانات للتوظيفات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('33', '1', '141', '                       اعانات للتوظيفات مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('34', '1', '145', '                       اعانات للتوظيفات محولة للنتائج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('35', '1', '15', '                        - مؤونات لمواجهة اخطار واعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('36', '1', '151', '                       مؤونات لمواجهة اخطار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('37', '1', '1511', '                      مؤونات للمنازعات والاحتمالات المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('38', '1', '1512', '                      مؤونات لقاء الضمانات المعطاة للزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('39', '1', '1513', '                      مؤونات خسائر سعر الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('40', '1', '1514', '                      مؤونات خسائر على عقود لاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('41', '1', '1515', '                      مؤونات الغرامات والجزاءات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('42', '1', '155', '                       مؤونات لمواجهة اعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('43', '1', '1551', '                      مؤونات الاعباء الواجب توزيعها على عدة دورات مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('44', '1', '1552', '                      مؤونات لمواجهة معاشات التقاعد والموجبات المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('45', '1', '1553', '                      مؤونات للضرائب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('46', '1', '16', '                        - دوين مالية طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('47', '1', '161', '                       قروض لقاء سندات دين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('48', '1', '162', '                       قروض من مؤسسات التسليف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('49', '1', '168', '                       قروض وديون مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('50', '1', '1681', '                      اوراق دفع ناجمة عن شراء المؤسسة التجارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('51', '1', '1682', '                      ودائع وكفالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('52', '1', '1683', '                      سلفات  الدولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('53', '1', '1684', '                      دخل لمدى الحيارة متراكم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('54', '1', '1689', '                      ديون اخرى طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('55', '1', '18', '                        -حسابات ارتباط المؤسسات والفروع والشركات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('56', '1', '181', '                       حسابات ارتباط المؤسسات والفروع وشركات المشاركة(حساب لكل مؤسسة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('57', '1', '1811', '                      رصيد مدور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('58', '1', '1815', '                      حركات الدورة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('59', '1', '186', '                       الاموال والخدمات المتبادلة بين الفروع ( اعباء )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('60', '1', '187', '                       الاموال والخدمات المتبادلة بين الفروع ( ايرادات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('61', '1', '19', '                       - حسابات تجميع الاعباء والايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('62', '1', '191', '                      تحديد الهامش التجاري القائم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('63', '1', '192', '                      تحديد القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('64', '1', '193', '                      تحديد الفائض غير الصافي للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('65', '1', '194', '                      تحديد نتيجة الاستثمار (قبل الاعباء والايرادات المالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('66', '1', '195', '                      تحديد النتيجة الجارية قبل الضريبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('67', '1', '196', '                      تحديد النتيجة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('68', '1', '197', '                      تحديد نتيجة الدورة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('69', '2', '2', '               الفئة الثانية - حسابات الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('70', '2', '21', '                       - الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('71', '2', '211', '                      المؤسسة التجارية ( الخلو , الشهرة,  الزبائن...)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('72', '2', '212', '                      مصاريف التأسيس');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('73', '2', '213', '                      مصاريف البحوث والتطوير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('74', '2', '214', '                      براءات الاختراع - الاجازات  العلامات والقيم المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('75', '2', '219', '                      اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('76', '2', '2191', '               ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('77', '2', '2198', '                     سلفات ودفعات على حساب تقديم   اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('78', '2', '22', '                       - الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('79', '2', '221', '                      الاراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('80', '2', '2211', '                     الاراضي الفراغ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('81', '2', '2212', '                     الاراضي المبنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('82', '2', '2213', '                     الاراضي برسم الاستثمار الجوفي ( مناجم - مقالع.. )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('83', '2', '2214', '                     استصلاح وتنظيم الاراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('84', '2', '223', '                      الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('85', '2', '2231', '                     الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('86', '2', '2232', '                     التجهيزات العامة - استصلاح وتنظيم الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('87', '2', '2233', 'انشاءات البنية التحتية (سدود , مدارج المطارات ... )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('88', '2', '2234', '                     انشاءات على اراضي الغير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('89', '2', '224', '                      التجهيزات الفنية, والالات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('90', '2', '2241', '                     تجهيزات متخصصة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('91', '2', '2242', '                     تجهيزات ذات طبيعة خاصة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('92', '2', '2243', '                     المعدات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('93', '2', '2244', '                     الادوات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('94', '2', '225', '                      اليات النقل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('95', '2', '226', '                      اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('96', '2', '2261', '                     تجهيزات عامة, استصلاحات وتحسينات مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('97', '2', '2262', '                     ادوات مكتبية ومعلوماتية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('98', '2', '2263', '                     اثاث');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('99', '2', '2264', '                     استثمارات زراعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('100', '2', '2265', '                     عبوات قابلة لاعادة الاستعمال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('101', '2', '227', '                      اصول ثابتة مادية قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('102', '2', '2271', '                     اراضي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('103', '2', '2273', '                     ابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('104', '2', '2274', '                     تجهيزات فنية, معدات وادوات صناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('105', '2', '2276', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('106', '2', '228', '                      سلفات ودفعات على حساب  شراء اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('107', '2', '25', '                       - الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('108', '2', '251', '                      سندات مشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('109', '2', '252', '                      ذمم مدينة مرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('110', '2', '253', '                      سندات اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('111', '2', '2531', '                     سندات ملكية ( اسهم, حصص  شراكة )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('112', '2', '2535', '                     سندات دين (سندات, قسائم )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('113', '2', '255', '                      قروض طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('114', '2', '2551', '                     قروض للشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('115', '2', '2552', '                     قروض للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('116', '2', '2558', '                     قروض اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('117', '2', '259', '                      ذمم مدينة اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('118', '2', '28', '                       استهلاكات الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('119', '2', '280', '                      استهلاكات المؤسسة التجارية(الشهرة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('120', '2', '281', '                      استهلاكات الاصول الثابتة غير المادية الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('121', '2', '2811', '                     مصاريف التأسيس');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('122', '2', '2812', '                     مصاريف البحوث والتطوير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('123', '2', '2813', '                     براءات الاختراع - الاجازات و القيم المماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('124', '2', '2819', '                     اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('125', '2', '282', '                      استهلاكات الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('126', '2', '2821', '                     اراضي برسم الاستثمار الجوفي ( مناجم - مقالع... )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('127', '2', '2823', '                     الابنية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('128', '2', '2824', '                     التجهيزات الفنية المعدات والادوات الصناعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('129', '2', '2825', '                     آليات النقل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('130', '2', '2826', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('131', '2', '29', '                       مؤونات هبوط اسعار الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('132', '2', '290', '                      مؤونات هبوط قيمة (المؤسسة التجارية )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('133', '2', '291', '                      مؤونات هبوط قيم الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('134', '2', '2911', '                     علامات وقيم مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('135', '2', '2919', '                     اصول ثابتة غير مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('136', '2', '292', '                      مؤونات هبوط اسعار الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('137', '2', '2921', '                     الاراضي (غير اراضي الاستثمار الجوفي )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('138', '2', '2926', '                     اصول ثابتة مادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('139', '2', '295', '                      مؤونات هبوط اسعار الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('140', '2', '2951', '                     سندات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('141', '2', '2952', '                     ذمم مدينة مرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('142', '2', '2953', '                     سندات اخرى مجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('143', '2', '2955', '                     قروض ممنوحة طويلة ومتوسطة الاجل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('144', '2', '2959', '                     ذمم مدينة اخرى مجمده');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('145', '3', '3', '                   الفئة الثالثة - المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('146', '3', '31', '                       مواد اولية او استهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('147', '3', '311', '                      مواد اولية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('148', '3', '315', '                      مواد ولوازم استهلاكية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('149', '3', '33', '                       قيد الصنع (سلغ واشغال وخدمات )');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('150', '3', '331', '                      منتجات قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('151', '3', '332', '                      اشغال قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('152', '3', '335', '                      دراسات قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('153', '3', '336', '                      خدمات قيد التقديم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('154', '3', '35', '                       منتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('155', '3', '351', '                      منتجات وسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('156', '3', '355', '                      منتجات تامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('157', '3', '358', '                      فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('158', '3', '37', '                       البضائع (المعدة للبيع)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('159', '3', '371', '                      ....................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('160', '3', '372', '                      ....................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('161', '3', '39', '                       مؤونات هبوط اسعار المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('162', '3', '391', '                      مؤونات هبوط اسعار مخزون المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('163', '3', '393', '                      مؤونات هبوط اسعار الانتاج قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('164', '3', '395', '                      مؤونات هبوط اسعار الانتاج المخزون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('165', '3', '397', '                      مؤونات هبوط اسعار البضاعة المخزونة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('166', '4', '4', 'الفئة الرابعة - حسابات الذمم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('167', '4', '40', '                       الموردون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('168', '4', '401', '                      ذمم دائنة (موردو الاستثمار)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('169', '4', '4011', '                     فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('170', '4', '4015', '                     اوراق دفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('171', '4', '4018', '                     فواتير لم تصل بعد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('172', '4', '4019', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('173', '4', '403', '                      موردو الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('174', '4', '4031', '                     فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('175', '4', '4035', '                     اوراق دفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('176', '4', '4038', '                     فواتير لم تصل بعد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('177', '4', '4039', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('178', '4', '409', '                      سلفات ودفعات على حساب طلبيات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('179', '4', '41', '                       الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('180', '4', '411', '                      فواتير الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('181', '4', '4111', '                     زبائن عاديون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('182', '4', '4115', '                     زبائن مشكوك بتحصيل ديونهم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('183', '4', '4119', '                     حسومات ممنوحة من المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('184', '4', '413', '                      اوراق قبض - زبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('185', '4', '415', '                      ذمم مدينة على اشغال لم تبلغ مرحلة تحرير فواتير بها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('186', '4', '418', '                      فواتير قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('187', '4', '419', '                      سلفات ومقبوضات على حساب طلبيات قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('188', '4', '42', '                       المستخدمون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('189', '4', '421', '                      مدفوعات متوجبة للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('190', '4', '428', '                      حسابات المستخدمين المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('191', '4', '4281', '                     سلفات ودفعات للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('192', '4', '4282', '                     حجوزات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('193', '4', '43', '                       مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('194', '4', '431', '                      ذمم دائنة تجاه مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('195', '4', '4311', '                     تقديمات برسم الدفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('196', '4', '4315', '                     اوراق الدفع - مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('197', '4', '4318', '                     اعباء يجب لحظها - مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('198', '4', '438', '                      ذمم مدينة على مؤسسات الضمان الاجتماعي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('199', '4', '44', '                       الدولة والمؤسسات العامة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('200', '4', '441', '                      ضرائب متوجبة على الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('201', '4', '4411', '                     ضرائب ورسوم متوجبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('202', '4', '4415', '                     اوراق دفع - ضرائب ورسوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('203', '4', '4418', '                    - اعباء يجب لحظها - ضرائب ورسوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('204', '4', '442', 'الدولة والمؤسسات العامة - الضريبة على القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('205', '4', '4421', 'الضريبة على القيمة المضافة المدفوعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('206', '4', '44210', 'الضريبة على القيمة المضافة المدفوعة على المشتريات المستعملة فقط لعمليات تتيح حق الحسم.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('207', '4', '44211', 'الضريبة على القيمة المضافة المدفوعة على المشتريات المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('208', '4', '44212', 'الضريبة على القيمة المضافة المدفوعة على المشتريات التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('209', '4', '44213', 'الضريبة على القيمة المضافة المدفوعة على مشتريات الأصول الثابتة المستعملة فقط لعميات تتيح حق الحسم.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('210', '4', '44214', 'الضريبة على القيمة المضافة المدفوعة على مشتريات الأصول الثابتة المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('211', '4', '44215', 'الضريبة على القيمة المضافة المدفوعة على الأصول الثابتة التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('212', '4', '44216', 'الضريبة على القيمة المضافة المدفوعة على الأعباء المستعملة فقط لعمليات تتيح حق الحسم. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('213', '4', '44217', 'الضريبة على القيمة المضافة المدفوعة على الأعباء المستعملة فقط لعمليات تتيح حق الاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('214', '4', '44218', 'الضريبة على القيمة المضافة المدفوعة على الأعباء التي لا يمكن تحديد وجهة استعمالها.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('215', '4', '44219', 'الضريبة على القيمة المضافة المدفوعة على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('216', '4', '4422', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('217', '4', '44221', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) على المشتريات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('218', '4', '44222', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على المشتريات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('219', '4', '44223', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) مشتريات الأصول الثابتة. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('220', '4', '44224', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على مشتريات الأصول الثابتة. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('221', '4', '44225', 'الضريبة على القيمة المضافة القابلة للاسترداد (مادة 59) على الأعباء. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('222', '4', '44226', 'الضريبة على القيمة المضافة القابلة للاسترداد الجزئي (مادة 59) على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('223', '4', '4425', 'رصيد الضريبة على القيمة المضافة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('224', '4', '44251', 'الضريبة على القيمة المضافة المستحقة للدفع.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('225', '4', '44252', 'الضريبة على القيمة المضافة المدورة القابلة للاسترداد.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('226', '4', '4426', 'الضريبة على القيمة المضافة القابلة للحسم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('227', '4', '44261', 'الضريبة على القيمة المضافة القابلة للحسم على المشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('228', '4', '44262', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على المشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('229', '4', '44263', 'الضريبة على القيمة المضافة القابلة للحسم على مشتريات الأصول الثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('230', '4', '44264', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على مشتريات الأصول الثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('231', '4', '44265', 'الضريبة على القيمة المضافة القابلة للحسم على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('232', '4', '44266', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على الأعباء.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('233', '4', '44267', 'الضريبة على القيمة المضافة القابلة للحسم على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('234', '4', '44268', 'الضريبة على القيمة المضافة القابلة للحسم الجزئي على السلفات والدفعات على حساب طلبيات للاستثمار. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('235', '4', '4427', 'الضريبة على القيمة المضافة المحصلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('236', '4', '44270', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('237', '4', '44271', 'الضريبة على القيمة المضافة المحصلة على عمليات تقديم الخدمات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('238', '4', '44272', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال على الأسس النقدية. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('239', '4', '44273', 'الضريبة على القيمة المضافة المحصلة على عمليات تقديم الخدمات على الأسس النقدية.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('240', '4', '44274', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم المجوهرات المحتسبة ضريبتها على أساس هامش الربح الإجمالي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('241', '4', '44275', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال للسياح.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('242', '4', '44276', 'الضريبة على القيمة المضافة المحصلة على عمليات تسليم الأموال وتقديم الخدمات للذات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('243', '4', '44277', 'الضريبة على القيمة المضافة المحصلة على مبالغ مستحقة لغير المقيمين.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('244', '4', '44278', 'الضريبة على القيمة المضافة المحصلة على مبيع أصول ثابتة.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('245', '4', '44279', 'الضريبة على القيمة المضافة المحصلة على السلفات والمقبوضات على حساب طلبيات قيد التنفيذ.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('246', '4', '4428', 'لضريبة على القيمة المضافة القابلة للتسوية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('247', '4', '44281', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('248', '4', '442811', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة على السلفات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('249', '4', '442812', 'الضريبة على القيمة المضافة القابلة للتسوية المدينة على الحسابات الأخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('250', '4', '44282', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('251', '4', '442821', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة على السلفات.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('252', '4', '442822', 'الضريبة على القيمة المضافة القابلة للتسوية الدائنة على الحسابات الأخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('253', '4', '4429', ' الضريبة على القيمة المضافة المطلوب استردادها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('254', '4', '44291', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد فصلي. ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('255', '4', '44292', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد نصف سنوي.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('256', '4', '44293', 'الضريبة على القيمة المضافة المطلوب استردادها - استرداد سنوي.');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('257', '4', '44294', 'الضريبة على القيمة المضافة المطلوب استردادها - مادة 59 .');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('258', '4', '443', '                       ضرائب متوجبة خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('259', '4', '4431', '                     الضرائب على الارباح');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('260', '4', '445', '                      الدولة والمؤسسات العامة (ذمم دائنة اخرى)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('261', '4', '449', '                      الدولة والمؤسسات العامة (ذمم مدينة)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('262', '4', '4491', '                     اعانات مستحقة غير مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('263', '4', '4497', '                     ذمم مدينة اخرى (الدولة والمؤسسات العامة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('264', '4', '4498', '                     ايرادات مستحقة غير مقبوضة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('265', '4', '45', '                       الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('266', '4', '451', '                      حسابات الشركاء الجارية المدينة او الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('267', '4', '4511', '                     شركات شقيقة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('268', '4', '45111', '                           الشركة الام');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('269', '4', '45112', '                           الشركات التابعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('270', '4', '45113', '                           الشركات المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('271', '4', '45114', '                           الشركات الداخلة في عدة مجموعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('272', '4', '4515', '                     شركاء آخرون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('273', '4', '4518', '                     عمليات مشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('274', '4', '453', '                      انصبة ارباح برسم الدفع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('275', '4', '455', '                      ذمم دائنة اخرى للشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('276', '4', '4551', '                     حسابات المقدمات للشركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('277', '4', '4552', '                     رأس المال المقرر استرداده من الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('278', '4', '4557', '                     ذمم دائنة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('279', '4', '459', '                      ذمم الشركاء المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('280', '4', '4591', '                     رأس المال المكتتب غير المستدعى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('281', '4', '4592', '                     رأس المال المكتتب المستدعى وغير المدفوع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('282', '4', '4597', '                     ذمم مدينة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('283', '4', '46', '                       ذمم مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('284', '4', '461', '                      ذمم الاستثمار الدائنة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('285', '4', '4611', '                     ذمم دائنة متعلقة بالعبوات والمعدات الموضوعة بالامانة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('286', '4', '4619', '                     حسابات دائنة مختلفة - استثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('287', '4', '463', '                      اقساط برسم الدفع على اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('288', '4', '4631', '                     سندات مشاركة غير محرره');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('289', '4', '4633', '                     سندات اخرى مجمدة غير محرره');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('290', '4', '465', '                      دائنون مختلفون خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('291', '4', '4651', '                     ذمم دائنة على امتلاك سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('292', '4', '4652', '                     سندات الدين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('293', '4', '4653', '                     اقساط برسم الدفع على سندات  توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('294', '4', '4659', '                     دائنون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('295', '4', '468', '                      مدينون مختلفون للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('296', '4', '4681', '                     ذمم مدينة متعلقة بالعبوات والمعدات الواجب اعادتها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('297', '4', '4689', '                     حسابات اخرى مدينة مختلفة - للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('298', '4', '469', '                      مدينون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('299', '4', '4691', '                     ذمم مدينة على بيع اصول ثابتة وسندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('300', '4', '4699', '                     حسابات اخرى مدينة مختلفة - خارج الاستثمار -');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('301', '4', '47', '                       حسابات التسوية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('302', '4', '471', '                      الاعباء الواجب توزيعها على عدة دورات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('303', '4', '4711', '                     اعباء ما قبل الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('304', '4', '4712', '                     التصليحات الكبير الواجب استهلاكها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('305', '4', '4713', '                     علاوات تسديد السندات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('306', '4', '4719', '                     اعباء اخرى واجب توزيعها على عدة دورات مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('307', '4', '472', '                      اعباء محتسبة مسبقا');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('308', '4', '473', '                      ايرادات محتسبة مسبقا');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('309', '4', '475', '                      فروقات صرف  - خصوم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('310', '4', '4751', '                     الزيادة في الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('311', '4', '4752', '                     التدني في الذمم الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('312', '4', '4758', '                     فروقات معوضة بفرق الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('313', '4', '476', '                      فروقات صرف  - اصول');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('314', '4', '4761', '                    التدني في الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('315', '4', '4762', '                     الزيادة في الذمم الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('316', '4', '4768', '                     فروقات معوضة بفرق الصرف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('317', '4', '48', '                       الحسابات المؤقتة وقيد التسوية حسابات التوزيع الدوري للاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('318', '4', '481', '                             (اشتراكات) حسابات التوزيع الدوري للايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('319', '4', '482', '                             (اشتراكات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('320', '4', '483', '                      ...................');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('321', '4', '49', '                       مؤونات لمواجهة هبوط قيم حسابات الذمم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('322', '4', '491', '                      مؤونات هبوط قيم الذمم المدينة الزبائن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('323', '4', '495', '                      مؤونات هبوط قيم حسابات الشركاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('324', '4', '496', '                      مؤونات هبوط قيم حسابات الذمم المدينة المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('325', '4', '4968', '                     مدينون مختلفون - للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('326', '4', '4969', '                     مدينون مختلفون - خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('327', '5', '5', '       الفئة الخامسة - الحسابات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('328', '5', '50', '                       سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('329', '5', '501', '                      اسهم صادرة عن الشركة ومعاد شراؤها من قبلها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('330', '5', '502', '                      سندات تمنح حامليها حق الملكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('331', '5', '505', '                      سندات دين وقسائم صادرة عن الشركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('332', '5', '506', '                      سندات تمنح حامليها حقوق الدائنين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('333', '5', '51', '                       المؤسسات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('334', '5', '511', '                      شكات وقسائم برسم القبض');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('335', '5', '512', '                      بنوك');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('336', '5', '519', '                      مؤسسات التمويل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('337', '5', '53', '                       الصندوق');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('338', '5', '58', '                       التحويلات الداخلية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('339', '5', '59', '                       مؤونات هبوط اسعار سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('340', '6', '6', '    الفئة السادسة - حسابات الاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('341', '6', '60', '                       مشتريات البضاعة وقيمة التغيير في المخزون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('342', '6', '601', '                            مشتريات البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('343', '6', '6011', '                     البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('344', '6', '6012', '                     العبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('345', '6', '6018', '                     نفقات اضافية على شراء البضائع والعبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('346', '6', '60181', '                        نقليات للمشتريات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('347', '6', '60182', '                        سمسرة وعمولات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('348', '6', '60183', '                        تأمين على الشحن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('349', '6', '60184', '                        اتعاب مخلصي البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('350', '6', '60185', '                        رسوم جمركية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('351', '6', '6019', '                     حسومات مكتسبة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('352', '6', '605', '                      قيمة التغيير في مخزون البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('353', '6', '6051', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('354', '6', '6052', '                     مخزون اخر المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('355', '6', '61', '                       مواد اولية واستهلاكية مستخدمة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('356', '6', '611', '                      مشتريات المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('357', '6', '6111', '                     شراء مواد اولية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('358', '6', '61111', '                        مواد اولية أ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('359', '6', '61112', '                        مود اولية ب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('360', '6', '6112', '                     مواد ولوازم استهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('361', '6', '61121', '                        محروقات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('362', '6', '61122', '                        مواد الصيانة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('363', '6', '61123', '                        لوازم للمشغل والمصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('364', '6', '61124', '                        لوازم للمخزن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('365', '6', '61125', '                        لوازم مكتبية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('366', '6', '6113', '                     شراء عبوات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('367', '6', '61131', '                        عبوات لا تسترد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('368', '6', '61132', '                        عبوات تسترد ويعاد استعمالها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('369', '6', '61133', '                        عبوات تستعمل على اوجه مختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('370', '6', '6118', '                     مصاريف اضافية على شراء مواد اولية واستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('371', '6', '61181', '                        نقليات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('372', '6', '61182', '                        سمسرة وعمولات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('373', '6', '61183', '                        تامين على الشحن');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('374', '6', '61184', '                        اتعاب مخلصي البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('375', '6', '61185', '                        رسوم جمركية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('376', '6', '6119', '                     حسومات مكتسبة (بالتفصيل)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('377', '6', '615', '                      قيمة التغيير في مخزون المواد الاولية والاستهلاكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('378', '6', '6151', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('379', '6', '6152', '                     مخزون اول المدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('380', '6', '62', '                       اعباء خارجية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('381', '6', '621', '                      مشتريات من ملتزمين ثانويين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('382', '6', '6211', '                     ملتزم ثانوي أ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('383', '6', '6212', '                     ملتزم ثانوي ب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('384', '6', '6219', '                     حسومات مكتسبة على مشتريات من ملتزمين ثانويين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('385', '6', '625', '                      الاتاوى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('386', '6', '6251', '                     ايجار - قرض اموال منقولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('387', '6', '6252', '                     ايجار - قرض اموال غير منقولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('388', '6', '6253', '                     حقوق الامتياز');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('389', '6', '6254', '                     براءات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('390', '6', '6255', '                     اجازات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('391', '6', '6256', '                     علامات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('392', '6', '6257', '                     اساليب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('393', '6', '6258', '                     حقوق وقيم مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('394', '6', '626', '                      الخدمات الخارجية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('395', '6', '6261', '                     نقليات واتصالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('396', '6', '62611', '                        نفقات نقل للموجودات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('397', '6', '62612', '                        نفقات النقل المشترك للمستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('398', '6', '62615', '                        نفقات البريد والاتصالات السلكية واللاسلكية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('399', '6', '6262', '                     الصيانة والتصليحات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('400', '6', '6263', '                     الايجارات (خدمات الابنية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('401', '6', '62631', '                        الايجارات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('402', '6', '62632', '                        اعباء تأجيرية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('403', '6', '6264', '                     خدمات الفنادق والمطاعم');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('404', '6', '62641', '                        تشريفات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('405', '6', '62642', '                        نقل وانتقال للمبيعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('406', '6', '62643', '                        اطعام المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('407', '6', '6265', '                     خدمات المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('408', '6', '62651', '                        المستخدمون المؤقتون');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('409', '6', '62652', '                        اجور الوسطاء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('410', '6', '62653', '                        بدلات اتعاب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('411', '6', '6266', '                     خدمات تعليمية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('412', '6', '62661', '                        الاعداد والتدريب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('413', '6', '62662', '                        التوثيق');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('414', '6', '6267', '                     الدراسات والبحوث');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('415', '6', '6268', '                     اقساط التأمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('416', '6', '6269', '                     خدمات خارجية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('417', '6', '62691', '                        خدمات صحية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('418', '6', '62692', '                        خدمات مالية (مصاريف على سندات واوراق مالية)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('419', '6', '63', '                       اعباء المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('420', '6', '631', '                      رواتب واجور المستخدمين');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('421', '6', '6311', '                     الرواتب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('422', '6', '6312', '                     الاجور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('423', '6', '6314', '                     العمولات الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('424', '6', '6316', '                     البدلات المدفوعة للمديرين الذين يتمتعون بأغلبية في ملكية المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('425', '6', '6317', '                     البدلات المدفوعة لاعضاء مجلس الادارة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('426', '6', '635', '                      اعباء اجتماعية (ضمان اجتاعي ...)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('427', '6', '64', '                       ضرائب ورسوم ومدفوعات مماثلة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('428', '6', '641', '                      على الرواتب والاجور وبدلات الاتعاب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('429', '6', '642', '                      ضرائب ورسوم للبلديات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('430', '6', '643', '                      ضرائب على المبيعات غير قابلة للاسترداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('431', '6', '644', '                      رسوم التسجيل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('432', '6', '645', '                      ضرائب ورسوم ومدفوعات مماثلة اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('433', '6', '65', '                       مخصصات الاستهلاكات والمؤونات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('434', '6', '651', '                      مخصصات الاستهلاكات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('435', '6', '6511', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('436', '6', '6512', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('437', '6', '6515', '                     اعباء للتوزيع على عدة دورات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('438', '6', '655', '                      مخصصات المؤونة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('439', '6', '6551', '                     مؤونات هبوط اسعار الاصول الثابتة غير المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('440', '6', '6552', '                     مؤونات هبوط اسعار الاصول الثابتة المادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('441', '6', '6553', '                     مؤونات هبوط اسعار المخزون وقيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('442', '6', '6554', '                     مؤونات هبوط قيم الذمم المدينة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('443', '6', '6555', '                     مؤونات لمواجهة المخاطر والاعباء العائدة للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('444', '6', '66', '                       اعباء ادارية عادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('445', '6', '661', '                      اعباء ادارية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('446', '6', '6611', '                     بدلات الحضور');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('447', '6', '6612', '                     خسارة على ذمم الاستثمار المدينة التي ثبت هلاكها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('448', '6', '665', '                      حصة المؤسسة من الخسائر على عمليات مشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('449', '6', '67', '                       الاعباء المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('450', '6', '673', '                      فوائد واعباء مشابهه');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('451', '6', '6731', '                     فوائد على الذمم الدائنة والقروض');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('452', '6', '6732', '                     فوائد على الحسابات  الجارية والودائع الدائنة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('453', '6', '6733', '                     فوائد السندات المربوطة بكفالات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('454', '6', '6734', '                     فوائد الذمم الدائنة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('455', '6', '6735', '                     الخصم الممنوح من المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('456', '6', '6736', '                     فوائد مصرفية وفوائد على عمليات التمويل');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('457', '6', '675', '                      فروقات صرف سلبية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('458', '6', '6751', '                     فروقات على عمليات عادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('459', '6', '6752', '                     فروقات على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('460', '6', '676', '                      اعباء صافية على عمليات التفرغ عن سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('461', '6', '679', '                      مخصصات الاستهلاكات والمؤونات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('462', '6', '6791', '                     استهلاك علاوات التسديد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('463', '6', '6792', '                     مؤونات هبوط اسعار الاصول الثابتة المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('464', '6', '6794', '                     مؤونات هبوط اسعار سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('465', '6', '6795', '                     مؤونات لمواجهة المخاطر والاعباء المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('466', '6', '68', '                       اعباء خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('467', '6', '681', '                      القيمة الدفترية للاصول الثابتة المتفرغ عنها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('468', '6', '6811', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('469', '6', '6812', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('470', '6', '6815', '                     اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('471', '6', '685', '                      اعباء اخرى خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('472', '6', '6851', 'اعباء على عمليات ادارة المؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('473', '6', '68511', '                        هبات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('474', '6', '68512', '                        اعانات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('475', '6', '68513', 'غرامات ضريبية وجزائية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('476', '6', '68514', 'غرامات على صفقات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('477', '6', '68515', 'ذمم مدينة اصبحت هالكة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('478', '6', '68516', 'عمليات ادارية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('479', '6', '6855', '                     اعباء على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('480', '6', '68551', '                        اعباء ناتجة عن موجب تطبيق مؤشر اسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('481', '6', '68552', '                        جوائز تسديد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('482', '6', '68553', '                        اعباء ناتجة عن استرداد المؤسسة لاسهم وسندات صادرة عنها');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('483', '6', '689', '                      مخصصات الاستهلاكات والمؤونات خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('484', '6', '6891', '                     استهلاك استثنائي على اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('485', '6', '6892', '                     مؤونات هبوط اسعار استثنائي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('486', '6', '6895', '                     مؤونات لمواجهة مخاطر واعباء خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('487', '6', '69', '                       الضرائب على الارباح ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('488', '7', '7', '                الفئة السابعة - حسابات الايرادات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('489', '7', '70', '                       مبيعات البضاعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('490', '7', '701', '                      فواتير');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('491', '7', '709', '                      حسومات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('492', '7', '71', '                       المنتجات المباعة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('493', '7', '711', '                      مبيعات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('494', '7', '7111', '                     مبيعات المنتجات التامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('495', '7', '7112', '                     مبيعات المنتجات الوسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('496', '7', '7113', '                     مبيعات فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('497', '7', '712', '                      اشغال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('498', '7', '713', '                      خدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('499', '7', '717', '                      ايرادات النشاطات الفرعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('500', '7', '719', '                      حسومات ممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('501', '7', '7191', '                     على بيع المنتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('502', '7', '7192', '                     على بيع الاشغال');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('503', '7', '7193', '                     على بيع الخدمات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('504', '7', '7197', '                     على بيع نشاطات فرعية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('505', '7', '72', '                       الانتاج المخزون (قيمة التغيير)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('506', '7', '721', '                      قيد الصنع (اموال)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('507', '7', '7211', '                     منتجات قيد الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('508', '7', '7212', '                     اشغال قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('509', '7', '722', '                      قيد الصنع (خدمات)');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('510', '7', '7225', '                     دراسات قيد الاعداد');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('511', '7', '7226', '                     خدمات قيد التنفيذ');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('512', '7', '725', '                      منتجات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('513', '7', '7251', '                     منتجات وسيطة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('514', '7', '7255', '                     منتجات تامة الصنع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('515', '7', '7258', '                     فضلات الانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('516', '7', '73', '                       منتجات لها طابع الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('517', '7', '731', '                      اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('518', '7', '732', '                      اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('519', '7', '74', '                       اعانات للاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('520', '7', '741', '                      للبضائع');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('521', '7', '742', '                      للانتاج');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('522', '7', '75', '                       استردادات من المؤونات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('523', '7', '752', ' للاستثمار                       استردادات من مؤونات  هبوط اسعار الاصول الثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('524', '7', '753', '                      استردادات من مؤونات هبوط الاسعار الاصول المتداولة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('525', '7', '76', '                       ايرادات اخرى ناتجة عن الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('526', '7', '761', '                      ايرادات عادية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('527', '7', '7611', '                     اتاوى الامتيازات, البراءات, الاجازات, العلامات والاساليب');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('528', '7', '7612', '                     ايرادات الابنية غير المخصصة للنشاط المهني');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('529', '7', '7613', '                     بدلات حضور وتعويضات اعضاء مجلس الادارة, المديرين . . . .');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('530', '7', '7619', '                     اعباء استثمار محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('531', '7', '765', '                      حصص ارباح العمليات المشتركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('532', '7', '7651', '                     حصص الاعباء الصافية التي جرى تحويلها (محاسبة المؤسسة التي');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('533', '7', '7655', '                     حصص الايرادات الصافية المقررة (محاسبة المؤسسة التي لم تتولى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('534', '7', '77', '                       الايرادات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('535', '7', '771', '                      ايرادات سندات  المشاركة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('536', '7', '7711', '                     عائدات سندات  التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('537', '7', '7716', '                     عائدات المشاركات  الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('538', '7', '7717', '                     عائدات الذمم المدينة المرتبطة بمشاركات');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('539', '7', '772', '                      ايرادات القيم المنقولة الاخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('540', '7', '7721', '                     عائدات سندات التوظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('541', '7', '7723', '                     عائدات السندات المجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('542', '7', '7725', '                     عائدات الذمم المدينة المجمدة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('543', '7', '7726', '                     عائدات الذمم المدينة التجارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('544', '7', '7727', '                     عائدات الذمم المدينة المختلفة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('545', '7', '773', '                      فوائد وايرادات مشابهة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('546', '7', '7731', '                     عائدات القروض الممنوحة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('547', '7', '7733', '                     الخصم الممنوح للمؤسسة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('548', '7', '775', '                      فروقات صرف ايجابية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('549', '7', '7751', '                     على عمليات جارية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('550', '7', '7755', '                     على عمليات رأسمالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('551', '7', '778', '                      ايرادات مالية اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('552', '7', '7781', '                     ايرادات صافية على بيع سندات توظيف');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('553', '7', '7789', '                     اعباء مالية محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('554', '7', '779', '                      استردادات من المؤونات المالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('555', '7', '7793', '                     استردادات من مؤونات هبوط الاسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('556', '7', '7795', '                     استردادات من مؤونات المخاطر والاعباء');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('557', '7', '78', '                       ايرادات التفرغ عن اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('558', '7', '781', '                      ايرادات التفرغ عن اصول ثابتة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('559', '7', '7811', '                     اصول ثابتة غير مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('560', '7', '7812', '                     اصول ثابتة مادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('561', '7', '7815', '                     اصول ثابتة مالية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('562', '7', '782', '                      اعانات للتوظيفات محولة الى نتيجة الدورة');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('563', '7', '788', '                      ايرادات اخرى خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('564', '7', '7881', '                     ايرادات استثنائية على عمليات عادية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('565', '7', '7888', '                     ايرادات اخرى على عمليات رأسمالية استثنائية');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('566', '7', '7889', '                     اعباء استثنائية محولة الى حسابات اخرى');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('567', '7', '789', '                      استردادات من مؤونات خارج الاستثمار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('568', '7', '7891', '                     استردادات من مؤونات هبوط الاسعار');
INSERT INTO "enterprise"."chartOfAccountRef" ("pk", "accountCategory_id", "num_", "description") VALUES ('569', '7', '7895', '                     استردادات من مؤونات المخاطر');
CREATE TABLE "auth"."user_permission" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."user_permission_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObjects_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "user_permission_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_permission" OWNER TO postgres;
ALTER SEQUENCE "auth"."user_permission_seq" OWNED BY "auth"."user_permission"."pk";
CREATE TABLE "auth"."user_department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_department_id_seq"'::regclass) ,
 "user_branch_id" SMALLINT NOT NULL REFERENCES "auth"."user_branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "user_department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_department" OWNER TO postgres;
CREATE TABLE "auth"."group_permission" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."group_permission_seq"'::regclass) ,
 "group_id" SMALLINT NOT NULL REFERENCES "auth"."group" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObjects_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "group_permission_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group_permission" ADD CONSTRAINT "group_permission_group_id_appObjects_id_unique" UNIQUE ("group_id", "appObjects_id");
ALTER TABLE "auth"."group_permission" OWNER TO postgres;
ALTER SEQUENCE "auth"."group_permission_seq" OWNED BY "auth"."group_permission"."pk";
CREATE TABLE "person"."person_personTypes" (
 "person_id" SMALLINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "person_personTypes_pkey" PRIMARY KEY ("person_id", "personType_id")
);
ALTER TABLE "person"."person_personTypes" OWNER TO postgres;
CREATE TABLE "person"."personContact" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('person."personContact_id_seq"'::regclass) ,
 "person_id" BIGINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "name_" VARCHAR(50) NOT NULL ,
 "post" VARCHAR(150) ,
 "phone" VARCHAR(18) NOT NULL ,
 CONSTRAINT "personContact_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."personContact" OWNER TO postgres;
ALTER SEQUENCE "person"."personContact_id_seq" OWNED BY "person"."personContact"."pk";
CREATE TABLE "person"."financialInfo" (
 "pk" BIGINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "financial_number" BIGINT NOT NULL UNIQUE ,
 "commercial_register_number" BIGINT NOT NULL UNIQUE ,
 "TVA_registration_number" VARCHAR(20) NOT NULL UNIQUE ,
 "capital" MONEY ,
 "legalForm" SMALLINT NOT NULL REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "financialInfo_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."financialInfo" OWNER TO postgres;
CREATE TABLE "person"."address" (
 "pk" BIGINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "phone" VARCHAR(18) UNIQUE ,
 "fax" VARCHAR(18) UNIQUE ,
 "email" VARCHAR(50) UNIQUE ,
 "website" VARCHAR(50) ,
 "address" TEXT ,
 CONSTRAINT "address_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."address" OWNER TO postgres;
CREATE TABLE "machine"."machine_emp" (
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "att_id" BIGINT NOT NULL ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "options" JSONB ,
 CONSTRAINT "machine_emp_pkey" PRIMARY KEY ("machine_id", "att_id", "emp_id")
);
ALTER TABLE "machine"."machine_emp" OWNER TO postgres;
CREATE TABLE "machine"."machinelogs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('machine."machinelogs_id_seq"'::regclass) ,
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "start_date" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "end_date" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "records_count" BIGINT NOT NULL ,
 "created" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "notes" TEXT ,
 CONSTRAINT "machinelogs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "machine"."machinelogs" OWNER TO postgres;
ALTER SEQUENCE "machine"."machinelogs_id_seq" OWNED BY "machine"."machinelogs"."pk";
CREATE TABLE "machine"."attendance" (
 "machine_id" SMALLINT NOT NULL REFERENCES "machine"."machine" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "att_id" BIGINT ,
 "attendance_date" TIMESTAMP WITHOUT TIME ZONE ,
 "created" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "attOptions" JSONB ,
 CONSTRAINT "attendance_pkey" PRIMARY KEY ("att_id", "attendance_date")
) PARTITION BY RANGE (attendance_date);
ALTER TABLE "machine"."attendance" OWNER TO postgres;
CREATE TABLE "hr"."vacationMAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationMAuthority_id_seq"'::regclass) ,
 "vacationDocs_id" BIGINT NOT NULL REFERENCES "hr"."vacationMDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT NOT NULL ,
 "priority" SMALLINT NOT NULL ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "vacationMAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationMAuthority" OWNER TO postgres;
ALTER SEQUENCE "hr"."vacationMAuthority_id_seq" OWNED BY "hr"."vacationMAuthority"."pk";
CREATE TABLE "hr"."vacationAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationAuthority_id_seq"'::regclass) ,
 "vacationDocs_id" BIGINT NOT NULL REFERENCES "hr"."vacationDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT NOT NULL ,
 "priority" SMALLINT NOT NULL ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "vacationAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationAuthority" OWNER TO postgres;
ALTER SEQUENCE "hr"."vacationAuthority_id_seq" OWNED BY "hr"."vacationAuthority"."pk";
CREATE TABLE "hr"."exitDocsAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."exitDocsAuthority_id_seq"'::regclass) ,
 "exitDocs_id" BIGINT NOT NULL REFERENCES "hr"."exitDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT ,
 "priority" SMALLINT ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "exitDocsAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."exitDocsAuthority" OWNER TO postgres;
ALTER SEQUENCE "hr"."exitDocsAuthority_id_seq" OWNED BY "hr"."exitDocsAuthority"."pk";
CREATE TABLE "hr"."detailAddress" (
 "pk" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "floor_" SMALLINT ,
 "nationality" person.nationality ,
 "state_" VARCHAR(50) ,
 "city" VARCHAR(50) ,
 "region" VARCHAR(50) ,
 "street" VARCHAR(50) ,
 "building_project" VARCHAR(50) ,
 "building" VARCHAR(50) ,
 "addition_info" VARCHAR(100) ,
 CONSTRAINT "detailAddress_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."detailAddress" OWNER TO postgres;
CREATE TABLE "hr"."decisionTransfer" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionTransfer_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "decisionTransfer_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionTransfer" OWNER TO postgres;
ALTER SEQUENCE "hr"."decisionTransfer_id_seq" OWNED BY "hr"."decisionTransfer"."pk";
CREATE TABLE "hr"."decisionExtend" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionExtend_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL UNIQUE REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 CONSTRAINT "decisionExtend_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionExtend" OWNER TO postgres;
ALTER SEQUENCE "hr"."decisionExtend_id_seq" OWNED BY "hr"."decisionExtend"."pk";
CREATE TABLE "hr"."decisionEndOfService" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionEndOfService_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL UNIQUE REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 CONSTRAINT "decisionEndOfService_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionEndOfService" OWNER TO postgres;
ALTER SEQUENCE "hr"."decisionEndOfService_id_seq" OWNED BY "hr"."decisionEndOfService"."pk";
CREATE TABLE "hr"."decisionEmployment" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionEmployment_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "decisionEmployment_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionEmployment" OWNER TO postgres;
ALTER SEQUENCE "hr"."decisionEmployment_id_seq" OWNED BY "hr"."decisionEmployment"."pk";
CREATE TABLE "hr"."decisionAssignment" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionAssignment_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_ops" BOOLEAN DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "decisionAssignment_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionAssignment" OWNER TO postgres;
ALTER SEQUENCE "hr"."decisionAssignment_id_seq" OWNED BY "hr"."decisionAssignment"."pk";
CREATE TABLE "enterprise"."financialYear" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."financialYear_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "start_" DATE NOT NULL ,
 "end_" DATE ,
 "accounting_info" JSONB ,
 CONSTRAINT "financialYear_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."financialYear" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."financialYear_id_seq" OWNED BY "enterprise"."financialYear"."pk";
CREATE TABLE "enterprise"."department_optypes" (
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "department_optypes_pkey" PRIMARY KEY ("department_id", "opType_id")
);
ALTER TABLE "enterprise"."department_optypes" OWNER TO postgres;
CREATE TABLE "enterprise"."branch_department_post" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."branch_department_post_id_seq"'::regclass) ,
 "branch_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch_department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "legal_capacity" SMALLINT ,
 "occupied" SMALLINT DEFAULT '0'::SMALLINT ,
 CONSTRAINT "branch_department_post_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."branch_department_post" OWNER TO postgres;
ALTER SEQUENCE "enterprise"."branch_department_post_id_seq" OWNED BY "enterprise"."branch_department_post"."pk";
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('1', '13', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('2', '14', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('3', '15', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('4', '16', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('5', '17', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('6', '18', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('7', '19', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('8', '1', '3');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('9', '7', '3');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('10', '3', '3');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('11', '4', '3');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('12', '0', '3');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('13', '5', '4');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('14', '8', '4');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('15', '6', '5');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('16', '9', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('17', '12', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('18', '11', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('19', '10', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('20', '2', '4');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('21', '3', '4');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('22', '2', '5');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('23', '3', '5');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('24', '5', '5');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('25', '3', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('26', '0', '5');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('27', '28', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('28', '7', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('29', '3', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('30', '4', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('31', '56', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('32', '31', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('33', '30', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('34', '29', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('35', '59', '8');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('36', '54', '8');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('37', '49', '8');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('38', '54', '9');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('39', '7', '9');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('40', '3', '8');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('41', '7', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('42', '4', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('43', '3', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('44', '20', '11');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('45', '7', '11');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('46', '3', '11');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('47', '21', '12');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('48', '4', '12');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('49', '3', '12');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('50', '21', '13');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('51', '7', '13');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('52', '4', '13');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('53', '3', '13');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('54', '21', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('55', '22', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('56', '23', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('57', '4', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('58', '3', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('59', '21', '15');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('60', '4', '15');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('61', '3', '15');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('62', '24', '16');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('63', '25', '16');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('64', '3', '16');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('65', '21', '17');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('66', '3', '17');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('67', '21', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('68', '4', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('69', '26', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('70', '57', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('71', '24', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('72', '32', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('73', '4', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('74', '3', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('75', '33', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('76', '7', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('77', '20', '20');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('78', '56', '20');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('79', '7', '20');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('80', '24', '21');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('81', '3', '21');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('82', '7', '21');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('83', '24', '22');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('84', '3', '22');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('85', '7', '22');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('86', '24', '23');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('87', '7', '23');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('88', '3', '23');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('89', '21', '24');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('90', '3', '24');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('91', '56', '24');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('92', '7', '24');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('93', '21', '25');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('94', '3', '25');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('95', '24', '26');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('96', '3', '26');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('97', '7', '26');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('98', '34', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('99', '7', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('100', '35', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('101', '58', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('105', '36', '28');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('106', '56', '28');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('107', '7', '28');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('108', '24', '29');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('109', '4', '29');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('110', '3', '29');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('111', '7', '29');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('112', '28', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('113', '37', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('114', '38', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('115', '39', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('116', '3', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('117', '7', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('118', '28', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('119', '7', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('120', '40', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('121', '41', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('122', '42', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('123', '37', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('124', '3', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('125', '40', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('126', '38', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('127', '37', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('128', '3', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('129', '43', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('130', '28', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('131', '7', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('132', '40', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('133', '44', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('134', '38', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('135', '3', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('136', '39', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('137', '40', '36');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('138', '3', '36');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('139', '39', '36');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('140', '21', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('141', '45', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('142', '4', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('143', '3', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('144', '39', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('145', '46', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('146', '47', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('147', '48', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('148', '49', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('149', '3', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('150', '24', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('151', '50', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('152', '51', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('153', '28', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('154', '40', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('155', '41', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('156', '38', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('157', '3', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('158', '7', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('159', '28', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('160', '42', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('161', '37', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('162', '3', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('163', '7', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('164', '28', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('165', '40', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('166', '38', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('167', '42', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('168', '37', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('169', '39', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('170', '3', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('171', '0', '1');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('173', '0', '26');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('174', '0', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('175', '0', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('176', '0', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('177', '0', '20');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('178', '0', '8');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('179', '0', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('180', '0', '43');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('182', '0', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('183', '0', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('184', '0', '45');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('185', '0', '46');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('186', '0', '9');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('187', '0', '11');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('188', '0', '28');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('189', '21', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('190', '0', '22');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('191', '0', '2');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('192', '0', '19');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('194', '0', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('195', '0', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('196', '0', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('198', '0', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('199', '0', '29');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('200', '0', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('201', '0', '30');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('202', '0', '16');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('203', '0', '12');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('204', '0', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('205', '0', '17');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('206', '0', '15');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('207', '0', '13');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('208', '0', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('209', '0', '4');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('210', '0', '6');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('211', '21', '48');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('212', '0', '24');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('213', '0', '48');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('214', '0', '25');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('215', '0', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('216', '0', '23');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('217', '0', '21');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('218', '0', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('219', '0', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('220', '0', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('221', '0', '36');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('223', '54', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('224', '53', '7');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('225', '54', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('226', '55', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('227', '7', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('228', '54', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('229', '55', '41');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('230', '19', '10');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('231', '3', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('232', '53', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('233', '54', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('234', '55', '44');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('235', '53', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('236', '54', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('237', '55', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('238', '29', '45');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('239', '21', '46');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('240', '49', '46');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('241', '54', '46');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('242', '3', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('243', '4', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('244', '21', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('245', '38', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('246', '39', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('247', '40', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('248', '42', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('249', '44', '34');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('250', '41', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('251', '3', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('252', '24', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('253', '37', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('254', '38', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('255', '41', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('256', '42', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('257', '54', '31');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('258', '3', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('259', '4', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('260', '7', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('261', '18', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('262', '21', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('263', '25', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('264', '33', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('265', '54', '47');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('266', '37', '39');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('267', '21', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('268', '7', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('269', '54', '14');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('270', '3', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('271', '54', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('272', '7', '25');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('273', '3', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('275', '54', '27');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('276', '54', '23');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('277', '54', '21');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('282', '28', '9');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('284', '21', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('285', '21', '36');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('293', '30', '45');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('296', '59', '38');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('297', '3', '48');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('298', '40', '28');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('299', '38', '37');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('301', '62', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('302', '63', '35');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('303', '54', '40');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('304', '54', '33');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('305', '54', '32');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('306', '64', '43');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('307', '0', '49');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('308', '65', '43');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('313', '67', '18');
INSERT INTO "enterprise"."branch_department_post" ("pk", "post_id", "branch_department_id") VALUES ('314', '68', '3');
CREATE TABLE "enterprise"."branch_currencies" (
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "branch_currencies_pkey" PRIMARY KEY ("branch_id", "currency_id")
);
ALTER TABLE "enterprise"."branch_currencies" OWNER TO postgres;
INSERT INTO "enterprise"."branch_currencies" ("branch_id", "currency_id") VALUES (0, 0);
INSERT INTO "enterprise"."branch_currencies" ("branch_id", "currency_id") VALUES (0, 1);
CREATE TABLE "enterprise"."branch_acc" (
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "chartOfAccountRef_id" SMALLINT NOT NULL REFERENCES "enterprise"."chartOfAccountRef" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allowRooting" BOOLEAN NOT NULL ,
 CONSTRAINT "branch_acc_pkey" PRIMARY KEY ("chartOfAccountRef_id", "branch_id")
);
ALTER TABLE "enterprise"."branch_acc" OWNER TO postgres;
CREATE TABLE "auth"."user_permission_detail" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."user_permission_detail_id_seq"'::regclass) ,
 "user_permission_id" BIGINT NOT NULL REFERENCES "auth"."user_permission" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'False'::BOOLEAN ,
 "permission_name_id" VARCHAR(10) NOT NULL REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "fieldlist" VARCHAR(50)[] ,
 CONSTRAINT "user_permission_detail_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_permission_detail" ADD CONSTRAINT "user_permission_detail_check" CHECK (permission_name_id IN ('Select', 'Insert', 'Update','View','Append','Change') OR (permission_name_id NOT IN ('Select', 'Insert', 'Update','View','Append','Change') AND fieldlist IS NULL) OR (allow is false AND fieldlist is Null));
ALTER TABLE "auth"."user_permission_detail" OWNER TO postgres;
ALTER SEQUENCE "auth"."user_permission_detail_id_seq" OWNED BY "auth"."user_permission_detail"."pk";
CREATE TABLE "auth"."user_group" (
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "group_id" SMALLINT NOT NULL REFERENCES "auth"."group" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "user_group_pkey" PRIMARY KEY ("user_id", "group_id")
);
ALTER TABLE "auth"."user_group" OWNER TO postgres;
CREATE TABLE "auth"."user_department_optypes" (
 "user_department_id" SMALLINT NOT NULL REFERENCES "auth"."user_department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "optype_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "user_department_optypes_pkey" PRIMARY KEY ("user_department_id", "optype_id")
);
ALTER TABLE "auth"."user_department_optypes" OWNER TO postgres;
CREATE TABLE "auth"."userToken" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."userToken_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "revoked" TIMESTAMP WITHOUT TIME ZONE ,
 "expires" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "created" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now() ,
 "token_text" TEXT NOT NULL ,
 CONSTRAINT "userToken_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."userToken" OWNER TO postgres;
ALTER SEQUENCE "auth"."userToken_id_seq" OWNED BY "auth"."userToken"."pk";
CREATE TABLE "auth"."group_personType" (
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "group_id" SMALLINT NOT NULL REFERENCES "auth"."group" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "group_personType_pkey" PRIMARY KEY ("personType_id", "group_id")
);
ALTER TABLE "auth"."group_personType" OWNER TO postgres;
CREATE TABLE "auth"."group_permission_detail" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."group_permission_detail_id_seq"'::regclass) ,
 "group_permission_id" BIGINT NOT NULL REFERENCES "auth"."group_permission" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN DEFAULT 'false'::BOOLEAN ,
 "permission_name_id" VARCHAR(10) NOT NULL REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "fieldlist" VARCHAR(50)[] ,
 CONSTRAINT "group_permission_detail_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group_permission_detail" ADD CONSTRAINT "group_permission_detail_check" CHECK (permission_name_id IN ('Select', 'Insert', 'Update','View','Append','Change') OR (permission_name_id NOT IN ('Select', 'Insert', 'Update','View','Append','Change') AND fieldlist IS NULL) OR (allow is false AND fieldlist is Null));
ALTER TABLE "auth"."group_permission_detail" OWNER TO postgres;
ALTER SEQUENCE "auth"."group_permission_detail_id_seq" OWNED BY "auth"."group_permission_detail"."pk";
CREATE TABLE "auth"."audit_user" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."audit_user_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObject_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "audit_user_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."audit_user" ADD CONSTRAINT "audit_user_user_id_appObject_id_unique" UNIQUE ("user_id", "appObject_id");
ALTER TABLE "auth"."audit_user" OWNER TO postgres;
ALTER SEQUENCE "auth"."audit_user_id_seq" OWNED BY "auth"."audit_user"."pk";
CREATE TABLE "auth"."audit_log" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."audit_log_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "logged_at" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW() ,
 "table_name" TEXT NOT NULL ,
 "row_id" TEXT NOT NULL ,
 "action_type" auth.action NOT NULL ,
 "old_data" JSONB NOT NULL ,
 "new_data" JSONB NOT NULL ,
 CONSTRAINT "audit_log_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."audit_log" OWNER TO postgres;
ALTER SEQUENCE "auth"."audit_log_id_seq" OWNED BY "auth"."audit_log"."pk";
CREATE TABLE "auth"."appObjects_allowed_permission" (
 "appObject_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "permission_name_id" VARCHAR(10) REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "appObjects_allowed_permission_pkey" PRIMARY KEY ("appObject_id", "permission_name_id")
);
ALTER TABLE "auth"."appObjects_allowed_permission" OWNER TO postgres;
CREATE OR REPLACE FUNCTION auth.aiu_alltables_logActions()
RETURNS TRIGGER AS $$
DECLARE
    v_old_data JSONB;
    v_new_data JSONB;
    v_row_id TEXT;
    v_pk_cols TEXT[]; -- Array to hold primary key column names
    v_pk_col TEXT;
    v_pk_values TEXT[]; -- Array to hold primary key values for the current row
    v_current_row_data JSONB; -- Will hold either OLD or NEW record as JSONB
    v_should_log BOOLEAN := FALSE; -- Flag to determine if logging should occur
    v_user_id INTEGER; -- To store the user_id from app_users table
BEGIN
    -- Dynamically get primary key column names for the current table
    SELECT ARRAY_AGG(a.attname ORDER BY a.attnum)
    INTO v_pk_cols
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    WHERE i.indrelid = TG_TABLE_NAME::regclass AND i.indisprimary;

    -- Determine which record (OLD/NEW) to use for extracting PK values
    IF TG_OP = 'DELETE' THEN
        v_current_row_data := to_jsonb(OLD);
        v_old_data := v_current_row_data;
        v_new_data := NULL;
    ELSE -- INSERT or UPDATE
        v_current_row_data := to_jsonb(NEW);
        v_new_data := v_current_row_data;
        IF TG_OP = 'UPDATE' THEN
            v_old_data := to_jsonb(OLD);
        ELSE -- INSERT
            v_old_data := NULL;
        END IF;
    END IF;

    -- Construct v_row_id by concatenating primary key values
    v_pk_values := ARRAY[]::TEXT[];
    IF v_current_row_data IS NOT NULL AND array_length(v_pk_cols, 1) > 0 THEN
        FOREACH v_pk_col IN ARRAY v_pk_cols LOOP
            v_pk_values := array_append(v_pk_values, v_current_row_data->>v_pk_col);
        END LOOP;
        v_row_id := ARRAY_TO_STRING(v_pk_values, '-');
    END IF;

    -- *** Start of new logic: Retrieve user_id and check audit_config ***
    -- First, get the user_id from app_users based on current_user (role name)
    SELECT user_id INTO v_user_id
    FROM app_users
    WHERE role_name = current_user;

    -- If a matching user_id is found, then check audit_config
    IF v_user_id IS NOT NULL THEN
        SELECT EXISTS (
            SELECT 1
            FROM audit_config
            WHERE audited_user_id = v_user_id AND audited_table = TG_TABLE_NAME
        ) INTO v_should_log;
    END IF;

    -- If v_should_log is TRUE, then insert the log entry
    IF v_should_log THEN
        INSERT INTO audit_log (
            table_name,
            action_type,
            performed_by,
            old_data,
            new_data,
            row_id,
            user_id_fk -- Populate the new user_id_fk column
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            current_user,
            v_old_data,
            v_new_data,
            v_row_id,
            v_user_id -- Use the retrieved user_id
        );
    END IF;
    -- *** End of new logic ***

    -- For row-level triggers, you must return NEW for INSERT/UPDATE or OLD for DELETE
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION auth.aiu_alltables_logActions() IS 'Trigger function to log INSERT, UPDATE, and DELETE operations on tables, dynamically determining primary keys and using app_users and audit_config for conditional logging.';
CREATE OR REPLACE FUNCTION auth.au_group_permission_detail_grantRevokePrivilege()
RETURNS TRIGGER AS $$
DECLARE
    v_rolename TEXT;
    v_privilege TEXT;
    v_schema_name TEXT;
    v_object_name TEXT;
BEGIN
     -- Fetch the role name from the group table using the group_id from group_permission
        SELECT g.rolename
        INTO v_rolename
        FROM auth.group g
        INNER JOIN auth.group_permission gp ON g.pk = gp.group_id
        WHERE gp.pk = NEW.group_permission_id;

    -- Fetch the privilege from the permission table using permission_name_id
        SELECT p.privilege
        INTO v_privilege
        FROM auth.permission p
        WHERE p.name_ = NEW.permission_name_id;

    -- Fetch the schema name from the appObjects table using appObjects_id in group_permission
        SELECT ao.schema_ , ao.name_
        INTO v_schema_name , v_object_name
        FROM auth.appObjects ao
        INNER JOIN auth.group_permission gp ON ao.pk = gp.appObjects_id
        WHERE gp.pk = NEW.group_permission_id;

    IF NEW.allow = false THEN -- don't need to check fieldlist
        IF OLD.allow = true THEN
            EXECUTE 'REVOKE ' || v_privilege || ' ON ' || v_schema_name || '.' || v_object_name || ' FROM ' || v_rolename;
        ELSE
            RAISE Warning 'Field List is not applicable when allow is false';
        END IF;
        NEW.fieldlist := Null;
        -- revoke usage on schema if neccessary
        perform auth.f_check_and_revoke_schema_usage_if_no_object_privs(v_schema_name, v_rolename);

    END IF;

    IF OLD.allow = true THEN
        EXECUTE 'REVOKE ' || v_privilege || ' ON ' || v_schema_name || '.' || v_object_name || ' FROM ' || v_rolename;

        IF NEW.fieldlist IS NOT NULL THEN
            EXECUTE 'GRANT ' || v_privilege || ' (' || NEW.fieldlist || ') ON ' || v_schema_name || '.' || v_object_name ||' TO ' || v_rolename;
        ELSE
            Execute format('GRANT %I ON %s.%s TO %I', v_privilege, v_schema_name, v_object_name, v_rolename);
        END IF;

        Execute format('GRANT USAGE ON %I TO %I;', v_schema_name, v_rolename);
    END IF;
  
    EXECUTE 'COMMIT;';
    RETURN NEW;

END;
$$ LANGUAGE plpgsql;

alter function auth.au_group_permission_detail_grantRevokePrivilege() owner to postgres;
CREATE TRIGGER trg_aiu_group_permission_detail_grantRevokePrivilege
after UPDATE of allow, fieldlist ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.au_group_permission_detail_grantRevokePrivilege();
CREATE OR REPLACE FUNCTION auth.aiu_user_branch_insertDepartmentsForBranch()
RETURNS TRIGGER AS $$
BEGIN

   IF TG_OP = 'UPDATE' AND OLD.branch_id != NEW.branch_id THEN
     -- Delete from the "group_department" table
     DELETE FROM auth.user_department
     WHERE user_branch_id = OLD.pk;
   END IF;
   
   IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.branch_id != NEW.branch_id) THEN

    IF current_setting('db.single_department') = 'true' THEN
      insert into auth.user_department (user_branch_id, department_id) values (new.pk, 0);
    ELSE
     -- Insert into the "group_department" table
     INSERT INTO auth.user_department (user_branch_id, department_id)
      SELECT DISTINCT NEW.pk, department_id
      FROM enterprise.v_branch_department_optypes;
    END IF;

   END IF;

  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function auth.aiu_user_branch_insertDepartmentsForBranch() owner to postgres;

CREATE TRIGGER trg_aiu_user_branch_insertDepartmentsForBranch
AFTER INSERT OR UPDATE OF branch_id ON auth.user_branch
FOR EACH ROW
EXECUTE FUNCTION auth.aiu_user_branch_insertDepartmentsForBranch();
CREATE OR REPLACE FUNCTION auth.aiu_user_department_insertOptypesForDepartment()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_id smallint;
BEGIN

  IF TG_OP = 'UPDATE' THEN
    -- if allow changed , update group_department_optypes table to set allow = false
    IF NEW.allow = false and OLD.allow = true and NEW.department_id = OLD.department_id THEN
      UPDATE auth.user_department_optypes SET allow = false
      WHERE group_department_id = OLD.pk;
    END IF;

    IF NEW.department_id <> OLD.department_id THEN
      -- if department changed, delete from group_department table and insert new data
      DELETE FROM auth.group_department_optypes
      WHERE group_department_id = OLD.pk;
    END IF;

  END IF;

  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.department_id <> OLD.department_id) THEN
    -- get branch_id from group_branch table
    SELECT branch_id INTO v_branch_id FROM auth.user_branch WHERE pk = NEW.user_branch_id;

    INSERT INTO auth.user_department_optypes (user_department_id, optype_id, allow)
      SELECT DISTINCT NEW.pk, optype_id, NEW.allow
      FROM enterprise.v_branch_department_optypes
      WHERE branch_id = v_branch_id AND department_id = NEW.department_id;
  END IF;

  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function auth.aiu_user_department_insertOptypesForDepartment owner to postgres;

CREATE TRIGGER trg_aiu_user_department_insertOptypesForDepartment
AFTER INSERT OR UPDATE OF department_id, allow ON auth.user_department
FOR EACH ROW
EXECUTE FUNCTION auth.aiu_user_department_insertOptypesForDepartment();
-- This trigger is used to maintain the group_user_membership table.
CREATE OR REPLACE FUNCTION auth.aiu_user_group_grantRevokeUserGroups()
RETURNS TRIGGER AS $$
DECLARE
  v_group_role NAME;
  v_user_role NAME;
BEGIN
    IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
    
        SELECT rolename INTO v_group_role FROM auth.group WHERE pk = OLD.group_id;
        SELECT rolename INTO v_user_role FROM auth.user WHERE pk = OLD.user_id;
        
        Execute format('REVOKE %s FROM %s', v_group_role, v_user_role);
    END IF;

    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        SELECT rolename INTO v_group_role FROM auth.group WHERE pk = NEW.group_id;
        SELECT rolename INTO v_user_role FROM auth.user WHERE pk = NEW.user_id;
        
        Execute format('GRANT %s TO %s', v_group_role, v_user_role);
    END IF;
END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION auth.aiu_user_group_grantRevokeUserGroups() owner to postgres;

CREATE TRIGGER trg_aiu_user_group_grantRevokeUserGroups
after INSERT OR UPDATE OR DELETE ON auth.user_group
FOR EACH ROW
EXECUTE PROCEDURE auth.aiu_user_group_grantRevokeUserGroups();
create or replace function auth.ai_group_createGroup() returns trigger as $$
declare
    v_group_name name;
begin

  -- generate a group name based on name_
  v_group_name := current_setting('db.group_prefix') || quote_ident(new.name_);
  -- create the group
  execute 'create role '|| quote_ident(v_group_name);
  new.rolename = v_group_name;

  IF new.app_id != 0 THEN
    insert into auth."group_personType" (group_id, "personType_id") 
      select distinct NEW.pk, unnest(personTypeList) 
      from auth.app 
      where pk = new.app_id and active_ = true;

    insert into auth.group_permission (group_id, "appObjects_id")
        select NEW.pk, ap.pk
        from auth."appObjects" ap join auth.app a on ap.app_id = a.pk
        where a.active_ = true and ap.app_id = new.app_id;

  ELSE -- the all apps group case
    
    RAISE EXCEPTION 'Invalid input for app id this must not shown in production';

  END IF;

end;
$$ language plpgsql;

alter function auth.ai_group_createGroup() owner to postgres;

create or replace trigger trg_ai_group_createGroup
  after insert on auth.group
  for each row
  execute procedure auth.ai_group_createGroup();
CREATE OR REPLACE FUNCTION auth.ai_group_permission_detail_grantPrivilege()
RETURNS TRIGGER AS $$
DECLARE
    v_rolename NAME;
    v_privilege TEXT;
    v_sch_name TEXT;
    v_object_name TEXT;
    v_sql_inject TEXT;
BEGIN
     -- Fetch the role name from the group table using the group_id from group_permission
        SELECT g.rolename
        INTO v_rolename
        FROM auth.group g
        INNER JOIN auth.group_permission gp ON g.pk = gp.group_id
        WHERE gp.pk = NEW.group_permission_id;    

    -- Fetch the privilege from the permission table using permission_name_id
        SELECT p.privilege , p."objectType"
        INTO v_privilege , v_sql_inject
        FROM auth.permission p
        WHERE p.name_ = NEW.permission_name_id;

    -- Fetch the schema name from the appObjects table using appObjects_id in group_permission
        SELECT ao.schema_ , ao.name_
        INTO v_sch_name , v_object_name
        FROM auth."appObjects" ao
        INNER JOIN auth.group_permission gp ON ao.pk = gp."appObjects_id"
        WHERE gp.pk = NEW.group_permission_id;

    IF lower(v_sql_inject) = 'view' or lower(v_sql_inject) = 'table' THEN
        v_sql_inject := 'Table' || ' ' || quote_ident(v_sch_name) || '.' || quote_ident(v_object_name);
    ELSE
        v_sql_inject := v_sql_inject || ' ' || quote_ident(v_sch_name) || '.' || quote_ident(substring(v_object_name FROM '^[^(]+')) || substring(v_object_name FROM '\(.*') ;
    END IF;

    IF NEW.allow = true THEN
        IF NEW.fieldlist IS NOT NULL THEN
            EXECUTE 'GRANT ' || v_privilege || ' (' || NEW.fieldlist || ') ON ' ||  v_sql_inject || ' TO ' || v_rolename;
        ELSE
            EXECUTE 'GRANT ' || v_privilege || ' ON ' ||  v_sql_inject || ' TO ' || v_rolename;
        END IF;

        EXECUTE FORMAT('GRANT USAGE ON SCHEMA %I TO %I', v_sch_name, v_rolename);
    END IF;
  
    RETURN NULL;

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_group_permission_detail_grantPrivilege() owner to postgres;
CREATE TRIGGER trg_ai_group_permission_detail_grantPrivilege
after insert ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.ai_group_permission_detail_grantPrivilege();
CREATE OR REPLACE FUNCTION auth.ai_group_permission_insertGroupPermissionDetails()
RETURNS TRIGGER AS $$
BEGIN

  -- Insert into the "group_permission_detail" table
  INSERT INTO auth.group_permission_detail (group_permission_id, permission_name_id)
    SELECT NEW.pk, ap.permission_name_id
    FROM  auth."appObjects_allowed_permission" ap join auth.permission per on ap.permission_name_id = per.name_
    WHERE ap."appObjects_id" = new."appObjects_id"
    order by per.order_; 

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_group_permission_insertGroupPermissionDetails() owner to postgres;

CREATE TRIGGER trg_ai_group_permission
AFTER INSERT ON auth.group_permission
FOR EACH ROW
EXECUTE FUNCTION auth.ai_group_permission_insertGroupPermissionDetails();
CREATE OR REPLACE FUNCTION auth.ai_group_permission_insertUserPermission()
RETURNS TRIGGER AS $$
BEGIN

  -- Insert into the "group_permission_detail" table
  INSERT INTO auth.user_permission_detail (group_permission_id, permission_name_id)
    SELECT NEW.pk, ap.permission_name_id
    FROM  auth."appObjects_allowed_permission" ap join auth.permission per on ap.permission_name_id = per.name_
    WHERE ap."appObjects_id" = new."appObjects_id"
    order by per.order_; 

END;
$$ LANGUAGE plpgsql;

alter function auth.ai_group_permission_insertUserPermission() owner to postgres;

CREATE TRIGGER trg_ai_group_permission_insertUserPermission
AFTER INSERT ON auth.group_permission
FOR EACH ROW
EXECUTE FUNCTION auth.ai_group_permission_insertUserPermission();
create or replace function auth.ai_user_createUserBranch() returns trigger as $$
begin

  IF current_setting('db.single_branch') = 'true' THEN
    IF NOT EXISTS (
        SELECT 1 FROM auth.user_branch WHERE user_id = new.pk AND branch_id = 0
    ) THEN
      insert into auth.user_branch (user_id, branch_id) values (new.pk, 0);

    END IF;
  ELSE
    -- multi branch need thinking
    -- fisrt thought the admin specify the branches for user manualy
    
  END IF;

  -- Return NULL (required, but ignored)
    RETURN NULL;

end;
$$ language plpgsql;

alter function auth.ai_user_createUserBranch() owner to postgres;

create or replace trigger trg_ai_user_createUserBranch
  after insert on auth.user
  for each row
  execute procedure auth.ai_user_createUserBranch();
create or replace function auth.bi_group_createGroupRole() returns trigger as $$
declare
    v_group_name name;
begin

  IF new.app_id = 0 THEN
    RAISE EXCEPTION 'Invalid input for app id';
    return null;
  END IF;  
  
  IF NEW.rolename IS NULL THEN
    -- generate a group name based on name_
    v_group_name := current_setting('db.group_prefix') || quote_ident(new.name_);
    -- create the group
    execute 'create role '|| quote_ident(v_group_name);
    new.rolename = v_group_name;
  END IF;

  return new;

end;
$$ language plpgsql;

alter function auth.bi_group_createGroupRole() owner to postgres;

create or replace trigger trg_bi_bi_group_createGroupRole
  before insert on auth.group
  for each row
  execute procedure auth.bi_group_createGroupRole();
create or replace function auth.bi_user_createUserRole() returns trigger as $$
declare
    v_role_name name;
begin

  -- generate a role name based on username
  v_role_name := current_setting('db.user_prefix') || quote_ident(new.username);

  -- create the role
  execute 'create role '|| quote_ident(v_role_name);
  execute 'grant ' || quote_ident(v_role_name) || ' to aas_db_test_authenticator';

  new.password_ := extschema.crypt(new.password_, extschema.gen_salt('bf'));
  new.rolename := v_role_name;

  return new;

end;
$$ language plpgsql security definer;

alter function auth.bi_user_createUserRole() owner to postgres;

create or replace trigger trg_bi_user_createUserRole
  before insert on auth.user
  for each row
  execute procedure auth.bi_user_createUserRole();
CREATE OR REPLACE FUNCTION auth.bu_group_permission_detail_lockedFields()
RETURNS TRIGGER AS $$
BEGIN

    -- preventing update of group_permission_id , permission_name
    IF NEW.group_permission_id IS DISTINCT FROM OLD.group_permission_id OR NEW.permission_name_id IS DISTINCT FROM OLD.permission_name_id THEN
        RAISE EXCEPTION 'Updates to columns group_permission_id and permission_name are not allowed';
    END IF;
   
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

alter function auth.bu_group_permission_detail_lockedFields() owner to postgres;

CREATE TRIGGER trg_bu_group_permission_detail_lockedFields
BEFORE UPDATE of group_permission_id, permission_name_id ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.bu_group_permission_detail_lockedFields();
create or replace function auth.bu_user_encryptPassword() returns trigger as $$
begin

  new.password_ := extschema.crypt(new.password_, extschema.gen_salt('bf'));
  return new;

end;
$$ language plpgsql;

alter function auth.bu_user_encryptPassword() owner to postgres;

create or replace trigger trg_bu_user_encryptPassword
  before update of password_ on auth.user
  for each row
  execute procedure auth.bu_user_encryptPassword();
CREATE OR REPLACE FUNCTION person.aiu_emp_createPersonRecord()
RETURNS TRIGGER AS $$
declare
    v_new_name varchar(255);
BEGIN
    v_new_name := COALESCE(NEW.first_name,'') || COALESCE(NEW.father_name,'') || COALESCE(NEW.family_name,'');

  insert into person.person(pk, name_)
  values(new.pk , v_new_name)
  on conflict(pk)
  do  update
   set name_ = case 
                  when EXISTS ( SELECT 1 FROM person.person WHERE name_ = v_new_name)
                       then v_new_name || ' (' || COALESCE(NEW.mother_fullname,'') || ')'
                       else v_new_name
                end;
   
  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function person.aiu_emp_createPersonRecord() owner to postgres;

CREATE TRIGGER trg_aiu_emp_createPersonRecord
AFTER INSERT OR UPDATE OF first_name, father_name, family_name ON hr.emp
FOR EACH ROW
EXECUTE FUNCTION person.aiu_emp_createPersonRecord();
