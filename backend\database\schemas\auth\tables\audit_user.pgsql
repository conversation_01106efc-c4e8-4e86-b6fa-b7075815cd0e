CREATE TABLE "auth"."audit_user" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."audit_user_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObject_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "audit_user_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."audit_user" ADD CONSTRAINT "audit_user_user_id_appObject_id_unique" UNIQUE ("user_id", "appObject_id");
ALTER TABLE "auth"."audit_user" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."audit_user_id_seq" OWNED BY "auth"."audit_user"."pk";
