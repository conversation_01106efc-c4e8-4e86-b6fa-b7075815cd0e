CREATE OR REPLACE FUNCTION auth.rls_persontype(pk SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
  v_has_access BOOLEAN;
  v_user_id SMALLINT;
BEGIN
    -- get the accumulated acl persontype_id from group_personType
  v_has_access := FALSE;
  v_user_id := current_setting('myapp.user_id')::SMALLINT;

  IF v_user_id IS NULL THEN
    RAISE NOTICE 'myapp.user_id is not set.';
    RETURN FALSE;

  ELSE    

    SELECT EXISTS (
      SELECT 1
      FROM auth.user_group ug
      JOIN auth.group_branch gb ON ug.group_id = gb.group_id
      JOIN auth.group_personType gp ON gb.pk = gp.group_branch_id
      WHERE ug.user_id = v_user_id and gp.personType_id = pk
    ) INTO v_has_access;

    RETURN v_has_access;
  
  END IF;

END;
$$;

alter function auth.rls_persontype(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#;