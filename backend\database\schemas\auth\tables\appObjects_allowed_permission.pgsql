CREATE TABLE "auth"."appObjects_allowed_permission" (
 "appObject_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "permission_name_id" VARCHAR(10) REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "appObjects_allowed_permission_pkey" PRIMARY KEY ("appObject_id", "permission_name_id")
);
ALTER TABLE "auth"."appObjects_allowed_permission" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
