CREATE TABLE "hr"."decisionTransfer" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decisionTransfer_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "old_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "new_post_id" SMALLINT NOT NULL REFERENCES "enterprise"."post" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "decisionTransfer_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decisionTransfer" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."decisionTransfer_id_seq" OWNED BY "hr"."decisionTransfer"."pk";
