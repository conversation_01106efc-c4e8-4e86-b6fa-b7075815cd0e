CREATE TABLE "hr"."decision" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."decision_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
    CHECK (enterprise."opType_applicable"("opType_id", 'decision') OR enterprise."opType_applicable"("opType_id", 'note')),
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "date_" DATE NOT NULL ,
 "ref_" CHARACTER VARYING(10) NOT NULL ,
 "editor" CHARACTER VARYING(60) NOT NULL ,
 CONS<PERSON>AINT "decision_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."decision" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."decision_id_seq" OWNED BY "hr"."decision"."pk";
