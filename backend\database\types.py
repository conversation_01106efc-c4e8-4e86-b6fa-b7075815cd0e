import json
import os
from config import config
from psycopg2 import <PERSON>rro<PERSON>, sql
from .schemas import check_schema_exists, get_schema_list_from_dir, SCHEMA_DIR

def check_type_exist(cur, schema_name : str, type_name : str)->bool:
    """
    Check if a type exists in the database.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema to check
        type_name: Name of the type to check
    Returns:
        bool: True if type exists, False otherwise
    """
    cur.execute(sql.SQL("SELECT EXISTS (SELECT 1 FROM pg_type t JOIN pg_namespace n ON t.typnamespace = n.oid WHERE n.nspname = %s AND t.typname = %s)"), 
                (schema_name,type_name))
    return cur.fetchone()[0]

def create_COMPOSITE_type(cur, schema_name : str, type_name : str, columns : list[dict])->bool:
    try:
        if check_type_exist(cur, schema_name, type_name):
            config.info(f"Type {type_name} already exists in schema {schema_name}")
            
        else:
            # Convert fields dictionary into SQL-compatible format
            fields_sql = sql.SQL(", ").join([
                sql.SQL("{} {}").format(sql.Identifier(field['name']), sql.SQL(field['type']))
                for field in columns
            ])
    
            run_sql = sql.SQL("CREATE TYPE {}.{} AS ({})").format(
                sql.Identifier(schema_name), 
                sql.Identifier(type_name), 
                fields_sql
                )
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Created type {type_name} in schema {schema_name}")
            
            cur.execute(sql.SQL("ALTER TYPE {}.{} OWNER TO {}").format(
                sql.Identifier(schema_name), sql.Identifier(type_name), sql.Identifier(config.POSTGRES_DEFAULT_ADMIN) ))
            config.info(f"Granted ownership of type {type_name} to {config.POSTGRES_DEFAULT_ADMIN}")

        return True
    except Exception as e:
        config.error(f"Error creating type {type_name} in schema {schema_name}: {str(e)}")
        return False  

def create_ENUM_type(cur, schema_name : str, type_name : str , elements : list[str])->bool:
    try:
        if check_type_exist(cur, schema_name, type_name):
            config.info(f"Type {type_name} already exists in schema {schema_name}")
            
        else:
            run_sql = sql.SQL("CREATE TYPE {}.{} AS ENUM ({})").format(
                sql.Identifier(schema_name), sql.Identifier(type_name), sql.SQL(', ').join(sql.Literal(e) for e in elements))
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Created type {type_name} in schema {schema_name}")
            
            run_sql = sql.SQL("ALTER TYPE {}.{} OWNER TO {}").format(
                sql.Identifier(schema_name), sql.Identifier(type_name), sql.Identifier(config.POSTGRES_DEFAULT_ADMIN))
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Granted ownership of type {type_name} to {config.POSTGRES_DEFAULT_ADMIN}")

        return True
    except Exception as e:
        config.error(f"Error creating type {type_name} in schema {schema_name}: {str(e)}")
        return False  
 
def create_types() -> bool:

   with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        try:
            with conn.cursor() as cur:
                for schema_name in get_schema_list_from_dir():
                    if not check_schema_exists(cur, schema_name):
                        config.error(f"Schema {schema_name} does not exist")
                        return False
                    else:
                        config.info(f"Schema {schema_name} exists")
                        type_file_path = os.path.join(SCHEMA_DIR, schema_name, 'types.json')
                        if not os.path.exists(type_file_path):
                            config.info(f"Type file does not exist for schema {schema_name}")
                            continue
                        else:
                            try:
                                with open(type_file_path, 'r', encoding="utf-8") as f:
                                    conf = json.load(f)
                            except json.JSONDecodeError as e:
                                config.error(f"Error decoding JSON from {type_file_path}: {e}")
                                conn.rollback()
                                return False
                            except Exception as e:
                                config.error(f"Error reading file {type_file_path}: {e}")
                                conn.rollback()
                                return False
                            enums = conf.get("Enums", [])
                            composites = conf.get("Composites", [])
                            
                            for enum in enums:
                                if not create_ENUM_type(cur, schema_name, enum["name"], enum["values"]):
                                    config.error(f"Error creating type {enum['name']} in schema {schema_name}")
                                    return False
                            
                            for composite in composites:
                                if not create_COMPOSITE_type(cur, schema_name, composite["name"], composite["fields"]):
                                    config.error(f"Error creating type {composite['name']} in schema {schema_name}")
                                    return False
                
                conn.commit()
                return True
        except Exception as e:
            config.error(f"Error creating types for schema : {str(e)}")
            return False 

def drop_types() -> bool:
    """
    Drops all custom ENUM and COMPOSITE types in the database, excluding system schemas.
    Uses CASCADE to handle dependencies.

    Returns:
        bool: True if all found custom types were dropped successfully, False otherwise.
    """
    config.info("Attempting to drop all custom ENUM and COMPOSITE types...")
    types_to_drop = []
    try:
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
                # Find all ENUM ('e') and COMPOSITE ('c') types not in system schemas
                find_types_sql = sql.SQL("""
                    SELECT n.nspname, t.typname
                    FROM pg_type t
                    JOIN pg_namespace n ON t.typnamespace = n.oid
                    WHERE t.typtype IN ('e', 'c')
                      AND n.nspname NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
                      AND n.nspname NOT LIKE 'pg_temp_%'
                      AND n.nspname NOT LIKE 'pg_toast_temp_%';
                """)
                cur.execute(find_types_sql)
                types_to_drop = cur.fetchall()

                if not types_to_drop:
                    config.info("No custom ENUM or COMPOSITE types found to drop.")
                    return True

                config.info(f"Found {len(types_to_drop)} custom types to drop.")

                for schema_name, type_name in types_to_drop:
                    try:
                        drop_sql = sql.SQL("DROP TYPE IF EXISTS {}.{} CASCADE").format(
                            sql.Identifier(schema_name),
                            sql.Identifier(type_name)
                        )
                        config.debug(drop_sql.as_string(cur))
                        config.cumulativeSQL(drop_sql.as_string(cur))
                        cur.execute(drop_sql)
                        config.info(f"Dropped type {schema_name}.{type_name}")
                    except Error as e:
                        config.error(f"Error dropping type {schema_name}.{type_name}: {e}")
                        conn.rollback() # Rollback on first error
                        return False
                    except Exception as e:
                        config.error(f"Unexpected error dropping type {schema_name}.{type_name}: {e}")
                        conn.rollback() # Rollback on first error
                        return False

            # If loop completes without error, commit the transaction
            conn.commit()
            config.info("Successfully dropped all found custom types.")
            return True

    except Error as e:
        config.error(f"Database error during type dropping process: {e}")
        return False
    except Exception as e:
        config.error(f"Unexpected error during type dropping process: {e}")
        return False
