{"version": "90500", "data": {"id": "37187ec4-3fd0-41e2-b91c-1cd762d6a452", "offsetX": 60.023423498470024, "offsetY": -143.35747075776888, "zoom": 94.69804768446384, "gridSize": 15, "layers": [{"id": "e696d9d9-b199-4a6c-8571-f5f60229b4cf", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {"b364822b-ef61-44a3-9e36-ff9db67194e3": {"id": "b364822b-ef61-44a3-9e36-ff9db67194e3", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "89708db8-7d84-44c4-8ac0-ef08ea20c1bf", "target": "c370fd0f-14ba-418e-8c42-9512a2e5349a", "targetPort": "e46d024e-9311-4dd9-b3f6-de81dee61827", "points": [{"id": "57a9c500-76e7-4f95-939f-91e8d3fc9b7d", "type": "point", "x": 1418.9999647566806, "y": 259.60938540681997}, {"id": "9dce8798-737c-481f-ad21-9456f43ec115", "type": "point", "x": 1418.9999647566806, "y": 420.************}, {"id": "ad977b5e-b187-447b-8396-1a916910a898", "type": "point", "x": 1179.0000132138841, "y": 420.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "c370fd0f-14ba-418e-8c42-9512a2e5349a", "local_column_attnum": 2, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "8c6e7cf6-d8cd-467c-abaa-755d7fccda0c": {"id": "8c6e7cf6-d8cd-467c-abaa-755d7fccda0c", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "9f2bde5f-3b25-4069-a31f-ba9903312c5d", "target": "23c5c539-6fbc-42f0-bf40-1b3643863309", "targetPort": "a4d00117-6cdf-4add-8c6a-c7357f34d913", "points": [{"id": "d50e856b-1adf-4336-935d-6e725ec84487", "type": "point", "x": 1186.0000786404275, "y": 259.60938540681997}, {"id": "0453dfa9-9845-411c-8ca2-83fc3dadc494", "type": "point", "x": 1186.0000786404275, "y": 510.65628346179614}, {"id": "877459f3-47a6-4127-9739-d93a999220c1", "type": "point", "x": 969.0000072746438, "y": 510.65628346179614}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "23c5c539-6fbc-42f0-bf40-1b3643863309", "local_column_attnum": 2, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "7a06205f-84c3-4b5c-8fb5-384528dc477e": {"id": "7a06205f-84c3-4b5c-8fb5-384528dc477e", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "89708db8-7d84-44c4-8ac0-ef08ea20c1bf", "target": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "targetPort": "6b109833-a5e2-4fa5-83f4-3f6df2511c62", "points": [{"id": "432c582e-00d6-40ff-b3df-24e07319479a", "type": "point", "x": 1418.9999647566806, "y": 259.60938540681997}, {"id": "b4b1efdb-19e3-49da-802d-4f3a2a47ea0f", "type": "point", "x": 1418.9999647566806, "y": 754.************}, {"id": "ad85a018-cbdc-4666-be50-6bc02d96ba60", "type": "point", "x": 1418.9999647566806, "y": 844.6093931685937}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "local_column_attnum": 2, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "288258f8-3415-4043-b2c2-dde16e5624cf": {"id": "288258f8-3415-4043-b2c2-dde16e5624cf", "locked": true, "type": "one<PERSON><PERSON>", "source": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "sourcePort": "83beb79b-0a42-4a1f-8a6c-13a8f9cc07d3", "target": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "targetPort": "a5463594-71d6-4f6d-882e-5bacf0097a61", "points": [{"id": "e016347f-3fb6-4440-a265-************", "type": "point", "x": 1179.0000132138841, "y": 709.6093428888568}, {"id": "6ce9c5e8-563a-4294-b21c-4174bd1fbd6e", "type": "point", "x": 1418.9999647566806, "y": 709.6093428888568}, {"id": "0464594f-90df-4038-bd2b-f9cf68a96e88", "type": "point", "x": 1418.9999647566806, "y": 870.6562158039646}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "local_column_attnum": 3, "referenced_table_uid": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "referenced_column_attnum": 1}}, "3f4264cc-9dec-4c6d-b2a8-1ea11f5c3789": {"id": "3f4264cc-9dec-4c6d-b2a8-1ea11f5c3789", "locked": true, "type": "one<PERSON><PERSON>", "source": "5ac4a00f-3ae3-4466-9740-5392e270f539", "sourcePort": "7ed5662a-f0f4-47be-8379-fb4ea016a34c", "target": "5f7e750f-2b39-487f-a079-114090c9b86d", "targetPort": "c5b55e7a-b46b-4c06-9fcd-06eef8405acb", "points": [{"id": "72ba7749-fdae-4b50-8551-1dcd76d84681", "type": "point", "x": 473.99997025629466, "y": 514.6094064299814}, {"id": "89ab8a18-21bb-4615-a850-035ce953c152", "type": "point", "x": 473.99997025629466, "y": 465.6562683778751}, {"id": "f90537cf-4b58-4f3a-95a5-07e7296f1ea3", "type": "point", "x": 511.0000457387854, "y": 465.6562683778751}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "local_column_attnum": 2, "referenced_table_uid": "5ac4a00f-3ae3-4466-9740-5392e270f539", "referenced_column_attnum": 1}}, "2749b524-6679-4038-b63e-94f57a8c922e": {"id": "2749b524-6679-4038-b63e-94f57a8c922e", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "sourcePort": "6c7d6359-ce5e-431c-8efe-dfa4c853d103", "target": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "targetPort": "9262f308-e194-4c29-bb38-a31be633f567", "points": [{"id": "cfb3da8d-f572-435e-b9aa-ec0443120372", "type": "point", "x": 511.0000457387854, "y": 259.60938540681997}, {"id": "f1306766-89bb-40f4-8384-cd5c24142c4b", "type": "point", "x": 511.0000457387854, "y": 285.6562563814843}, {"id": "39e4de5e-3dd2-4c77-b5aa-e5d308099551", "type": "point", "x": 473.99997025629466, "y": 285.6562563814843}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "local_column_attnum": 0, "referenced_table_uid": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "referenced_column_attnum": 12}}, "70106182-a9d7-40c3-a1cb-64affe977a01": {"id": "70106182-a9d7-40c3-a1cb-64affe977a01", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "sourcePort": "71a86d14-ad2d-4d81-8553-54b7e4b56d53", "target": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "targetPort": "1ddbab9c-0e1f-4e77-8099-044bd3ee5874", "points": [{"id": "d5773a27-f8fe-4f0d-b99b-3bc9827abe97", "type": "point", "x": 218.9999975724267, "y": 299.1093717919876}, {"id": "4e34fa9d-fc39-46fc-831b-35be1d212da3", "type": "point", "x": 218.9999975724267, "y": 347.2499907731311}, {"id": "74527ee1-4f76-4d71-9924-3e49ccdb7dc5", "type": "point", "x": 241.00001968765025, "y": 347.2499907731311}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "local_column_attnum": 1, "referenced_table_uid": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "referenced_column_attnum": 1}}, "c9ccae15-fc59-444a-86ba-5ac00b1a2068": {"id": "c9ccae15-fc59-444a-86ba-5ac00b1a2068", "locked": true, "type": "one<PERSON><PERSON>", "source": "5f7e750f-2b39-487f-a079-114090c9b86d", "sourcePort": "4ebdfef6-842c-4e34-abf3-e1e055a3125e", "target": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "targetPort": "3c821855-a94f-47f6-b5cd-bbfcb2ac26c3", "points": [{"id": "e1b3bc4c-30d6-4622-8254-891dc7ce1a41", "type": "point", "x": 743.9999963074298, "y": 439.6093812901129}, {"id": "4f68e7fd-c930-49d3-9928-0e5d740c09cc", "type": "point", "x": 743.9999963074298, "y": 281.************}, {"id": "3f37f360-247e-45e0-b588-e1cfb9aacbd0", "type": "point", "x": 743.9999963074298, "y": 311.70314346924647}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "local_column_attnum": 3, "referenced_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "referenced_column_attnum": 1}}, "ce40cf5e-2861-41c7-ae3e-6a8bee40c8a1": {"id": "ce40cf5e-2861-41c7-ae3e-6a8bee40c8a1", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "9f2bde5f-3b25-4069-a31f-ba9903312c5d", "target": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "targetPort": "b8ca995b-9804-42d0-9484-86b74aadc10b", "points": [{"id": "2de648b0-ac2f-4837-9cd9-a63507f7c8f0", "type": "point", "x": 1186.0000786404275, "y": 259.60938540681997}, {"id": "aa17820b-6fa9-4a0c-8e59-3493bd18005d", "type": "point", "x": 1186.0000786404275, "y": 285.6562563814843}, {"id": "f46fbcd3-53c2-43d0-872d-efa3445cf2b0", "type": "point", "x": 743.9999963074298, "y": 285.6562563814843}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "local_column_attnum": 2, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "8d7f671d-e2bd-4568-9ed4-e43e1e9eb6c1": {"id": "8d7f671d-e2bd-4568-9ed4-e43e1e9eb6c1", "locked": true, "type": "one<PERSON><PERSON>", "source": "5f7e750f-2b39-487f-a079-114090c9b86d", "sourcePort": "4ebdfef6-842c-4e34-abf3-e1e055a3125e", "target": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "targetPort": "7cca4722-5aa4-4f9d-a885-df803e025b9e", "points": [{"id": "f8761ea8-4da5-4f18-9861-f45f03f27d98", "type": "point", "x": 743.9999963074298, "y": 439.6093812901129}, {"id": "d5a1bb21-6f49-4516-85ca-d2d48af42911", "type": "point", "x": 743.9999963074298, "y": 776.7031125432028}, {"id": "bfe9ff5b-7d95-4c6f-960b-25098532fcc7", "type": "point", "x": 743.9999963074298, "y": 806.7031321483022}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "local_column_attnum": 3, "referenced_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "referenced_column_attnum": 1}}, "be197d38-fe7c-43af-90c9-8b1a6f5c2cda": {"id": "be197d38-fe7c-43af-90c9-8b1a6f5c2cda", "locked": true, "type": "one<PERSON><PERSON>", "source": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "sourcePort": "ba5bc097-52d1-4d08-a3f7-139b0f4a4147", "target": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "targetPort": "a41b9c2b-863a-45e3-bea1-3bf48d9441e0", "points": [{"id": "f9ba4c14-fc64-4a3e-9f4d-c491ae6362d7", "type": "point", "x": 945.9999981928485, "y": 709.6093428888568}, {"id": "72b1c10e-5e6a-4427-bde1-0537df59062a", "type": "point", "x": 945.9999981928485, "y": 780.65624506054}, {"id": "fd780f51-fb9d-4e29-bfeb-00e2882eb0c1", "type": "point", "x": 743.9999963074298, "y": 780.65624506054}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "local_column_attnum": 2, "referenced_table_uid": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "referenced_column_attnum": 1}}, "f61dce8e-c974-42dd-a1f9-36cd2d79c2f8": {"id": "f61dce8e-c974-42dd-a1f9-36cd2d79c2f8", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "sourcePort": "fa1350f2-d534-4d39-bc93-6974a9e761ac", "target": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "targetPort": "365aec67-b227-45d0-9b51-526ea4c10504", "points": [{"id": "70f8e215-9197-4d38-8c92-b5f2aa89136e", "type": "point", "x": 511.0000457387854, "y": 754.6093579727777}, {"id": "928d63d2-6b4a-4151-9e5a-0032c2413d9e", "type": "point", "x": 511.0000457387854, "y": 780.65624506054}, {"id": "9470b559-821d-4145-9875-8c9517e925d8", "type": "point", "x": 459.00002968071226, "y": 780.65624506054}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "local_column_attnum": 0, "referenced_table_uid": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "referenced_column_attnum": 11}}, "2c6d8ad0-ad42-4787-94b5-c0fdaf44f9df": {"id": "2c6d8ad0-ad42-4787-94b5-c0fdaf44f9df", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "sourcePort": "71a86d14-ad2d-4d81-8553-54b7e4b56d53", "target": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "targetPort": "baec0bb2-9ccd-457d-b6d0-bf310fae81c7", "points": [{"id": "da07908a-9d1e-4ddf-9331-82bf56e60488", "type": "point", "x": 218.9999975724267, "y": 299.1093717919876}, {"id": "5ebfb522-9254-4a25-8c8a-f4139daa6c44", "type": "point", "x": 218.9999975724267, "y": 842.2499794521868}, {"id": "4b4325d5-80c4-4b09-95b6-511179e4a28c", "type": "point", "x": 226.0000146596766, "y": 842.2499794521868}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "local_column_attnum": 1, "referenced_table_uid": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "referenced_column_attnum": 1}}, "d560c109-cbf5-4752-a402-67642a5f6d88": {"id": "d560c109-cbf5-4752-a402-67642a5f6d88", "locked": true, "type": "one<PERSON><PERSON>", "source": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "sourcePort": "ecddfa1a-df40-4bdc-9ddd-380d3cc85a9d", "target": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "targetPort": "9e8ea016-248e-4035-841b-2463a7516a82", "points": [{"id": "631fad24-9586-445b-acb7-b3bca9b448f8", "type": "point", "x": 930.9999931648748, "y": 979.6093689399918}, {"id": "da5f4559-25d1-47dc-84b4-314cb146eb61", "type": "point", "x": 930.9999931648748, "y": 989.1093936085056}, {"id": "59e3bb6e-5103-4bca-9b12-e58e3613fab8", "type": "point", "x": 759.0000013354035, "y": 989.1093936085056}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "local_column_attnum": 0, "referenced_table_uid": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "referenced_column_attnum": 3}}, "b54960bc-8888-401d-863b-a835e474f692": {"id": "b54960bc-8888-401d-863b-a835e474f692", "locked": true, "type": "one<PERSON><PERSON>", "source": "5ac4a00f-3ae3-4466-9740-5392e270f539", "sourcePort": "7ed5662a-f0f4-47be-8379-fb4ea016a34c", "target": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "targetPort": "eede7ca3-b064-44c5-917a-b1e2c72b3f57", "points": [{"id": "16a14e85-c770-4409-a50a-a0f278a74084", "type": "point", "x": 473.99997025629466, "y": 514.6094064299814}, {"id": "9f3e6d3c-5be1-4da0-9add-0e47351ffe3a", "type": "point", "x": 473.99997025629466, "y": 735.6562299766189}, {"id": "3566d431-4fde-4728-a5b6-65de6c553c36", "type": "point", "x": 945.9999981928485, "y": 735.6562299766189}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "local_column_attnum": 5, "referenced_table_uid": "5ac4a00f-3ae3-4466-9740-5392e270f539", "referenced_column_attnum": 1}}, "8c27486e-53bb-48fe-a701-2999943b8770": {"id": "8c27486e-53bb-48fe-a701-2999943b8770", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "89708db8-7d84-44c4-8ac0-ef08ea20c1bf", "target": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "targetPort": "eae271bb-3941-42b0-8031-b093d54d8d77", "points": [{"id": "b36d4a28-3f33-44ac-a4ad-e1ab7f1b08b6", "type": "point", "x": 1418.9999647566806, "y": 259.60938540681997}, {"id": "fdbc1956-2330-4fb7-a0b9-70ba3d73c266", "type": "point", "x": 1418.9999647566806, "y": 1035.6561974956412}, {"id": "12ababdc-90c2-4143-87f7-1dba68503f8c", "type": "point", "x": 1418.9999647566806, "y": 1035.6562660837014}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "local_column_attnum": 0, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "230ad93f-cd13-4ed7-97aa-236c74a80122": {"id": "230ad93f-cd13-4ed7-97aa-236c74a80122", "locked": true, "type": "one<PERSON><PERSON>", "source": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "sourcePort": "83beb79b-0a42-4a1f-8a6c-13a8f9cc07d3", "target": "2315b6ab-7676-4e19-b432-738f7c03679f", "targetPort": "88a8156f-72b8-44d3-be89-d64267035c91", "points": [{"id": "291b7ef9-a7d2-49d4-a1b9-97ba4e08da34", "type": "point", "x": 1179.0000132138841, "y": 709.6093428888568}, {"id": "b301df09-782a-4a47-830c-a4c3c38dd476", "type": "point", "x": 1179.0000132138841, "y": 874.************}, {"id": "12c9b995-a56c-4afb-aea9-68cb1d754ead", "type": "point", "x": 969.0000072746438, "y": 874.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "2315b6ab-7676-4e19-b432-738f7c03679f", "local_column_attnum": 0, "referenced_table_uid": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "referenced_column_attnum": 1}}, "a381c5e6-e783-4325-9bc3-1be59f8e6a27": {"id": "a381c5e6-e783-4325-9bc3-1be59f8e6a27", "locked": true, "type": "one<PERSON><PERSON>", "source": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "sourcePort": "107a95e7-343d-40d3-ad52-f120e239a469", "target": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "targetPort": "8d3e0ed9-39da-495e-8736-5d6764520ae3", "points": [{"id": "6f19eb84-ee8c-42e6-a499-cc2f57f7ab81", "type": "point", "x": 1186.0000786404275, "y": 1009.6093789959391}, {"id": "fb6e06b8-5047-4929-99f1-945b632cf55b", "type": "point", "x": 1186.0000786404275, "y": 1005.************}, {"id": "8242bec6-f6c0-4966-929f-f36d224d6a25", "type": "point", "x": 1164.0000081859105, "y": 1005.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "local_column_attnum": 0, "referenced_table_uid": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "referenced_column_attnum": 2}}, "b2a2f028-e6f0-4670-9d45-8f3d955ec13f": {"id": "b2a2f028-e6f0-4670-9d45-8f3d955ec13f", "locked": true, "type": "one<PERSON><PERSON>", "source": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "sourcePort": "ecddfa1a-df40-4bdc-9ddd-380d3cc85a9d", "target": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "targetPort": "9e8ea016-248e-4035-841b-2463a7516a82", "points": [{"id": "ea9aed99-4673-46eb-a63d-09060c5dd292", "type": "point", "x": 930.9999931648748, "y": 979.6093689399918}, {"id": "559d4347-726e-4c76-91b9-03703b723b52", "type": "point", "x": 930.9999931648748, "y": 989.1093936085056}, {"id": "601e2616-4582-4714-a946-eef07463a9cd", "type": "point", "x": 759.0000013354035, "y": 989.1093936085056}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "local_column_attnum": 0, "referenced_table_uid": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "referenced_column_attnum": 3}}, "c27fb697-3bef-48b6-a34c-32617aaaa33d": {"id": "c27fb697-3bef-48b6-a34c-32617aaaa33d", "locked": true, "type": "one<PERSON><PERSON>", "source": "5ac4a00f-3ae3-4466-9740-5392e270f539", "sourcePort": "7ed5662a-f0f4-47be-8379-fb4ea016a34c", "target": "5f7e750f-2b39-487f-a079-114090c9b86d", "targetPort": "c5b55e7a-b46b-4c06-9fcd-06eef8405acb", "points": [{"id": "5ceeae22-c988-4fd6-a527-9c79729df212", "type": "point", "x": 473.99997025629466, "y": 514.6094064299814}, {"id": "b7c3ec82-2786-4b51-97e7-12e31c7d7af5", "type": "point", "x": 473.99997025629466, "y": 465.6562683778751}, {"id": "1f3ac4c1-807c-4ecd-b301-31f14e22af55", "type": "point", "x": 511.0000457387854, "y": 465.6562683778751}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "local_column_attnum": 2, "referenced_table_uid": "5ac4a00f-3ae3-4466-9740-5392e270f539", "referenced_column_attnum": 1}}, "b5d7f768-82fa-4022-94f9-2ae688207eee": {"id": "b5d7f768-82fa-4022-94f9-2ae688207eee", "locked": true, "type": "one<PERSON><PERSON>", "source": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "sourcePort": "71a86d14-ad2d-4d81-8553-54b7e4b56d53", "target": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "targetPort": "11763b76-7229-44bd-8823-754cd6f4bdf5", "points": [{"id": "bd09e658-ee71-4c94-a9e9-8b62ea2b1880", "type": "point", "x": 218.9999975724267, "y": 299.1093717919876}, {"id": "0a662b21-4ee4-4b85-9628-1798f5a46ae5", "type": "point", "x": 218.9999975724267, "y": 959.1093300943846}, {"id": "8bc8911a-3013-424d-af61-f0e789a6f034", "type": "point", "x": 218.9999975724267, "y": 959.1093835525583}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "local_column_attnum": 1, "referenced_table_uid": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "referenced_column_attnum": 1}}, "31cc289c-4092-4236-98dd-96028d25cf77": {"id": "31cc289c-4092-4236-98dd-96028d25cf77", "locked": true, "type": "one<PERSON><PERSON>", "source": "5f7e750f-2b39-487f-a079-114090c9b86d", "sourcePort": "f117213c-adf4-45a1-b9af-7ec7d226b3bf", "target": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "targetPort": "ffbec0d4-9501-4625-9fb5-984caa8ca725", "points": [{"id": "d8e89089-ad43-494b-ba61-50f29f9e0047", "type": "point", "x": 511.0000457387854, "y": 439.6093812901129}, {"id": "5a188ca8-4abe-4b64-a88d-7309f0ec3847", "type": "point", "x": 511.0000457387854, "y": 994.6406333777476}, {"id": "25d05496-d1e6-4170-a792-3e485b6b6242", "type": "point", "x": 218.9999975724267, "y": 994.6406333777476}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "local_column_attnum": 6, "referenced_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "referenced_column_attnum": 1}}, "f53936d5-5192-4171-b715-6d4e4382c636": {"id": "f53936d5-5192-4171-b715-6d4e4382c636", "locked": true, "type": "one<PERSON><PERSON>", "source": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "sourcePort": "9f2bde5f-3b25-4069-a31f-ba9903312c5d", "target": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "targetPort": "13fa9d5e-0fda-4fbe-8d4c-1e8cf6e0f695", "points": [{"id": "5d42163d-e00a-4260-ae25-cd022651d972", "type": "point", "x": 1186.0000786404275, "y": 259.60938540681997}, {"id": "fe330d86-1093-4521-a8f6-4935bcef720b", "type": "point", "x": 1186.0000786404275, "y": 300.65626140945795}, {"id": "d72f540e-a85e-469b-8089-d398a80a96f4", "type": "point", "x": 984.0000123026175, "y": 300.65626140945795}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "local_column_attnum": 2, "referenced_table_uid": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "referenced_column_attnum": 1}}, "e8a9134c-d382-498f-ae75-179fc8a9bf4d": {"id": "e8a9134c-d382-498f-ae75-179fc8a9bf4d", "locked": true, "type": "one<PERSON><PERSON>", "source": "5f7e750f-2b39-487f-a079-114090c9b86d", "sourcePort": "4ebdfef6-842c-4e34-abf3-e1e055a3125e", "target": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "targetPort": "c9eb9586-c79f-4889-b11d-16f72440ab66", "points": [{"id": "325959b7-1ddd-4aa4-bfd1-3dd06ad8ad33", "type": "point", "x": 743.9999963074298, "y": 439.6093812901129}, {"id": "a202250c-6936-4a87-b9a7-2f8e24433733", "type": "point", "x": 743.9999963074298, "y": 326.7031162710245}, {"id": "1d426489-b35d-4ddf-823e-6c905758815f", "type": "point", "x": 984.0000123026175, "y": 326.7031162710245}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "local_column_attnum": 3, "referenced_table_uid": "5f7e750f-2b39-487f-a079-114090c9b86d", "referenced_column_attnum": 1}}}}, {"id": "62f66d13-7ac7-41bf-aaf6-b8449651667d", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"5f7e750f-2b39-487f-a079-114090c9b86d": {"id": "5f7e750f-2b39-487f-a079-114090c9b86d", "type": "table", "selected": false, "x": 540, "y": 345, "ports": [{"id": "c5b55e7a-b46b-4c06-9fcd-06eef8405acb", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 465.6562683778751, "name": "coll-port-2-left", "alignment": "left", "parentNode": "5f7e750f-2b39-487f-a079-114090c9b86d", "links": ["3f4264cc-9dec-4c6d-b2a8-1ea11f5c3789", "c27fb697-3bef-48b6-a34c-32617aaaa33d"]}, {"id": "b1703c2f-2990-45ea-81e6-2f283c64398c", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 465.6562683778751, "name": "coll-port-2-right", "alignment": "right", "parentNode": "5f7e750f-2b39-487f-a079-114090c9b86d", "links": []}, {"id": "f117213c-adf4-45a1-b9af-7ec7d226b3bf", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 439.6093812901129, "name": "coll-port-1-left", "alignment": "left", "parentNode": "5f7e750f-2b39-487f-a079-114090c9b86d", "links": ["31cc289c-4092-4236-98dd-96028d25cf77"]}, {"id": "4ebdfef6-842c-4e34-abf3-e1e055a3125e", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 439.6093812901129, "name": "coll-port-1-right", "alignment": "right", "parentNode": "5f7e750f-2b39-487f-a079-114090c9b86d", "links": ["c9ccae15-fc59-444a-86ba-5ac00b1a2068", "8d7f671d-e2bd-4568-9ed4-e43e1e9eb6c1", "e8a9134c-d382-498f-ae75-179fc8a9bf4d"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["c5b55e7a-b46b-4c06-9fcd-06eef8405acb", "b1703c2f-2990-45ea-81e6-2f283c64398c", "f117213c-adf4-45a1-b9af-7ec7d226b3bf", "4ebdfef6-842c-4e34-abf3-e1e055a3125e"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "app_objects", "is_view_only": false, "attcompression": null, "seqrelid": 26328, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "app_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "app_objects", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "objectType", "atttypid": 26338, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "auth.objecttype", "displaytypname": "auth.objecttype", "cltype": "auth.objecttype", "inheritedfrom": null, "inheritedid": null, "elemoid": 26338, "typnspname": "auth", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "app_objects", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["auth.objecttype"]}, {"name": "name_", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "255", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "schema_", "is_primary_key": false, "attnum": 6, "cltype": "character varying", "attlen": "255", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "options", "is_primary_key": false, "attnum": 7, "cltype": "jsonb", "geometry": null, "srid": null, "attlen": null, "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "appObjects", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c151"}], "include": [], "cid": "c150"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "app_id", "referenced": "pk", "references": "5ac4a00f-3ae3-4466-9740-5392e270f539", "references_table_name": "(auth) app", "cid": "c153"}], "confupdtype": "c", "confdeltype": "c", "cid": "c152"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "5ac4a00f-3ae3-4466-9740-5392e270f539": {"id": "5ac4a00f-3ae3-4466-9740-5392e270f539", "type": "table", "selected": false, "x": 270, "y": 420, "ports": [{"id": "3e582ee4-d71b-4082-b8e5-dcba7eecf016", "type": "one<PERSON><PERSON>", "x": 271.00001968765025, "y": 514.6094064299814, "name": "coll-port-1-left", "alignment": "left", "parentNode": "5ac4a00f-3ae3-4466-9740-5392e270f539", "links": []}, {"id": "7ed5662a-f0f4-47be-8379-fb4ea016a34c", "type": "one<PERSON><PERSON>", "x": 443.99997025629466, "y": 514.6094064299814, "name": "coll-port-1-right", "alignment": "right", "parentNode": "5ac4a00f-3ae3-4466-9740-5392e270f539", "links": ["3f4264cc-9dec-4c6d-b2a8-1ea11f5c3789", "b54960bc-8888-401d-863b-a835e474f692", "c27fb697-3bef-48b6-a34c-32617aaaa33d"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["3e582ee4-d71b-4082-b8e5-dcba7eecf016", "7ed5662a-f0f4-47be-8379-fb4ea016a34c"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "appschema", "is_view_only": false, "attcompression": null, "seqrelid": 26334, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "appLabel", "atttypid": 1043, "attlen": "100", "attnum": 2, "attndims": 0, "atttypmod": 104, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(100)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "appschema", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "active_", "is_primary_key": false, "attnum": 3, "cltype": "boolean", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "default_", "is_primary_key": false, "attnum": 4, "cltype": "boolean", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "defval": "False", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "personTypeList", "is_primary_key": false, "attnum": 5, "cltype": "smallint[]", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "app", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c156"}], "include": [], "cid": "c155"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": [{"columns": [{"column": "appLabel", "cid": "c162"}], "include": [], "cid": "c161"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd": {"id": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "type": "table", "selected": false, "x": 975, "y": 615, "ports": [{"id": "ba5bc097-52d1-4d08-a3f7-139b0f4a4147", "type": "one<PERSON><PERSON>", "x": 975.9999981928485, "y": 709.6093428888568, "name": "coll-port-1-left", "alignment": "left", "parentNode": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "links": ["be197d38-fe7c-43af-90c9-8b1a6f5c2cda"]}, {"id": "83beb79b-0a42-4a1f-8a6c-13a8f9cc07d3", "type": "one<PERSON><PERSON>", "x": 1149.0000132138841, "y": 709.6093428888568, "name": "coll-port-1-right", "alignment": "right", "parentNode": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "links": ["288258f8-3415-4043-b2c2-dde16e5624cf", "230ad93f-cd13-4ed7-97aa-236c74a80122"]}, {"id": "eede7ca3-b064-44c5-917a-b1e2c72b3f57", "type": "one<PERSON><PERSON>", "x": 975.9999981928485, "y": 735.6562299766189, "name": "coll-port-5-left", "alignment": "left", "parentNode": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "links": ["b54960bc-8888-401d-863b-a835e474f692"]}, {"id": "bfd5168c-bb12-4b0d-8fb7-0481ad5c3894", "type": "one<PERSON><PERSON>", "x": 1149.0000132138841, "y": 735.6562299766189, "name": "coll-port-5-right", "alignment": "right", "parentNode": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["ba5bc097-52d1-4d08-a3f7-139b0f4a4147", "83beb79b-0a42-4a1f-8a6c-13a8f9cc07d3", "eede7ca3-b064-44c5-917a-b1e2c72b3f57", "bfd5168c-bb12-4b0d-8fb7-0481ad5c3894"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "group", "is_view_only": false, "attcompression": null, "seqrelid": 26330, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "app_id", "is_primary_key": false, "attnum": 5, "cltype": "smallint", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "defval": "0", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "name_", "atttypid": 1043, "attlen": "150", "attnum": 2, "attndims": 0, "atttypmod": 154, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(150)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "group", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "description", "is_primary_key": false, "attnum": 4, "cltype": "text", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "rolename", "is_primary_key": false, "attnum": 3, "cltype": "text", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "group", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c118"}], "include": [], "cid": "c117"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "app_id", "referenced": "pk", "references": "5ac4a00f-3ae3-4466-9740-5392e270f539", "references_table_name": "app", "cid": "c120"}], "confupdtype": "c", "confdeltype": "c", "cid": "c119"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": [{"oid": 26422, "name": "group_name__key", "col_count": 1, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "name_", "cid": "c128"}], "include": [], "cid": "c127"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "7a88dff0-8c4d-411d-a9d9-d0185deb62be": {"id": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "type": "table", "selected": false, "x": 540, "y": 660, "ports": [{"id": "60df51d3-a5c7-4d2c-8be3-81b907f490bc", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 806.7031321483022, "name": "coll-port-3-left", "alignment": "left", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": []}, {"id": "348f93cf-829b-4149-9dbb-256780c66c9d", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 780.65624506054, "name": "coll-port-2-left", "alignment": "left", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": []}, {"id": "7cca4722-5aa4-4f9d-a885-df803e025b9e", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 806.7031321483022, "name": "coll-port-3-right", "alignment": "right", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": ["8d7f671d-e2bd-4568-9ed4-e43e1e9eb6c1"]}, {"id": "a41b9c2b-863a-45e3-bea1-3bf48d9441e0", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 780.65624506054, "name": "coll-port-2-right", "alignment": "right", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": ["be197d38-fe7c-43af-90c9-8b1a6f5c2cda"]}, {"id": "fa1350f2-d534-4d39-bc93-6974a9e761ac", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 754.6093579727777, "name": "coll-port-11-left", "alignment": "left", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": ["f61dce8e-c974-42dd-a1f9-36cd2d79c2f8"]}, {"id": "2b121788-f875-49ea-8281-85877c33e73f", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 754.6093579727777, "name": "coll-port-11-right", "alignment": "right", "parentNode": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["60df51d3-a5c7-4d2c-8be3-81b907f490bc", "348f93cf-829b-4149-9dbb-256780c66c9d", "7cca4722-5aa4-4f9d-a885-df803e025b9e", "a41b9c2b-863a-45e3-bea1-3bf48d9441e0", "fa1350f2-d534-4d39-bc93-6974a9e761ac", "2b121788-f875-49ea-8281-85877c33e73f"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 11, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "group_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "group_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "appObjects_id", "atttypid": 23, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "group_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}], "name": "group_permission", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "appObjects_id", "referenced": "pk", "references": "5f7e750f-2b39-487f-a079-114090c9b86d", "references_table_name": "appObjects", "cid": "c15"}], "confupdtype": "c", "confdeltype": "a", "cid": "c14"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "group_id", "referenced": "pk", "references": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "references_table_name": "group", "cid": "c17"}], "confupdtype": "c", "confdeltype": "a", "cid": "c16"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "d6a9f9ed-34da-4191-b9a3-2180523e5faf": {"id": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "type": "table", "selected": false, "x": 1215, "y": 165, "ports": [{"id": "9f2bde5f-3b25-4069-a31f-ba9903312c5d", "type": "one<PERSON><PERSON>", "x": 1216.0000786404275, "y": 259.60938540681997, "name": "coll-port-1-left", "alignment": "left", "parentNode": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "links": ["8c6e7cf6-d8cd-467c-abaa-755d7fccda0c", "ce40cf5e-2861-41c7-ae3e-6a8bee40c8a1", "f53936d5-5192-4171-b715-6d4e4382c636"]}, {"id": "89708db8-7d84-44c4-8ac0-ef08ea20c1bf", "type": "one<PERSON><PERSON>", "x": 1388.9999647566806, "y": 259.60938540681997, "name": "coll-port-1-right", "alignment": "right", "parentNode": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "links": ["b364822b-ef61-44a3-9e36-ff9db67194e3", "7a06205f-84c3-4b5c-8fb5-384528dc477e", "8c27486e-53bb-48fe-a701-2999943b8770"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["9f2bde5f-3b25-4069-a31f-ba9903312c5d", "89708db8-7d84-44c4-8ac0-ef08ea20c1bf"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 23, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": 26329, "seqtypid": 23, "seqstart": "1", "seqincrement": "1", "seqmax": "2147483647", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"], "cid": "c14"}, {"name": "is_active", "atttypid": 16, "attlen": null, "attnum": 10, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "False", "typname": "boolean", "displaytypname": "boolean", "cltype": "boolean", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "boolean", "boolean", "character", "character varying", "text"], "cid": "c15"}, {"name": "mustChangePassword", "is_primary_key": false, "attnum": 13, "cltype": "boolean", "attlen": null, "attprecision": null, "defval": "True", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c16"}, {"name": "lastLogin", "atttypid": 1184, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"], "cid": "c17"}, {"name": "joinedDate", "atttypid": 1184, "attlen": null, "attnum": 11, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"], "cid": "c18"}, {"name": "password_", "atttypid": 1043, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": 132, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(128)", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c19"}, {"name": "username", "atttypid": 1043, "attlen": "150", "attnum": 5, "attndims": 0, "atttypmod": 154, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(150)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c20"}, {"name": "firstName", "atttypid": 1043, "attlen": "30", "attnum": 6, "attndims": 0, "atttypmod": 34, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(30)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c21"}, {"name": "lastName", "atttypid": 1043, "attlen": "30", "attnum": 7, "attndims": 0, "atttypmod": 34, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(30)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c22"}, {"name": "email", "atttypid": 1043, "attlen": "254", "attnum": 8, "attndims": 0, "atttypmod": 258, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(254)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c23"}, {"name": "phone", "atttypid": 1043, "attlen": "15", "attnum": 12, "attndims": 0, "atttypmod": 19, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(15)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "character varying", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"], "cid": "c24"}, {"name": "rolename", "is_primary_key": false, "attnum": 14, "cltype": "text", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c25"}], "name": "user", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": [{"columns": [{"column": "username", "cid": "c27"}], "include": [], "cid": "c26"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "6ce73bc9-66f3-4e11-bb58-24be5b1e9628": {"id": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "type": "table", "selected": true, "x": 1215, "y": 750, "ports": [{"id": "556287a3-7002-4412-8bb0-d9762316afb7", "type": "one<PERSON><PERSON>", "x": 1216.0000786404275, "y": 870.6562158039646, "name": "coll-port-3-left", "alignment": "left", "parentNode": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "links": []}, {"id": "a5463594-71d6-4f6d-882e-5bacf0097a61", "type": "one<PERSON><PERSON>", "x": 1388.9999647566806, "y": 870.6562158039646, "name": "coll-port-3-right", "alignment": "right", "parentNode": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "links": ["288258f8-3415-4043-b2c2-dde16e5624cf"]}, {"id": "302c3853-72f4-4767-929d-a0f3248e0425", "type": "one<PERSON><PERSON>", "x": 1216.0000786404275, "y": 844.6093931685937, "name": "coll-port-2-left", "alignment": "left", "parentNode": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "links": []}, {"id": "6b109833-a5e2-4fa5-83f4-3f6df2511c62", "type": "one<PERSON><PERSON>", "x": 1388.9999647566806, "y": 844.6093931685937, "name": "coll-port-2-right", "alignment": "right", "parentNode": "6ce73bc9-66f3-4e11-bb58-24be5b1e9628", "links": ["7a06205f-84c3-4b5c-8fb5-384528dc477e"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["556287a3-7002-4412-8bb0-d9762316afb7", "a5463594-71d6-4f6d-882e-5bacf0097a61", "302c3853-72f4-4767-929d-a0f3248e0425", "6b109833-a5e2-4fa5-83f4-3f6df2511c62"], "otherInfo": {"data": {"columns": [{"name": "user_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_groups", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "group_id", "atttypid": 23, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_groups", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}], "name": "user_group", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "user_id", "cid": "c313"}, {"column": "group_id", "cid": "c314"}], "include": [], "cid": "c312"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "user", "cid": "c316"}], "confupdtype": "c", "confdeltype": "a", "cid": "c315"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "group_id", "referenced": "pk", "references": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "references_table_name": "group", "cid": "c318"}], "confupdtype": "c", "confdeltype": "a", "cid": "c317"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": [{"oid": 26430, "name": "user_groups_user_id_group_id_key", "col_count": 2, "indnullsnotdistinct": false, "spcname": "pg_default", "comment": null, "condeferrable": false, "condeferred": false, "conislocal": true, "fillfactor": null, "columns": [{"column": "user_id", "cid": "c322"}, {"column": "group_id", "cid": "c323"}], "include": [], "cid": "c321"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "c370fd0f-14ba-418e-8c42-9512a2e5349a": {"id": "c370fd0f-14ba-418e-8c42-9512a2e5349a", "type": "table", "selected": false, "x": 975, "y": 300, "ports": [{"id": "b0a85020-6cc9-489d-9688-b56c25<PERSON><PERSON><PERSON>", "type": "one<PERSON><PERSON>", "x": 975.9999981928485, "y": 420.************, "name": "coll-port-2-left", "alignment": "left", "parentNode": "c370fd0f-14ba-418e-8c42-9512a2e5349a", "links": []}, {"id": "e46d024e-9311-4dd9-b3f6-de81dee61827", "type": "one<PERSON><PERSON>", "x": 1149.0000132138841, "y": 420.************, "name": "coll-port-2-right", "alignment": "right", "parentNode": "c370fd0f-14ba-418e-8c42-9512a2e5349a", "links": ["b364822b-ef61-44a3-9e36-ff9db67194e3"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["b0a85020-6cc9-489d-9688-b56c25<PERSON><PERSON><PERSON>", "e46d024e-9311-4dd9-b3f6-de81dee61827"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 20, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "bigint", "displaytypname": "bigint", "cltype": "bigint", "inheritedfrom": null, "inheritedid": null, "elemoid": 20, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": 26335, "seqtypid": 20, "seqstart": "1", "seqincrement": "1", "seqmax": "9223372036854775807", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "user_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "timstamp", "atttypid": 1184, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp with time zone", "displaytypname": "timestamp with time zone", "cltype": "timestamp with time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1184, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "information_schema.time_stamp", "time with time zone", "time without time zone", "timestamp with time zone", "timestamp with time zone", "timestamp without time zone"]}, {"name": "userAction", "atttypid": 26346, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "auth.objectaction", "displaytypname": "auth.objectaction", "cltype": "auth.useraction", "inheritedfrom": null, "inheritedid": null, "elemoid": 26346, "typnspname": "auth", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["auth.objectaction"]}, {"name": "objectType", "atttypid": 26338, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "auth.objecttype", "displaytypname": "auth.objecttype", "cltype": "auth.objecttype", "inheritedfrom": null, "inheritedid": null, "elemoid": 26338, "typnspname": "auth", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["auth.objecttype"]}, {"name": "objectName", "atttypid": 1043, "attlen": "50", "attnum": 6, "attndims": 0, "atttypmod": 54, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(50)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}, {"name": "arg", "atttypid": 1043, "attlen": "255", "attnum": 7, "attndims": 0, "atttypmod": 259, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "character varying", "displaytypname": "character varying(255)", "cltype": "character varying", "inheritedfrom": null, "inheritedid": null, "elemoid": 1043, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_logs", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "character varying", "character varying", "information_schema.character_data", "information_schema.yes_or_no", "name", "regclass", "text"]}], "name": "userLog", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "user", "cid": "c15"}], "confupdtype": "c", "confdeltype": "a", "cid": "c14"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "9d7a5a8f-b62f-40f5-92de-75faf72641e3": {"id": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "type": "table", "selected": false, "x": 540, "y": 165, "ports": [{"id": "a21afb4d-b41f-41a4-9592-94937dc0af72", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 311.70314346924647, "name": "coll-port-3-left", "alignment": "left", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": []}, {"id": "bd4f895a-f8c7-4a83-b9bf-b4437e5dcb07", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 285.6562563814843, "name": "coll-port-2-left", "alignment": "left", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": []}, {"id": "6c7d6359-ce5e-431c-8efe-dfa4c853d103", "type": "one<PERSON><PERSON>", "x": 541.0000457387854, "y": 259.60938540681997, "name": "coll-port-12-left", "alignment": "left", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": ["2749b524-6679-4038-b63e-94f57a8c922e"]}, {"id": "e791817a-fb72-42d2-8a04-688efb05e25c", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 259.60938540681997, "name": "coll-port-12-right", "alignment": "right", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": []}, {"id": "3c821855-a94f-47f6-b5cd-bbfcb2ac26c3", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 311.70314346924647, "name": "coll-port-3-right", "alignment": "right", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": ["c9ccae15-fc59-444a-86ba-5ac00b1a2068"]}, {"id": "b8ca995b-9804-42d0-9484-86b74aadc10b", "type": "one<PERSON><PERSON>", "x": 713.9999963074298, "y": 285.6562563814843, "name": "coll-port-2-right", "alignment": "right", "parentNode": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "links": ["ce40cf5e-2861-41c7-ae3e-6a8bee40c8a1"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["a21afb4d-b41f-41a4-9592-94937dc0af72", "bd4f895a-f8c7-4a83-b9bf-b4437e5dcb07", "6c7d6359-ce5e-431c-8efe-dfa4c853d103", "e791817a-fb72-42d2-8a04-688efb05e25c", "3c821855-a94f-47f6-b5cd-bbfcb2ac26c3", "b8ca995b-9804-42d0-9484-86b74aadc10b"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 12, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c110"}, {"name": "user_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"], "cid": "c111"}, {"name": "appObjects_id", "atttypid": 23, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"], "cid": "c112"}], "name": "user_permission", "description": "The permission given for a user that is not included in ordinary group", "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c109"}], "include": [], "cid": "c108"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "appObjects_id", "referenced": "pk", "references": "5f7e750f-2b39-487f-a079-114090c9b86d", "references_table_name": "appObjects", "cid": "c46"}], "confupdtype": "c", "confdeltype": "a", "cid": "c45"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "user", "cid": "c48"}], "confupdtype": "c", "confdeltype": "a", "cid": "c47"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "23c5c539-6fbc-42f0-bf40-1b3643863309": {"id": "23c5c539-6fbc-42f0-bf40-1b3643863309", "type": "table", "selected": false, "x": 765, "y": 390, "ports": [{"id": "af3696ad-4f5e-4838-bf09-698c897ea1f9", "type": "one<PERSON><PERSON>", "x": 765.9999922536082, "y": 510.65628346179614, "name": "coll-port-2-left", "alignment": "left", "parentNode": "23c5c539-6fbc-42f0-bf40-1b3643863309", "links": []}, {"id": "a4d00117-6cdf-4add-8c6a-c7357f34d913", "type": "one<PERSON><PERSON>", "x": 939.0000072746438, "y": 510.65628346179614, "name": "coll-port-2-right", "alignment": "right", "parentNode": "23c5c539-6fbc-42f0-bf40-1b3643863309", "links": ["8c6e7cf6-d8cd-467c-abaa-755d7fccda0c"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["af3696ad-4f5e-4838-bf09-698c897ea1f9", "a4d00117-6cdf-4add-8c6a-c7357f34d913"], "otherInfo": {"data": {"columns": [{"name": "pk", "atttypid": 20, "attlen": null, "attnum": 1, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "bigint", "displaytypname": "bigint", "cltype": "bigint", "inheritedfrom": null, "inheritedid": null, "elemoid": 20, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": 26336, "seqtypid": 20, "seqstart": "1", "seqincrement": "1", "seqmax": "9223372036854775807", "seqmin": "1", "seqcache": "1", "seqcycle": false, "is_pk": true, "is_primary_key": true, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "user_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "revoked", "atttypid": 16, "attlen": null, "attnum": 6, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": false, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "", "typname": "boolean", "displaytypname": "boolean", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 16, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["boolean", "character", "character varying", "text"], "min_val_attlen": 0, "max_val_attlen": 6}, {"name": "expires", "atttypid": 1114, "attlen": null, "attnum": 4, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "created", "atttypid": 1114, "attlen": null, "attnum": 5, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": "now()", "typname": "timestamp without time zone", "displaytypname": "timestamp without time zone", "cltype": "timestamp without time zone", "inheritedfrom": null, "inheritedid": null, "elemoid": 1114, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["date", "time without time zone", "timestamp with time zone", "timestamp without time zone", "timestamp without time zone", "timestamp without time zone"]}, {"name": "token_", "atttypid": 25, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "x", "attidentity": "", "defval": null, "typname": "text", "displaytypname": "text", "cltype": "text", "inheritedfrom": null, "inheritedid": null, "elemoid": 25, "typnspname": "pg_catalog", "defaultstorage": "x", "description": null, "indkey": "1", "isdup": false, "collspcname": "pg_catalog.\"default\"", "is_fk": false, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_tokens", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["\"char\"", "character", "character varying", "name", "regclass", "text"]}], "name": "userToken", "description": null, "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c126"}], "include": [], "cid": "c125"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "user", "cid": "c128"}], "confupdtype": "c", "confdeltype": "a", "cid": "c127"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "4c19c236-6a2a-48fb-b82b-7200dd08fde3": {"id": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "type": "table", "selected": false, "x": 15, "y": 195, "ports": [{"id": "f46ed0a9-cb87-4b04-9db7-986caa3eefe6", "type": "one<PERSON><PERSON>", "x": 15.999998664488846, "y": 299.1093717919876, "name": "coll-port-1-left", "alignment": "left", "parentNode": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "links": []}, {"id": "71a86d14-ad2d-4d81-8553-54b7e4b56d53", "type": "one<PERSON><PERSON>", "x": 188.9999975724267, "y": 299.1093717919876, "name": "coll-port-1-right", "alignment": "right", "parentNode": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "links": ["70106182-a9d7-40c3-a1cb-64affe977a01", "2c6d8ad0-ad42-4787-94b5-c0fdaf44f9df", "b5d7f768-82fa-4022-94f9-2ae688207eee"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["f46ed0a9-cb87-4b04-9db7-986caa3eefe6", "71a86d14-ad2d-4d81-8553-54b7e4b56d53"], "otherInfo": {"data": {"columns": [{"name": "name_", "is_primary_key": true, "attnum": 1, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c76"}, {"name": "privilege", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c77"}, {"name": "binaryCode", "is_primary_key": false, "attnum": 4, "cltype": "bit", "attlen": "4", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c78"}, {"name": "order_", "is_primary_key": false, "attnum": 6, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c79"}, {"name": "objectType", "is_primary_key": false, "attnum": 3, "cltype": "auth.objecttype", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "cid": "c80"}], "name": "permission", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "name_", "cid": "c75"}], "include": [], "cid": "c74"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "317c582a-9526-4daa-8c8b-17c8c1ccae26": {"id": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "type": "table", "selected": false, "x": 270, "y": 165, "ports": [{"id": "cf5f8615-438f-4b6e-84e6-b1b62da4578e", "type": "one<PERSON><PERSON>", "x": 271.00001968765025, "y": 285.6562563814843, "name": "coll-port-0-left", "alignment": "left", "parentNode": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "links": []}, {"id": "9262f308-e194-4c29-bb38-a31be633f567", "type": "one<PERSON><PERSON>", "x": 443.99997025629466, "y": 285.6562563814843, "name": "coll-port-0-right", "alignment": "right", "parentNode": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "links": ["2749b524-6679-4038-b63e-94f57a8c922e"]}, {"id": "1ddbab9c-0e1f-4e77-8099-044bd3ee5874", "type": "one<PERSON><PERSON>", "x": 271.00001968765025, "y": 347.2499907731311, "name": "coll-port-1-left", "alignment": "left", "parentNode": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "links": ["70106182-a9d7-40c3-a1cb-64affe977a01"]}, {"id": "400f0f3c-f4f1-4c82-b788-4bd3c2ae2d66", "type": "one<PERSON><PERSON>", "x": 443.99997025629466, "y": 347.2499907731311, "name": "coll-port-1-right", "alignment": "right", "parentNode": "317c582a-9526-4daa-8c8b-17c8c1ccae26", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["cf5f8615-438f-4b6e-84e6-b1b62da4578e", "9262f308-e194-4c29-bb38-a31be633f567", "1ddbab9c-0e1f-4e77-8099-044bd3ee5874", "400f0f3c-f4f1-4c82-b788-4bd3c2ae2d66"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 5, "cltype": "bigint", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "user_permission_id", "is_primary_key": false, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "allow", "is_primary_key": false, "attnum": 4, "cltype": "boolean", "attlen": null, "attprecision": null, "defval": "False", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "permission_name_id", "is_primary_key": false, "attnum": 1, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "fieldlist", "is_primary_key": false, "attnum": 2, "cltype": "character varying[]", "attlen": null, "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "user_permission_detail", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "user_permission_pk", "cid": "c13"}, {"column": "permission_name_", "cid": "c14"}, {"column": "pk"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_permission_pk", "referenced": "pk", "references_table_name": "user_permission", "references": "9d7a5a8f-b62f-40f5-92de-75faf72641e3", "cid": "c16"}], "confupdtype": "a", "confdeltype": "a", "cid": "c15"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "permission_name_", "referenced": "name_", "references_table_name": "permission", "references": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "cid": "c18"}], "confupdtype": "a", "confdeltype": "a", "cid": "c17"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "036f1b68-e651-41ff-aa2a-ed5988de73bc": {"id": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "type": "table", "selected": false, "x": 255, "y": 660, "ports": [{"id": "e532a123-080d-421a-9fef-af30a639e8b9", "type": "one<PERSON><PERSON>", "x": 256.0000146596766, "y": 780.65624506054, "name": "coll-port-0-left", "alignment": "left", "parentNode": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "links": []}, {"id": "365aec67-b227-45d0-9b51-526ea4c10504", "type": "one<PERSON><PERSON>", "x": 429.00002968071226, "y": 780.65624506054, "name": "coll-port-0-right", "alignment": "right", "parentNode": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "links": ["f61dce8e-c974-42dd-a1f9-36cd2d79c2f8"]}, {"id": "baec0bb2-9ccd-457d-b6d0-bf310fae81c7", "type": "one<PERSON><PERSON>", "x": 256.0000146596766, "y": 842.2499794521868, "name": "coll-port-1-left", "alignment": "left", "parentNode": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "links": ["2c6d8ad0-ad42-4787-94b5-c0fdaf44f9df"]}, {"id": "4ce6cb31-1710-431c-865d-d99b75ea4344", "type": "one<PERSON><PERSON>", "x": 429.00002968071226, "y": 842.2499794521868, "name": "coll-port-1-right", "alignment": "right", "parentNode": "036f1b68-e651-41ff-aa2a-ed5988de73bc", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["e532a123-080d-421a-9fef-af30a639e8b9", "365aec67-b227-45d0-9b51-526ea4c10504", "baec0bb2-9ccd-457d-b6d0-bf310fae81c7", "4ce6cb31-1710-431c-865d-d99b75ea4344"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 5, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "group_permission_id", "is_primary_key": false, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "allow", "is_primary_key": false, "attnum": 4, "cltype": "boolean", "attlen": null, "attprecision": null, "defval": "False", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "permission_name_id", "is_primary_key": false, "attnum": 1, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "fieldlist", "is_primary_key": false, "attnum": 2, "cltype": "character varying[]", "attlen": null, "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "group_permission_detail", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "group_permission_pk", "cid": "c41"}, {"column": "permission_name_", "cid": "c42"}, {"column": "pk", "cid": "c43"}], "include": [], "cid": "c40"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "group_permission_pk", "referenced": "pk", "references_table_name": "group_permission", "references": "7a88dff0-8c4d-411d-a9d9-d0185deb62be", "cid": "c45"}], "confupdtype": "a", "confdeltype": "a", "cid": "c44"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "permission_name_", "referenced": "name_", "references_table_name": "permission", "references": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "cid": "c47"}], "confupdtype": "a", "confdeltype": "a", "cid": "c46"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "8182c05e-69a9-4a22-8464-1a5e15cc62c4": {"id": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "type": "table", "selected": false, "x": 960, "y": 885, "ports": [{"id": "d8df613a-6a84-4c0a-a285-791db5321510", "type": "one<PERSON><PERSON>", "x": 960.9999931648748, "y": 1005.************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "links": []}, {"id": "8d3e0ed9-39da-495e-8736-5d6764520ae3", "type": "one<PERSON><PERSON>", "x": 1134.0000081859105, "y": 1005.************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "links": ["a381c5e6-e783-4325-9bc3-1be59f8e6a27"]}, {"id": "ecddfa1a-df40-4bdc-9ddd-380d3cc85a9d", "type": "one<PERSON><PERSON>", "x": 960.9999931648748, "y": 979.6093689399918, "name": "coll-port-3-left", "alignment": "left", "parentNode": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "links": ["d560c109-cbf5-4752-a402-67642a5f6d88", "b2a2f028-e6f0-4670-9d45-8f3d955ec13f"]}, {"id": "79ff54ef-01cd-4a5d-862b-a6a43abbfd3e", "type": "one<PERSON><PERSON>", "x": 1134.0000081859105, "y": 979.6093689399918, "name": "coll-port-3-right", "alignment": "right", "parentNode": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["d8df613a-6a84-4c0a-a285-791db5321510", "8d3e0ed9-39da-495e-8736-5d6764520ae3", "ecddfa1a-df40-4bdc-9ddd-380d3cc85a9d", "79ff54ef-01cd-4a5d-862b-a6a43abbfd3e"], "otherInfo": {"data": {"name": "user_department", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_branch_id", "referenced": "pk", "references": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "references_table_name": "(auth) user_branch", "cid": "c15"}], "confupdtype": "c", "confdeltype": "c", "cid": "c14"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 3, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "user_branch_id", "is_primary_key": false, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "department_id", "is_primary_key": false, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "b7c84009-1ea9-4ff6-ab9a-30087d94c810": {"id": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "type": "table", "selected": false, "x": 555, "y": 885, "ports": [{"id": "0e6d762c-e88a-4aa4-afa3-39da2c411b87", "type": "one<PERSON><PERSON>", "x": 555.9999863143678, "y": 989.1093936085056, "name": "coll-port-0-left", "alignment": "left", "parentNode": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "links": []}, {"id": "9e8ea016-248e-4035-841b-2463a7516a82", "type": "one<PERSON><PERSON>", "x": 729.0000013354035, "y": 989.1093936085056, "name": "coll-port-0-right", "alignment": "right", "parentNode": "b7c84009-1ea9-4ff6-ab9a-30087d94c810", "links": ["d560c109-cbf5-4752-a402-67642a5f6d88", "b2a2f028-e6f0-4670-9d45-8f3d955ec13f"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["0e6d762c-e88a-4aa4-afa3-39da2c411b87", "9e8ea016-248e-4035-841b-2463a7516a82"], "otherInfo": {"data": {"columns": [{"name": "user_department_id", "is_primary_key": true, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "optype_id", "is_primary_key": true, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "allow", "is_primary_key": false, "attnum": 2, "cltype": "boolean", "attlen": null, "attprecision": null, "defval": "False", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "user_department_optypes", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "user_department_id"}, {"column": "optype_id"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_department_id", "referenced": "pk", "references": "8182c05e-69a9-4a22-8464-1a5e15cc62c4", "references_table_name": "user_department", "cid": "c521"}], "confupdtype": "c", "confdeltype": "c"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "2315b6ab-7676-4e19-b432-738f7c03679f": {"id": "2315b6ab-7676-4e19-b432-738f7c03679f", "type": "table", "selected": false, "x": 765, "y": 780, "ports": [{"id": "960d48c0-a514-4a62-a025-3798a9be3164", "type": "one<PERSON><PERSON>", "x": 765.9999922536082, "y": 874.************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "2315b6ab-7676-4e19-b432-738f7c03679f", "links": []}, {"id": "88a8156f-72b8-44d3-be89-d64267035c91", "type": "one<PERSON><PERSON>", "x": 939.0000072746438, "y": 874.************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "2315b6ab-7676-4e19-b432-738f7c03679f", "links": ["230ad93f-cd13-4ed7-97aa-236c74a80122"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["960d48c0-a514-4a62-a025-3798a9be3164", "88a8156f-72b8-44d3-be89-d64267035c91"], "otherInfo": {"data": {"columns": [{"name": "group_id", "is_primary_key": true, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "personType_id", "is_primary_key": true, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "allow", "is_primary_key": false, "attnum": 2, "cltype": "boolean", "attlen": null, "attprecision": null, "defval": "False", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "group_personType", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "group_id"}, {"column": "personType_id"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "group_id", "referenced": "pk", "references": "54e754c4-6ce9-4f8d-94ca-16ad85fcaffd", "references_table_name": "group", "cid": "c165"}], "confupdtype": "c", "confdeltype": "c", "cid": "c164"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "0a6aa001-feb3-4ea4-91ef-1b24156baac3": {"id": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "type": "table", "selected": false, "x": 1215, "y": 915, "ports": [{"id": "2598d7ae-cfd6-49ae-aa0e-321d965a5e37", "type": "one<PERSON><PERSON>", "x": 1216.0000786404275, "y": 1035.6562660837014, "name": "coll-port-0-left", "alignment": "left", "parentNode": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "links": []}, {"id": "eae271bb-3941-42b0-8031-b093d54d8d77", "type": "one<PERSON><PERSON>", "x": 1388.9999647566806, "y": 1035.6562660837014, "name": "coll-port-0-right", "alignment": "right", "parentNode": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "links": ["8c27486e-53bb-48fe-a701-2999943b8770"]}, {"id": "107a95e7-343d-40d3-ad52-f120e239a469", "type": "one<PERSON><PERSON>", "x": 1216.0000786404275, "y": 1009.6093789959391, "name": "coll-port-2-left", "alignment": "left", "parentNode": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "links": ["a381c5e6-e783-4325-9bc3-1be59f8e6a27"]}, {"id": "06f4dce8-fb3a-4834-84f5-f7465d0d6e25", "type": "one<PERSON><PERSON>", "x": 1388.9999647566806, "y": 1009.6093789959391, "name": "coll-port-2-right", "alignment": "right", "parentNode": "0a6aa001-feb3-4ea4-91ef-1b24156baac3", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["2598d7ae-cfd6-49ae-aa0e-321d965a5e37", "eae271bb-3941-42b0-8031-b093d54d8d77", "107a95e7-343d-40d3-ad52-f120e239a469", "06f4dce8-fb3a-4834-84f5-f7465d0d6e25"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 2, "cltype": "smallint", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "user_id", "is_primary_key": false, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "user_branch", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "user", "cid": "c274"}], "confupdtype": "c", "confdeltype": "c", "cid": "c273"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "64cb93a8-5d89-42be-96b5-7f18c1811e7d": {"id": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "type": "table", "selected": false, "x": 15, "y": 855, "ports": [{"id": "d3051832-e330-4608-bf6e-6c4b751ae978", "type": "one<PERSON><PERSON>", "x": 15.999998664488846, "y": 959.1093835525583, "name": "coll-port-1-left", "alignment": "left", "parentNode": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "links": []}, {"id": "11763b76-7229-44bd-8823-754cd6f4bdf5", "type": "one<PERSON><PERSON>", "x": 188.9999975724267, "y": 959.1093835525583, "name": "coll-port-1-right", "alignment": "right", "parentNode": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "links": ["b5d7f768-82fa-4022-94f9-2ae688207eee"]}, {"id": "9ef0280f-5c5f-4513-a48e-cdf93d9356c3", "type": "one<PERSON><PERSON>", "x": 15.999998664488846, "y": 994.6406333777476, "name": "coll-port-6-left", "alignment": "left", "parentNode": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "links": []}, {"id": "ffbec0d4-9501-4625-9fb5-984caa8ca725", "type": "one<PERSON><PERSON>", "x": 188.9999975724267, "y": 994.6406333777476, "name": "coll-port-6-right", "alignment": "right", "parentNode": "64cb93a8-5d89-42be-96b5-7f18c1811e7d", "links": ["31cc289c-4092-4236-98dd-96028d25cf77"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["d3051832-e330-4608-bf6e-6c4b751ae978", "11763b76-7229-44bd-8823-754cd6f4bdf5", "9ef0280f-5c5f-4513-a48e-cdf93d9356c3", "ffbec0d4-9501-4625-9fb5-984caa8ca725"], "otherInfo": {"data": {"columns": [{"name": "permission_name_id", "is_primary_key": true, "attnum": 1, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "appObject_id", "is_primary_key": true, "attnum": 6, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "appObjects_allowed_permission", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "name_", "cid": "c217"}, {"column": "appObject_id", "cid": "c218"}], "include": [], "cid": "c216"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "permission_name_id", "referenced": "name_", "references": "4c19c236-6a2a-48fb-b82b-7200dd08fde3", "references_table_name": "(auth) permission", "cid": "c220"}], "confupdtype": "c", "confdeltype": "c", "cid": "c219"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "appObject_id", "referenced": "pk", "references": "5f7e750f-2b39-487f-a079-114090c9b86d", "references_table_name": "appObjects", "cid": "c222"}], "confupdtype": "c", "confdeltype": "c", "cid": "c221"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "b562e7a6-1c13-4a46-8726-d1b1e1707a7e": {"id": "b562e7a6-1c13-4a46-8726-d1b1e1707a7e", "type": "table", "selected": false, "x": 15, "y": 480, "ports": [], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": [], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "user_id", "is_primary_key": false, "attnum": 6, "cltype": "smallint", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "logged_at", "is_primary_key": false, "attnum": 1, "cltype": "timestamp without time zone", "geometry": null, "srid": null, "attlen": null, "min_val_attlen": 0, "max_val_attlen": 6, "attprecision": null, "defval": "now()", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "table_name", "is_primary_key": false, "attnum": 8, "cltype": "text", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "row_id", "is_primary_key": false, "attnum": 7, "cltype": "text", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "action_type", "is_primary_key": false, "attnum": 5, "cltype": "auth.action", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "old_data", "is_primary_key": false, "attnum": 3, "cltype": "jsonb", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "new_data", "is_primary_key": false, "attnum": 4, "cltype": "jsonb", "geometry": null, "srid": null, "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "audit_log", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c85"}], "include": [], "cid": "c84"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd": {"id": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "type": "table", "selected": false, "x": 780, "y": 180, "ports": [{"id": "609f1c74-0c8f-473f-8a87-a9a1aebf9133", "type": "one<PERSON><PERSON>", "x": 780.9999972815818, "y": 300.65626140945795, "name": "coll-port-2-left", "alignment": "left", "parentNode": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "links": []}, {"id": "13fa9d5e-0fda-4fbe-8d4c-1e8cf6e0f695", "type": "one<PERSON><PERSON>", "x": 954.0000123026175, "y": 300.65626140945795, "name": "coll-port-2-right", "alignment": "right", "parentNode": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "links": ["f53936d5-5192-4171-b715-6d4e4382c636"]}, {"id": "a6794021-e2c1-49c4-845a-85942a638e65", "type": "one<PERSON><PERSON>", "x": 780.9999972815818, "y": 326.7031162710245, "name": "coll-port-3-left", "alignment": "left", "parentNode": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "links": []}, {"id": "c9eb9586-c79f-4889-b11d-16f72440ab66", "type": "one<PERSON><PERSON>", "x": 954.0000123026175, "y": 326.7031162710245, "name": "coll-port-3-right", "alignment": "right", "parentNode": "c2b4d60a-e12b-4123-8c0b-8c8a5d1094dd", "links": ["e8a9134c-d382-498f-ae75-179fc8a9bf4d"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["609f1c74-0c8f-473f-8a87-a9a1aebf9133", "13fa9d5e-0fda-4fbe-8d4c-1e8cf6e0f695", "a6794021-e2c1-49c4-845a-85942a638e65", "c9eb9586-c79f-4889-b11d-16f72440ab66"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 12, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "user_id", "atttypid": 23, "attlen": null, "attnum": 2, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}, {"name": "appObject_id", "atttypid": 23, "attlen": null, "attnum": 3, "attndims": 0, "atttypmod": -1, "attacl": [], "attnotnull": true, "attoptions": null, "attfdwoptions": null, "attstattarget": -1, "attstorage": "p", "attidentity": "", "defval": null, "typname": "integer", "displaytypname": "integer", "cltype": "smallint", "inheritedfrom": null, "inheritedid": null, "elemoid": 23, "typnspname": "pg_catalog", "defaultstorage": "p", "description": null, "indkey": "1", "isdup": false, "collspcname": "", "is_fk": true, "seclabels": null, "is_sys_column": false, "colconstype": "n", "genexpr": null, "relname": "user_permissions", "is_view_only": false, "attcompression": null, "seqrelid": null, "seqtypid": null, "seqstart": null, "seqincrement": null, "seqmax": null, "seqmin": null, "seqcache": null, "seqcycle": null, "is_pk": false, "is_primary_key": false, "attprecision": null, "coloptions": [], "edit_types": ["bigint", "double precision", "information_schema.cardinal_number", "integer", "integer", "integer", "money", "numeric", "oid", "real", "regclass", "regcollation", "regconfig", "regdictionary", "regnamespace", "regoper", "regoperator", "regproc", "regprocedure", "regrole", "regtype", "smallint"]}], "name": "audit_user", "description": "", "relpersistence": false, "fillfactor": null, "toast_tuple_target": null, "parallel_workers": null, "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c64"}], "include": [], "cid": "c63"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "user_id", "referenced": "pk", "references": "d6a9f9ed-34da-4191-b9a3-2180523e5faf", "references_table_name": "(auth) user", "cid": "c66"}], "confupdtype": "c", "confdeltype": "c"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "appObject_id", "referenced": "pk", "references": "5f7e750f-2b39-487f-a079-114090c9b86d", "references_table_name": "(auth) appObjects", "cid": "c101"}], "confupdtype": "c", "confdeltype": "c"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "auth", "rlspolicy": false, "forcerlspolicy": false, "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}}}]}}