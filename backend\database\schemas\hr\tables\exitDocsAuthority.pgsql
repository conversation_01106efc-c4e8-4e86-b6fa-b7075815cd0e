CREATE TABLE "hr"."exitDocsAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."exitDocsAuthority_id_seq"'::regclass) ,
 "exitDocs_id" BIGINT NOT NULL REFERENCES "hr"."exitDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT ,
 "priority" SMALLINT ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "exitDocsAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."exitDocsAuthority" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."exitDocsAuthority_id_seq" OWNED BY "hr"."exitDocsAuthority"."pk";
