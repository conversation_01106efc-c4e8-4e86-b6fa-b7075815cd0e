CREATE TABLE "hr"."exitDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."exitDocs_id_seq"'::regclass) ,
 "emp_id" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
        CHECK (enterprise."opType_applicable"("opType_id", 'exit')),
 "minutesCount" SMALLINT ,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "editowner_id" SMALLINT NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE ,
 "fromDate" TIME WITHOUT TIME ZONE ,
 "toDate" TIME WITHOUT TIME ZONE ,
 "state" hr.docstate DEFAULT 'Pending'::hr.docstate ,
 "note" TEXT ,
 CONSTRAINT "exitDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."exitDocs" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."exitDocs_id_seq" OWNED BY "hr"."exitDocs"."pk";
