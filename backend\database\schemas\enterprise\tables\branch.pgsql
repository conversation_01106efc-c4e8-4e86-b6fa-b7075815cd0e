CREATE TABLE "enterprise"."branch" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."branch_id_seq"'::regclass) ,
 "enterprise_id" SMALLINT NOT NULL REFERENCES "enterprise"."enterprise" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "base_currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "tax_currency_id" SMALLINT NOT NULL REFERENCES "enterprise"."currency" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "emp_vacation_days_per_year" SMALLINT ,
 "emp_vacation_years" SMALLINT ,
 "level_" SMALLINT NOT NULL ,
 "parent_id" SMALLINT REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "prefix" CHARACTER(2) UNIQUE ,
 "name_" VARCHAR(255) NOT NULL UNIQUE ,
 "active_" BOOLEAN NOT NULL DEFAULT 'True'::BOOLEAN ,
 CONSTRAINT "branch_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."branch" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."branch_id_seq" OWNED BY "enterprise"."branch"."pk";
INSERT INTO "enterprise"."branch" ("pk", "enterprise_id", "base_currency_id", "tax_currency_id", "emp_vacation_days_per_year", "emp_vacation_years", "prefix", "name_", "level_") 
VALUES (0, 0, 0, 0, 20, 3, 'B0', 'Main Branch', 0);
