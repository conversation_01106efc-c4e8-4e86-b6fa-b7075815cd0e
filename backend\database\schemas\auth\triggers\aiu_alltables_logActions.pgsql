CREATE OR REPLACE FUNCTION auth.aiu_alltables_logActions()
RETURNS TRIGGER AS $$
DECLARE
    v_old_data JSONB;
    v_new_data JSONB;
    v_row_id TEXT;
    v_pk_cols TEXT[]; -- Array to hold primary key column names
    v_pk_col TEXT;
    v_pk_values TEXT[]; -- Array to hold primary key values for the current row
    v_current_row_data JSONB; -- Will hold either OLD or NEW record as JSONB
    v_should_log BOOLEAN := FALSE; -- Flag to determine if logging should occur
    v_user_id INTEGER; -- To store the user_id from app_users table
BEGIN
    -- Dynamically get primary key column names for the current table
    SELECT ARRAY_AGG(a.attname ORDER BY a.attnum)
    INTO v_pk_cols
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    WHERE i.indrelid = TG_TABLE_NAME::regclass AND i.indisprimary;

    -- Determine which record (OLD/NEW) to use for extracting PK values
    IF TG_OP = 'DELETE' THEN
        v_current_row_data := to_jsonb(OLD);
        v_old_data := v_current_row_data;
        v_new_data := NULL;
    ELSE -- INSERT or UPDATE
        v_current_row_data := to_jsonb(NEW);
        v_new_data := v_current_row_data;
        IF TG_OP = 'UPDATE' THEN
            v_old_data := to_jsonb(OLD);
        ELSE -- INSERT
            v_old_data := NULL;
        END IF;
    END IF;

    -- Construct v_row_id by concatenating primary key values
    v_pk_values := ARRAY[]::TEXT[];
    IF v_current_row_data IS NOT NULL AND array_length(v_pk_cols, 1) > 0 THEN
        FOREACH v_pk_col IN ARRAY v_pk_cols LOOP
            v_pk_values := array_append(v_pk_values, v_current_row_data->>v_pk_col);
        END LOOP;
        v_row_id := ARRAY_TO_STRING(v_pk_values, '-');
    END IF;

    -- *** Start of new logic: Retrieve user_id and check audit_config ***
    -- First, get the user_id from app_users based on current_user (role name)
    SELECT user_id INTO v_user_id
    FROM app_users
    WHERE role_name = current_user;

    -- If a matching user_id is found, then check audit_config
    IF v_user_id IS NOT NULL THEN
        SELECT EXISTS (
            SELECT 1
            FROM audit_config
            WHERE audited_user_id = v_user_id AND audited_table = TG_TABLE_NAME
        ) INTO v_should_log;
    END IF;

    -- If v_should_log is TRUE, then insert the log entry
    IF v_should_log THEN
        INSERT INTO audit_log (
            table_name,
            action_type,
            performed_by,
            old_data,
            new_data,
            row_id,
            user_id_fk -- Populate the new user_id_fk column
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            current_user,
            v_old_data,
            v_new_data,
            v_row_id,
            v_user_id -- Use the retrieved user_id
        );
    END IF;
    -- *** End of new logic ***

    -- For row-level triggers, you must return NEW for INSERT/UPDATE or OLD for DELETE
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION auth.aiu_alltables_logActions() IS 'Trigger function to log INSERT, UPDATE, and DELETE operations on tables, dynamically determining primary keys and using app_users and audit_config for conditional logging.';