CREATE TABLE "enterprise"."accountCategory" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."accountCategory_id_seq"'::regclass) ,
 "name_" VARCHAR(100) NOT NULL ,
 "accountingMethods_id" SMALLINT NOT NULL REFERENCES "enterprise"."accountingMethods" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "accountCategory_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."accountCategory" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."accountCategory_id_seq" OWNED BY "enterprise"."accountCategory"."pk";
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (1, 'الفئة الاولى - حسابات الرسائل الدائمة', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (2, 'الفئة الثانية - حسابات الاصول الثابتة', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (3, 'الفئة الثالثة - المخزون وقيد الصنع', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (4, 'الفئة الرابعة - حسابات الذمم', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (5, 'الفئة الخامسة - الحسابات المالية', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (6, 'الفئة السادسة - حسابات الاعباء', 1);
INSERT INTO "enterprise"."accountCategory" ("pk", "name_", "accountingMethods_id") VALUES (7, 'الفئة السابعة - حسابات الايرادات', 1);
