CREATE TABLE "enterprise"."currency" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."currency_id_seq"'::regclass) ,
 "name_" VARCHAR(20) NOT NULL UNIQUE ,
 "symbol" VARCHAR(3) NOT NULL UNIQUE ,
 CONSTRAINT "currency_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."currency" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."currency_id_seq" OWNED BY "enterprise"."currency"."pk";
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (0, 'ليرة لبنانية', 'LBL');
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (1, 'دولار امريكي', 'USD');
INSERT INTO "enterprise"."currency" ("pk", "name_", "symbol") VALUES (2, 'يورو اوروبي', 'EUR');
