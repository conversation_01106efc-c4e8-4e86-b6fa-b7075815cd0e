CREATE TABLE "auth"."appObjects" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."appObjects_id_seq"'::regclass) ,
 "app_id" SMALLINT NOT NULL REFERENCES "auth"."app" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "objectType" auth.objecttype NOT NULL ,
 "name_" TEXT NOT NULL ,
 "schema_" TEXT NOT NULL ,
 "options" JSONB ,
 CONSTRAINT "appObjects_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."appObjects" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."appObjects_id_seq" OWNED BY "auth"."appObjects"."pk";
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('currency', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "name_", "symbol"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('accountingMethods', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('allowedSocialMedia', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["platform_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('legalForm', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('post', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "chefLevel", "authority", "active_", "name_", "post_role"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('department', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "code_", "level_", "active_", "parent_id", "prefix", "name_", "dep_role"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('accountCategory', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_", "accountingMethods_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('opTypes', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "name_", "prefix", "appLable", "filter"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('enterprise', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "financial_number", "commercial_register_number", "official_legalform_id", "legalform_id", "enterprise_accounting_level", "social_media", "email", "TVA_registartion_number", "phone", "fax", "website", "name_", "address", "logo"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "enterprise_id", "base_currency_id", "tax_currency_id", "emp_vacation_days_per_year", "emp_vacation_years", "level_", "parent_id", "prefix", "name_", "active_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('financialYear', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "start_", "end_", "accounting_info"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_department', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "department_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_department_post', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["pk", "branch_department_id", "post_id", "legal_capacity", "occupied"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('department_optypes', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["department_id", "opType_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_currencies', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["branch_id", "currency_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('chartOfAccountRef', 'enterprise', 'Table', 4, '{"sys_table": true, "fieldlist": ["pk", "accountCategory_id", "num_", "description"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('branch_acc', 'enterprise', 'Table', 4, '{"sys_table": false, "fieldlist": ["branch_id", "chartOfAccountRef_id", "allowRooting"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('person', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "active_", "nickname", "name_", "social_media", "gender"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('personContact', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "person_id", "name_", "post", "phone"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('personType', 'person', 'Table', 5, '{"sys_table": true, "fieldlist": ["pk", "initial_acc", "description"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('person_personTypes', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["person_id", "personType_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('financialInfo', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "financial_number", "commercial_register_number", "TVA_registration_number", "capital", "legalForm"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('address', 'person', 'Table', 5, '{"sys_table": false, "fieldlist": ["pk", "phone", "fax", "email", "website", "address"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('emp', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "social_security", "financial_number", "birth_date", "start_date", "end_date", "first_name", "father_name", "family_name", "mother_fullname"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decision', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "branch_id", "opType_id", "personType_id", "date_", "ref_", "editor"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionTransfer', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "old_department_id", "old_post_id", "new_department_id", "new_post_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionEndOfService', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "date_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionEmployment', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "department_id", "post_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionAssignment', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "old_department_id", "old_post_id", "new_department_id", "new_post_id", "old_ops"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "decision_id", "emp_id", "opType_id", "daysCount", "ownerDepartment_id", "editowner_id", "fromDate", "toDate", "state", "editDate", "note"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "vacationDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationMDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "decision_id", "ownerDepartment_id", "editowner_id", "editDate", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('vacationMAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "vacationDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('exitDocs', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "opType_id", "minutesCount", "ownerDepartment_id", "editowner_id", "editDate", "fromDate", "toDate", "state", "note"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('exitDocsAuthority', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "exitDocs_id", "authorityUser_id", "priority", "state_", "signature"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('detailAddress', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "floor_", "nationality", "state_", "city", "region", "street", "building_project", "building", "addition_info"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('decisionExtend', 'hr', 'Table', 7, '{"sys_table": false, "fieldlist": ["pk", "emp_id", "decision_id", "date_"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('type', 'machine', 'Table', 6, '{"sys_table": true, "fieldlist": ["pk", "company", "parameters"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machine', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["pk", "type_id", "branch_id", "recordsCount", "lastRead", "name_", "conf"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('attendance', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["machine_id", "att_id", "attendance_date", "created", "attOptions"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machinelogs', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["pk", "machine_id", "start_date", "end_date", "records_count", "created", "notes"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('machine_emp', 'machine', 'Table', 6, '{"sys_table": false, "fieldlist": ["machine_id", "att_id", "emp_id", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('permission', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["name_", "privilege", "binaryCode", "order_", "objectType"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('app', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "appLable", "default_", "active_", "personTypeList"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "password_", "last_login", "date_joined", "is_active", "mustChangePassword", "username", "first_name", "last_name", "email", "phone", "rolename"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "app_id", "name_", "description", "rolename"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('appObjects', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "app_id", "objectType", "name_", "schema_", "options"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_group', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["user_id", "group_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_permission', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "appObjects_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_permission', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "group_id", "appObjects_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('appObjects_allowed_permission', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["appObject_id", "permission_name_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_permission_detail', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_permission_id", "allow", "permission_name_id", "fieldlist"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_permission_detail', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "group_permission_id", "allow", "permission_name_id", "fieldlist"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_branch', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "branch_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('userToken', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_id", "revoked", "expires", "created", "token_text"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('group_personType', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["personType_id", "group_id", "allow"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('audit_user', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "user_id", "appObject_id"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('audit_log', 'auth', 'Table', 3, '{"sys_table": true, "fieldlist": ["pk", "user_id", "logged_at", "table_name", "row_id", "action_type", "old_data", "new_data"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_department', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["pk", "user_branch_id", "department_id", "allow"]}')
 RETURNING pk;
INSERT INTO "auth"."appObjects" (name_, schema_, "objectType", app_id, options)
 VALUES ('user_department_optypes', 'auth', 'Table', 3, '{"sys_table": false, "fieldlist": ["user_department_id", "optype_id", "allow"]}')
 RETURNING pk;
