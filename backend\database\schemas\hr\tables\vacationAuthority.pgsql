CREATE TABLE "hr"."vacationAuthority" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationAuthority_id_seq"'::regclass) ,
 "vacationDocs_id" BIGINT NOT NULL REFERENCES "hr"."vacationDocs" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "authorityUser_id" SMALLINT NOT NULL ,
 "priority" SMALLINT NOT NULL ,
 "state_" hr.signstate NOT NULL DEFAULT 'Pending'::hr.signstate ,
 "signature" JSONB ,
 CONSTRAINT "vacationAuthority_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationAuthority" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."vacationAuthority_id_seq" OWNED BY "hr"."vacationAuthority"."pk";
