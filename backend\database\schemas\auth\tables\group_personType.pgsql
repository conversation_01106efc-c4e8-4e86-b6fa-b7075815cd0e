CREATE TABLE "auth"."group_personType" (
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "group_id" SMALLINT NOT NULL REFERENCES "auth"."group" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::BOOLEAN ,
 CONSTRAINT "group_personType_pkey" PRIMARY KEY ("personType_id", "group_id")
);
ALTER TABLE "auth"."group_personType" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
