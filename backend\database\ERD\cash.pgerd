{"version": "81300", "data": {"id": "dcec7db7-227c-47ed-b67c-d6c24eb702d4", "offsetX": 795.5472415004126, "offsetY": 527.7785171827691, "zoom": 105.00000000000014, "gridSize": 15, "layers": [{"id": "e667d835-40ff-4e24-8fca-2d5a85807dbf", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {"d27711fe-5a62-47d7-ae1b-bc4faf41ad57": {"id": "d27711fe-5a62-47d7-ae1b-bc4faf41ad57", "locked": true, "type": "one<PERSON><PERSON>", "source": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "sourcePort": "45fea341-1220-4b08-a0c7-54b8d8185ce1", "target": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "targetPort": "128f9104-659b-4876-864e-ec5cc06bb3d3", "points": [{"id": "7430bcff-6b3d-412b-b4a8-3c8af71cc881", "type": "point", "x": -35.999979707066345, "y": -400.3906497350772}, {"id": "517c2215-6014-4259-81c7-a886dbced821", "type": "point", "x": -35.999979707066345, "y": -374.34373985784504}, {"id": "1b2723b5-d529-4f33-bdf9-7c3e6e1eec3c", "type": "point", "x": 30.99998809856529, "y": -374.34373985784504}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "local_column_attnum": 1, "referenced_table_uid": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "referenced_column_attnum": 0}}, "80176687-aa21-4f2e-ac39-f610715b7bed": {"id": "80176687-aa21-4f2e-ac39-f610715b7bed", "locked": true, "type": "one<PERSON><PERSON>", "source": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "sourcePort": "d5e2dcbe-2814-4389-b609-4f423f4b3692", "target": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "targetPort": "6041117d-94e5-468b-8138-96a773ef00b7", "points": [{"id": "db5d7e84-9fca-4868-95b6-05da17f191d2", "type": "point", "x": -35.999988649946445, "y": 124.60936189066621}, {"id": "2107836f-0896-4253-91f0-d1911983523e", "type": "point", "x": -35.999988649946445, "y": -215.6250059550294}, {"id": "bc594298-baef-433b-a4d1-a41b24f8d2ff", "type": "point", "x": -35.999979707066345, "y": -215.6250059550294}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "local_column_attnum": 14, "referenced_table_uid": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "referenced_column_attnum": 0}}, "579d0a45-fc43-49d9-98a4-88cfeea5a078": {"id": "579d0a45-fc43-49d9-98a4-88cfeea5a078", "locked": true, "type": "one<PERSON><PERSON>", "source": "********-b80f-4b2c-9cf7-001d0a268bde", "sourcePort": "df0ad2d9-4c5b-4ce5-8ad4-52561bbb631a", "target": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "targetPort": "515b13c8-fd36-4e9c-8303-57b1d9485d98", "points": [{"id": "6addb654-0c07-4148-8fa6-4816aeb13e84", "type": "point", "x": 15.**************, "y": -85.**************}, {"id": "b52d7ffa-a1ae-4715-b9a3-98016eef22c8", "type": "point", "x": 15.**************, "y": -303.2656497350774}, {"id": "55de58ce-52b6-47be-a95f-6aa5c5c22d58", "type": "point", "x": -35.999979707066345, "y": -303.2656497350774}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "local_column_attnum": 15, "referenced_table_uid": "********-b80f-4b2c-9cf7-001d0a268bde", "referenced_column_attnum": 0}}, "f8f81384-92f1-4d30-952e-3322655290b0": {"id": "f8f81384-92f1-4d30-952e-3322655290b0", "locked": true, "type": "one<PERSON><PERSON>", "source": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "sourcePort": "d5e2dcbe-2814-4389-b609-4f423f4b3692", "target": "********-b80f-4b2c-9cf7-001d0a268bde", "targetPort": "66e8d2ff-92de-447f-816a-80be58da5cbc", "points": [{"id": "10a891e7-bf7c-466f-b2c5-9e08e1b593ae", "type": "point", "x": -35.999988649946445, "y": 124.60936189066621}, {"id": "3e9ef0c2-9206-4395-8b0d-271c445563ae", "type": "point", "x": -35.999988649946445, "y": -33.29689973507764}, {"id": "07aaebcb-7454-4ead-a55a-91d9379be8f2", "type": "point", "x": 15.**************, "y": -33.29689973507764}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "********-b80f-4b2c-9cf7-001d0a268bde", "local_column_attnum": 2, "referenced_table_uid": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "referenced_column_attnum": 0}}, "4a6cde23-04d2-40d0-9b72-30d13ed9a4c0": {"id": "4a6cde23-04d2-40d0-9b72-30d13ed9a4c0", "locked": true, "type": "one<PERSON><PERSON>", "selected": false, "source": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "sourcePort": "d5e2dcbe-2814-4389-b609-4f423f4b3692", "target": "6242a3b7-84f9-4da2-ac59-4b92c174ce68", "targetPort": "054b66dc-fa06-4b50-a68d-47ecee147ff3", "points": [{"id": "b45bed17-607b-4ac0-991f-94ec90c7521c", "type": "point", "x": -35.999988649946445, "y": 124.60936189066621}, {"id": "e21b41b4-6ef3-4814-bfcc-cb61a4d99bc3", "type": "point", "x": -35.999988649946445, "y": 175.15623689066615}, {"id": "1c72abc6-2237-4c40-b8c8-d23c7add9de2", "type": "point", "x": 225.9999299698448, "y": 175.15623689066615}, {"id": "fed4bf9f-f446-4bc4-8824-9ab9431ca128", "type": "point", "x": 225.9999299698448, "y": 175.15623689066615}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "6242a3b7-84f9-4da2-ac59-4b92c174ce68", "local_column_attnum": 1, "referenced_table_uid": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "referenced_column_attnum": 0}}, "5cb5bcbf-0232-46ef-95f7-c7319b7f77c5": {"id": "5cb5bcbf-0232-46ef-95f7-c7319b7f77c5", "locked": true, "type": "one<PERSON><PERSON>", "source": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "sourcePort": "62d25a4c-d425-4088-bf4d-efcbd52037bc", "target": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "targetPort": "d3f271e0-6f33-4477-8e34-4b2ff3f57cc2", "points": [{"id": "2fc51946-579e-47ce-bc09-76d2cf881d9e", "type": "point", "x": -268.9999537727141, "y": 124.60936189066621}, {"id": "25808e1e-3bb5-444c-b6db-ee6319296825", "type": "point", "x": -268.9999537727141, "y": 111.20313514215479}, {"id": "beb917b8-6ada-4884-900a-7a6229b2c997", "type": "point", "x": -291.0000467786674, "y": 111.20313514215479}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "local_column_attnum": 2, "referenced_table_uid": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "referenced_column_attnum": 0}}, "27f340ab-2752-4f87-b296-2c521c308dde": {"id": "27f340ab-2752-4f87-b296-2c521c308dde", "locked": true, "type": "one<PERSON><PERSON>", "source": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "sourcePort": "62d25a4c-d425-4088-bf4d-efcbd52037bc", "target": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "targetPort": "55257a0c-474e-473b-8550-c3aa790cbd5c", "points": [{"id": "a01a8ff1-f3be-40e7-ac8a-9169aca3977d", "type": "point", "x": -268.9999537727141, "y": 124.60936189066621}, {"id": "6480fde1-44f1-4603-abb3-5e1ded25e7e6", "type": "point", "x": -268.9999537727141, "y": 146.73437351641064}, {"id": "7dde573b-3a12-432e-94ec-319d14c779db", "type": "point", "x": -291.0000467786674, "y": 146.73437351641064}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "local_column_attnum": 3, "referenced_table_uid": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "referenced_column_attnum": 0}}}}, {"id": "fc7bd49e-826d-4d35-adca-0a984391b175", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"0ceb32a4-02d5-412b-a2f7-a0a941104387": {"id": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "type": "table", "selected": false, "x": -240, "y": -495, "ports": [{"id": "fcac4dd9-e1c3-4bbb-b3d2-c997613ac373", "type": "one<PERSON><PERSON>", "x": -238.99995377271406, "y": -400.39060904497296, "name": "coll-port-0-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "50b2230a-58b3-470f-a541-566313d595e4", "type": "one<PERSON><PERSON>", "x": -238.99995377271406, "y": -277.21875729646126, "name": "coll-port-11-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "e41875b9-09a9-4b33-a35b-ac59244161fe", "type": "one<PERSON><PERSON>", "x": -65.99998864994645, "y": -277.21875729646126, "name": "coll-port-11-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "7eab3db2-aeb7-452c-84ca-af0927b92ab9", "type": "one<PERSON><PERSON>", "x": -238.99995377271406, "y": -251.1718881093333, "name": "coll-port-8-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "f62ae1a5-27c5-4d0e-9a89-5df2b004ef99", "type": "one<PERSON><PERSON>", "x": -65.99998864994645, "y": -251.1718881093333, "name": "coll-port-8-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "bd0c0669-c920-4172-a7de-caf8cc9221ea", "type": "one<PERSON><PERSON>", "x": -238.99995377271406, "y": -364.843734044973, "name": "coll-port-5-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "36a58756-c146-42c7-96b0-069637cf3a8f", "type": "one<PERSON><PERSON>", "x": -65.99998864994645, "y": -364.843734044973, "name": "coll-port-5-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "81967ea8-444a-483f-870b-30405accc8c9", "type": "one<PERSON><PERSON>", "x": -239.00002860769143, "y": -303.2656201520954, "name": "coll-port-4-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "018c65bb-5554-4445-af05-c4b8d843231b", "type": "one<PERSON><PERSON>", "x": -238.99999848711417, "y": -329.31251534505327, "name": "coll-port-13-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "dd3bc854-e948-4def-af77-31c17cb4bd22", "type": "one<PERSON><PERSON>", "x": -65.99997970706634, "y": -329.31251534505327, "name": "coll-port-13-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "45fea341-1220-4b08-a0c7-54b8d8185ce1", "type": "one<PERSON><PERSON>", "x": -65.99997970706634, "y": -400.3906497350772, "name": "coll-port-0-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": ["d27711fe-5a62-47d7-ae1b-bc4faf41ad57"]}, {"id": "c24aa869-cb6f-4234-b606-649027c8a398", "type": "one<PERSON><PERSON>", "x": -238.99999848711417, "y": -215.6250059550294, "name": "coll-port-14-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "6041117d-94e5-468b-8138-96a773ef00b7", "type": "one<PERSON><PERSON>", "x": -65.99997970706634, "y": -215.6250059550294, "name": "coll-port-14-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": ["80176687-aa21-4f2e-ac39-f610715b7bed"]}, {"id": "d58dc97d-2de3-4941-929a-2ec96a1d247e", "type": "one<PERSON><PERSON>", "x": -238.99999848711417, "y": -303.2656497350774, "name": "coll-port-15-left", "alignment": "left", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": []}, {"id": "515b13c8-fd36-4e9c-8303-57b1d9485d98", "type": "one<PERSON><PERSON>", "x": -65.99997970706634, "y": -303.2656497350774, "name": "coll-port-15-right", "alignment": "right", "parentNode": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "links": ["579d0a45-fc43-49d9-98a4-88cfeea5a078"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["fcac4dd9-e1c3-4bbb-b3d2-c997613ac373", "50b2230a-58b3-470f-a541-566313d595e4", "e41875b9-09a9-4b33-a35b-ac59244161fe", "7eab3db2-aeb7-452c-84ca-af0927b92ab9", "f62ae1a5-27c5-4d0e-9a89-5df2b004ef99", "bd0c0669-c920-4172-a7de-caf8cc9221ea", "36a58756-c146-42c7-96b0-069637cf3a8f", "81967ea8-444a-483f-870b-30405accc8c9", "018c65bb-5554-4445-af05-c4b8d843231b", "dd3bc854-e948-4def-af77-31c17cb4bd22", "45fea341-1220-4b08-a0c7-54b8d8185ce1", "c24aa869-cb6f-4234-b606-649027c8a398", "6041117d-94e5-468b-8138-96a773ef00b7", "d58dc97d-2de3-4941-929a-2ec96a1d247e", "515b13c8-fd36-4e9c-8303-57b1d9485d98"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "journalVoucherDetail_id", "is_primary_key": false, "attnum": 5, "cltype": "bigint", "attlen": null, "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "account_id", "is_primary_key": false, "attnum": 13, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "order_id", "is_primary_key": false, "attnum": 15, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "opType_id", "is_primary_key": false, "attnum": 11, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "owner_id", "is_primary_key": false, "attnum": 8, "cltype": "smallint", "attlen": null, "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "physicalCashBox_id", "is_primary_key": false, "attnum": 14, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "date_", "is_primary_key": false, "attnum": 1, "cltype": "date", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "dueDate", "is_primary_key": false, "attnum": 10, "cltype": "date", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "description", "is_primary_key": false, "attnum": 2, "cltype": "character varying", "attlen": "100", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "manualRef", "is_primary_key": false, "attnum": 7, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "foreignRef", "is_primary_key": false, "attnum": 12, "cltype": "character varying", "attlen": "30", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "option", "is_primary_key": false, "attnum": 9, "cltype": "jsonb", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "cashOpsHead", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c129"}], "include": [], "cid": "c128"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "physicalCashBox_id", "referenced": "pk", "references": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "references_table_name": "physicalCashBox", "cid": "c131"}], "confupdtype": "c", "confdeltype": "a"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "order_id", "referenced": "pk", "references": "********-b80f-4b2c-9cf7-001d0a268bde", "references_table_name": "cashOrder", "cid": "c168"}], "confupdtype": "c", "confdeltype": "a"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "cash"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3": {"id": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "type": "table", "selected": false, "x": 60, "y": -495, "ports": [{"id": "128f9104-659b-4876-864e-ec5cc06bb3d3", "type": "one<PERSON><PERSON>", "x": 60.99998809856529, "y": -374.34373985784504, "name": "coll-port-1-left", "alignment": "left", "parentNode": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "links": ["d27711fe-5a62-47d7-ae1b-bc4faf41ad57"]}, {"id": "adec109f-f585-42c0-935f-5c9314ee64d6", "type": "one<PERSON><PERSON>", "x": 233.99995322133293, "y": -374.34373985784504, "name": "coll-port-1-right", "alignment": "right", "parentNode": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "links": []}, {"id": "16876944-75db-49c4-9f28-8ad5994dc33c", "type": "one<PERSON><PERSON>", "x": 60.9999880985653, "y": -277.21875729646126, "name": "coll-port-3-left", "alignment": "left", "parentNode": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "links": []}, {"id": "8908d871-9bb3-43b3-b68d-e7eaac8a6bca", "type": "one<PERSON><PERSON>", "x": 60.9999880985653, "y": -303.26562648358913, "name": "coll-port-2-left", "alignment": "left", "parentNode": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "links": []}, {"id": "74b9edab-9833-4424-b4c0-a0d16b679eaa", "type": "one<PERSON><PERSON>", "x": 233.99995322133293, "y": -303.26562648358913, "name": "coll-port-2-right", "alignment": "right", "parentNode": "5a2110b0-2f4d-4852-ba70-1798ac6e4ff3", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["128f9104-659b-4876-864e-ec5cc06bb3d3", "adec109f-f585-42c0-935f-5c9314ee64d6", "16876944-75db-49c4-9f28-8ad5994dc33c", "8908d871-9bb3-43b3-b68d-e7eaac8a6bca", "74b9edab-9833-4424-b4c0-a0d16b679eaa"], "otherInfo": {"data": {"name": "cashOpsDetail", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c86"}], "include": [], "cid": "c85"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "cashOpsHead_id", "referenced": "pk", "references": "0ceb32a4-02d5-412b-a2f7-a0a941104387", "references_table_name": "cashOpsHead", "cid": "c88"}], "confupdtype": "c", "confdeltype": "a", "cid": "c87"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "cashOpsHead_id", "is_primary_key": false, "attnum": 1, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "journalVoucherDetail_id", "is_primary_key": false, "attnum": 7, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "account_id", "is_primary_key": false, "attnum": 2, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "amount", "is_primary_key": false, "attnum": 6, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "description", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "200", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "cash"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "bcf769f2-44b8-4a22-8117-d3f8defc5e4b": {"id": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "type": "table", "selected": false, "x": -240, "y": 30, "ports": [{"id": "62d25a4c-d425-4088-bf4d-efcbd52037bc", "type": "one<PERSON><PERSON>", "x": -238.99995377271406, "y": 124.60936189066621, "name": "coll-port-0-left", "alignment": "left", "parentNode": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "links": ["5cb5bcbf-0232-46ef-95f7-c7319b7f77c5", "27f340ab-2752-4f87-b296-2c521c308dde"]}, {"id": "d5e2dcbe-2814-4389-b609-4f423f4b3692", "type": "one<PERSON><PERSON>", "x": -65.99998864994645, "y": 124.60936189066621, "name": "coll-port-0-right", "alignment": "right", "parentNode": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "links": ["80176687-aa21-4f2e-ac39-f610715b7bed", "f8f81384-92f1-4d30-952e-3322655290b0", "4a6cde23-04d2-40d0-9b72-30d13ed9a4c0"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["62d25a4c-d425-4088-bf4d-efcbd52037bc", "d5e2dcbe-2814-4389-b609-4f423f4b3692"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 2, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "prefix", "is_primary_key": false, "attnum": 3, "cltype": "character", "attlen": "2", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "defval": "B1", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "name_", "is_primary_key": false, "attnum": 1, "cltype": "character varying", "attlen": "50", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "physicalCashBox", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c307"}], "include": [], "cid": "c306"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "cash", "unique_constraint": [{"columns": [{"column": "name_", "cid": "c312"}], "include": [], "cid": "c311"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "6242a3b7-84f9-4da2-ac59-4b92c174ce68": {"id": "6242a3b7-84f9-4da2-ac59-4b92c174ce68", "type": "table", "selected": false, "x": 255, "y": 45, "ports": [{"id": "054b66dc-fa06-4b50-a68d-47ecee147ff3", "type": "one<PERSON><PERSON>", "x": 255.9999299698448, "y": 175.15623689066615, "name": "coll-port-1-left", "alignment": "left", "parentNode": "6242a3b7-84f9-4da2-ac59-4b92c174ce68", "links": ["4a6cde23-04d2-40d0-9b72-30d13ed9a4c0"]}, {"id": "5fb59b55-732a-48c2-990d-48b1dceababf", "type": "one<PERSON><PERSON>", "x": 428.99995322133265, "y": 175.15623689066615, "name": "coll-port-1-right", "alignment": "right", "parentNode": "6242a3b7-84f9-4da2-ac59-4b92c174ce68", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["054b66dc-fa06-4b50-a68d-47ecee147ff3", "5fb59b55-732a-48c2-990d-48b1dceababf"], "otherInfo": {"data": {"columns": [{"name": "user_id", "is_primary_key": true, "attnum": 0, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "physicalCashBox_id", "is_primary_key": true, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "opType_id", "is_primary_key": true, "attnum": 2, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "accountCategory_id", "is_primary_key": true, "attnum": 5, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "startDate", "is_primary_key": false, "attnum": 3, "cltype": "date", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "endDate", "is_primary_key": false, "attnum": 4, "cltype": "date", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "physicalCashBox_permissions", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "user_id", "cid": "c258"}, {"column": "cashBox_id", "cid": "c259"}, {"column": "opType_id", "cid": "c260"}, {"column": "accountCategory_id", "cid": "c261"}], "include": [], "cid": "c257"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "physicalCashBox_id", "referenced": "pk", "references": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "references_table_name": "physicalCashBox", "cid": "c291"}], "confupdtype": "c", "confdeltype": "a"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "cash"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "********-b80f-4b2c-9cf7-001d0a268bde": {"id": "********-b80f-4b2c-9cf7-001d0a268bde", "type": "table", "selected": false, "x": 45, "y": -180, "ports": [{"id": "df0ad2d9-4c5b-4ce5-8ad4-52561bbb631a", "type": "one<PERSON><PERSON>", "x": 45.**************, "y": -85.**************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "********-b80f-4b2c-9cf7-001d0a268bde", "links": ["579d0a45-fc43-49d9-98a4-88cfeea5a078"]}, {"id": "a418e751-0226-4096-ae47-644e5f3fd427", "type": "one<PERSON><PERSON>", "x": 218.99995322133293, "y": -85.**************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "********-b80f-4b2c-9cf7-001d0a268bde", "links": []}, {"id": "66e8d2ff-92de-447f-816a-80be58da5cbc", "type": "one<PERSON><PERSON>", "x": 45.**************, "y": -33.29689973507764, "name": "coll-port-2-left", "alignment": "left", "parentNode": "********-b80f-4b2c-9cf7-001d0a268bde", "links": ["f8f81384-92f1-4d30-952e-3322655290b0"]}, {"id": "2a6edd18-581d-4182-814c-d6d5f98c8933", "type": "one<PERSON><PERSON>", "x": 218.99995322133293, "y": -33.29689973507764, "name": "coll-port-2-right", "alignment": "right", "parentNode": "********-b80f-4b2c-9cf7-001d0a268bde", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["df0ad2d9-4c5b-4ce5-8ad4-52561bbb631a", "a418e751-0226-4096-ae47-644e5f3fd427", "66e8d2ff-92de-447f-816a-80be58da5cbc", "2a6edd18-581d-4182-814c-d6d5f98c8933"], "otherInfo": {"data": {"name": "cashOrder", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c110"}], "include": [], "cid": "c109"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "physicalbox_id", "referenced": "pk", "references": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "references_table_name": "physicalCashBox", "cid": "c112"}], "confupdtype": "c", "confdeltype": "a", "cid": "c111"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "account_id", "is_primary_key": false, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "physicalbox_id", "is_primary_key": false, "attnum": 2, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "opType_id", "is_primary_key": false, "attnum": 4, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "owner_id", "is_primary_key": false, "attnum": 9, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "amount", "is_primary_key": false, "attnum": 5, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "complete", "is_primary_key": false, "attnum": 8, "cltype": "boolean", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "order_date", "is_primary_key": false, "attnum": 3, "cltype": "date", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "description", "is_primary_key": false, "attnum": 7, "cltype": "text", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "cash"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "34a2076d-3a9d-4412-a507-4a8789c9ff64": {"id": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "type": "table", "selected": false, "x": -495, "y": -45, "ports": [{"id": "2233ff39-6dc1-436e-91b7-0d717dabeffb", "type": "one<PERSON><PERSON>", "x": -494.00007003015526, "y": 111.20313514215479, "name": "coll-port-2-left", "alignment": "left", "parentNode": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "links": []}, {"id": "d3f271e0-6f33-4477-8e34-4b2ff3f57cc2", "type": "one<PERSON><PERSON>", "x": -321.0000467786674, "y": 111.20313514215479, "name": "coll-port-2-right", "alignment": "right", "parentNode": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "links": ["5cb5bcbf-0232-46ef-95f7-c7319b7f77c5"]}, {"id": "43e3a995-448c-4ee9-8f86-2d9abe610b4a", "type": "one<PERSON><PERSON>", "x": -494.00007003015526, "y": 146.73437351641064, "name": "coll-port-3-left", "alignment": "left", "parentNode": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "links": []}, {"id": "55257a0c-474e-473b-8550-c3aa790cbd5c", "type": "one<PERSON><PERSON>", "x": -321.0000467786674, "y": 146.73437351641064, "name": "coll-port-3-right", "alignment": "right", "parentNode": "34a2076d-3a9d-4412-a507-4a8789c9ff64", "links": ["27f340ab-2752-4f87-b296-2c521c308dde"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["2233ff39-6dc1-436e-91b7-0d717dabeffb", "d3f271e0-6f33-4477-8e34-4b2ff3f57cc2", "43e3a995-448c-4ee9-8f86-2d9abe610b4a", "55257a0c-474e-473b-8550-c3aa790cbd5c"], "otherInfo": {"data": {"name": "physicalCashTransfer", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c185"}], "include": [], "cid": "c184"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "from_physicalbox_id", "referenced": "pk", "references": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "references_table_name": "(cash) physicalCashBox", "cid": "c187"}], "confupdtype": "c", "confdeltype": "a"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "to_physicalbox_id", "referenced": "pk", "references": "bcf769f2-44b8-4a22-8117-d3f8defc5e4b", "references_table_name": "(cash) physicalCashBox", "cid": "c218"}], "confupdtype": "c", "confdeltype": "a"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "account_id", "is_primary_key": false, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "from_physicalbox_id", "is_primary_key": false, "attnum": 2, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "to_physicalbox_id", "is_primary_key": false, "attnum": 3, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "amount", "is_primary_key": false, "attnum": 4, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "date_", "is_primary_key": false, "attnum": 5, "cltype": "date", "attlen": null, "attprecision": null, "defval": "now()", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "description", "is_primary_key": false, "attnum": 6, "cltype": "text", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "cash"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}}}]}}