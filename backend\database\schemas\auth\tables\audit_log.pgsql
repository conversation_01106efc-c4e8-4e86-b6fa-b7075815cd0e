CREATE TABLE "auth"."audit_log" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."audit_log_id_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "logged_at" TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW() ,
 "table_name" TEXT NOT NULL ,
 "row_id" TEXT NOT NULL ,
 "action_type" auth.action NOT NULL ,
 "old_data" JSONB NOT NULL ,
 "new_data" JSONB NOT NULL ,
 CONSTRAINT "audit_log_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."audit_log" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."audit_log_id_seq" OWNED BY "auth"."audit_log"."pk";
