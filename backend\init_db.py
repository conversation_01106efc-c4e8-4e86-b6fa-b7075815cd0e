from config import config
from database.roles import create_roles, drop_roles
from database.db_operations import create_database, drop_database

def run_functions_with_recovery_and_logging(function_pairs):
            
    for i, (func, _) in enumerate(function_pairs):
        if not func():
            config.error(f"{func.__name__} failed, running recovery functions")
            # Run recovery functions in reverse order up to the current function
            for j in range(i, -1, -1):
                if function_pairs[j][1]():
                    config.warning(f"Recovery for {function_pairs[j][1].__name__} succeeded")
                else:
                    config.error(f"Recovery for {function_pairs[j][1].__name__} failed")
            return
        else:
            config.info(f"{func.__name__} succeeded ----------------------------------------")
            
    config.info("All functions returned True")
    print("All Done.")
    
    
def init_db():
    config.info("Starting database creation process...")
    try:       
        # List of function pairs (function, recovery function)
        function_pairs = [ 
                 (create_roles, drop_roles),
                 (create_database, drop_database)
            ]

        # Execute
        run_functions_with_recovery_and_logging(function_pairs)
           
    except Exception as e:
        config.error(f"Fatal error during creation: {e}")
        raise
    
if __name__ == "__main__":
    init_db()   