CREATE OR <PERSON><PERSON>LACE FUNCTION auth.bu_group_permission_detail_lockedFields()
RETURNS TRIGGER AS $$
BEGIN

    -- preventing update of group_permission_id , permission_name
    IF NEW.group_permission_id IS DISTINCT FROM OLD.group_permission_id OR NEW.permission_name_id IS DISTINCT FROM OLD.permission_name_id THEN
        RAISE EXCEPTION 'Updates to columns group_permission_id and permission_name are not allowed';
    END IF;
   
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

alter function auth.bu_group_permission_detail_lockedFields() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_bu_group_permission_detail_lockedFields
BEFORE UPDATE of group_permission_id, permission_name_id ON auth.group_permission_detail
FOR EACH ROW
EXECUTE FUNCTION auth.bu_group_permission_detail_lockedFields();
