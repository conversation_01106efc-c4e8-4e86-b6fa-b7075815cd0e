import json
import os

from psycopg2 import sql
from psycopg2.errors import Error
from config import config

from .schemas import check_schema_exists, get_schema_list_from_dir, SCHEMA_DIR

def check_sequence_exists(cur, schema_name: str, sequence_name: str) -> bool:
    """
    Check if a sequence exists in the database.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema to check
        sequence_name: Name of the sequence to check
    Returns:
        bool: True if sequence exists, False otherwise
    """
    check_sql = sql.SQL("SELECT EXISTS (SELECT 1 FROM pg_sequences WHERE schemaname = %s AND sequencename = %s)")
    cur.execute(check_sql, (schema_name, sequence_name))
    return cur.fetchone()[0]

def create_sequence(conf, schema_name, cur) -> bool:
    """
    Creates a sequence within an existing transaction.

    Args:
        conf (dict): Dictionary containing sequence configuration (seq_name, seq_type).
        schema_name (str): Name of the schema.
        cur: Database cursor.

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        seq_name = conf["seq_name"]
        seq_type = conf.get("seq_type", "BIGINT") # Default to BIGINT if not specified
        config.info(f"Creating sequence {schema_name}.{seq_name}")

        if check_sequence_exists(cur, schema_name, seq_name):
            config.warning(f"Sequence {schema_name}.{seq_name} already exists. Skipping creation.")
            # Decide if you want to restart existing sequences or just skip
            # For now, let's skip restart if it exists to avoid resetting unintentionally
            return True
        else:
            # Create Sequence statement
            run_sql = sql.SQL("CREATE SEQUENCE {}.{} AS {}").format(
                sql.Identifier(schema_name),
                sql.Identifier(seq_name),
                sql.SQL(seq_type) # Assuming seq_type is a valid SQL type like BIGINT, INTEGER etc.
            )
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Sequence {schema_name}.{seq_name} created.")

            # Alter Sequence Restart statement
            run_sql = sql.SQL("ALTER SEQUENCE {}.{} RESTART WITH 1").format(
                sql.Identifier(schema_name),
                sql.Identifier(seq_name)
            )
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Sequence {schema_name}.{seq_name} set to restart with 1.")

            # Alter Sequence Owner statement (Good practice to set owner)
            run_sql = sql.SQL("ALTER SEQUENCE {}.{} OWNER TO {}").format(
                sql.Identifier(schema_name),
                sql.Identifier(seq_name),
                sql.Identifier(config.POSTGRES_DEFAULT_ADMIN)
            )
            config.debug(run_sql.as_string(cur))
            config.cumulativeSQL(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Ownership of sequence {schema_name}.{seq_name} granted to {config.POSTGRES_DEFAULT_ADMIN}.")

        return True
    except KeyError as e:
        config.error(f"Missing 'seq_name' key in configuration for sequence in schema {schema_name}: {conf}")
        return False
    
    except Error as e:
        config.error(f"Database error creating sequence {schema_name}.{seq_name}: {e}")
        return False
    
    except Exception as e:
        config.error(f"Unexpected error creating sequence {schema_name}.{seq_name}: {e}")
        return False

def create_sequences() -> bool:
    """
    Create sequences based on sequences.json files found in schema directories.
    """
    config.info("Attempting to create sequences...")
    try:
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
                for schema_name in get_schema_list_from_dir():
                    if not check_schema_exists(cur, schema_name):
                        config.error(f"Schema {schema_name} does not exist")
                        return False
                    
                    seq_file_path = os.path.join(SCHEMA_DIR, schema_name, "sequences.json")

                    if os.path.exists(seq_file_path):
                        config.info(f"Processing sequences for schema: {schema_name}")
                        try:
                            with open(seq_file_path, 'r', encoding='utf-8') as f:
                                sequences_conf = json.load(f)
                                if not isinstance(sequences_conf, list):
                                    config.error(f"Invalid format in {seq_file_path}: Expected a JSON list.")
                                    conn.rollback()
                                    return False

                                for seq_conf in sequences_conf:
                                    # Basic validation of seq_conf
                                    if not isinstance(seq_conf, dict) or "seq_name" not in seq_conf:
                                        config.error(f"Invalid sequence entry in {seq_file_path}: {seq_conf}")
                                        conn.rollback()
                                        return False

                                    if not create_sequence(seq_conf, schema_name, cur):
                                        # Error already logged by create_sequence
                                        conn.rollback()
                                        return False # Stop on first error

                        except json.JSONDecodeError as e:
                            config.error(f"Error decoding JSON from {seq_file_path}: {e}")
                            conn.rollback()
                            return False
                        
                        except Exception as e:
                            config.error(f"Error reading or processing {seq_file_path}: {e}")
                            conn.rollback()
                            return False


            # Commit only if all sequences in all files were processed without error
            conn.commit()
            return True

    except Error as e:
        config.error(f"Database connection or transaction error during sequence creation: {e}")
        return False
    
    except Exception as e:
        config.error(f"Unexpected error during sequence creation process: {e}")
        return False

def drop_sequences() -> bool:
    """
    Drops all custom sequences in the database, excluding system schemas.
    Uses CASCADE to handle dependencies.

    Returns:
        bool: True if all found custom sequences were dropped successfully, False otherwise.
    """
    config.info("Attempting to drop all custom sequences...")
    sequences_to_drop = []
    try:
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
                # Find all sequences not in system schemas using pg_sequences view
                find_sequences_sql = sql.SQL("""
                    SELECT schemaname, sequencename
                    FROM pg_sequences
                    WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
                      AND schemaname NOT LIKE 'pg_temp_%'
                      AND schemaname NOT LIKE 'pg_toast_temp_%';
                """)

                cur.execute(find_sequences_sql)
                sequences_to_drop = cur.fetchall()

                if not sequences_to_drop:
                    config.info("No custom sequences found to drop.")
                    return True

                config.info(f"Found {len(sequences_to_drop)} custom sequences to drop.")

                for schema_name, seq_name in sequences_to_drop:
                    try:
                        # Drop Sequence statement
                        run_sql = sql.SQL("DROP SEQUENCE IF EXISTS {}.{} CASCADE").format(
                            sql.Identifier(schema_name),
                            sql.Identifier(seq_name)
                        )
                        config.debug(run_sql.as_string(cur))
                        config.cumulativeSQL(run_sql.as_string(cur))
                        cur.execute(run_sql)
                        config.info(f"Dropped sequence {schema_name}.{seq_name}")
                    except Error as e:
                        config.error(f"Error dropping sequence {schema_name}.{seq_name}: {e}")
                        conn.rollback() # Rollback on first error
                        return False
                    except Exception as e:
                        config.error(f"Unexpected error dropping sequence {schema_name}.{seq_name}: {e}")
                        conn.rollback() # Rollback on first error
                        return False

            # If loop completes without error, commit the transaction
            conn.commit()
            config.info("Successfully dropped all found custom sequences.")
            return True

    except Error as e:
        config.error(f"Database error during sequence dropping process: {e}")
        return False
    except Exception as e:
        config.error(f"Unexpected error during sequence dropping process: {e}")
        return False
