import os
import re
import networkx as nx
from networkx.exception import NetworkXUnfeasible
from psycopg2 import sql, Error

from config import config, postgrestConf, get_config_attribute
from .schemas import check_schema_exists, get_schema_list_from_dir, SCHEMA_DIR

created_tables : list[str] =[]
direct_tables : list[str] = []
graph = nx.DiGraph()

def extract_table_references_from_pgsql_file(file_path: str) -> list[str] | None:
    """
    Read a PostgreSQL file containing CREATE TABLE commands and extract table references.

    Args:
        file_path: Path to the .pgsql file to process

    Returns:
        list[str]: List of table references in format "schema.table"

    Example:
        For REFERENCES "auth"."user" returns "auth.user"
    """
    table_references = []

    try:
        if not os.path.isfile(file_path):
            config.error(f"File not found: {file_path}")
            return []

        config.debug(f"Processing file: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # Extract table references using regex
        # Pattern matches: REFERENCES "schema"."table" or REFERENCES schema.table or PARTITION OF schema.table or PARTITION OF "schema"."table"
        reference_pattern = r'(?:REFERENCES|PARTITION OF)\s+["\']?([^"\'.\s]+)["\']?\.["\']?([^"\'.\s\(]+)["\']?'
        matches = re.findall(reference_pattern, sql_content, re.IGNORECASE)

        for schema_ref, table_ref in matches:
            # Clean up the schema and table names (remove quotes if any)
            clean_schema = schema_ref.strip('"\'')
            clean_table = table_ref.strip('"\'')
            reference = f"{clean_schema}.{clean_table}"

            if reference not in table_references:
                table_references.append(reference)

        if not table_references:
            config.debug(f"No table references found in {file_path}.")
            return None
        else:
            config.debug(f"Extracted {len(table_references)} unique table references from {file_path}")
            return table_references

    except (IOError, UnicodeDecodeError) as e:
        config.error(f"Error reading file {file_path}: {e}")
        return []
    
    except Exception as e:
        config.error(f"Error extracting table references from {file_path}: {e}")
        return []

def check_table_exists(cur, schema_name: str, table_name: str) -> bool:
    """
    Check if a table exists in the database
    Args:
        cur: Database cursor
        table_name: Name of the table to check
        schema_name: Name of the schema the table belongs to (default: "public")
    Returns:
        True if the table exists, False otherwise
    """
    run_sql = sql.SQL("SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = {} AND table_schema = {} )").format(
                    sql.Literal(table_name),
                    sql.Literal(schema_name)
                )
    cur.execute(run_sql)
    return cur.fetchone()[0]

def create_table_from_file(cur, file_path: str) -> bool:
    """
    Executes a SQL script to create a table.
    
    Args:
        cur: Database cursor
        file_path: Path to the SQL file containing the CREATE TABLE statement
    Returns:
        bool: True if table was created successfully or already exists, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # Extract table name from file name (assuming file name is table_name.sql)
        table_name = os.path.splitext(os.path.basename(file_path))[0]
        schema_name = os.path.basename(os.path.dirname(os.path.dirname(file_path)))

        if check_table_exists(cur, schema_name, table_name):
            config.warning(f"Table {schema_name}.{table_name} already exists. Skipping creation.")
            return True

        # Replace placeholders with actual values
        for key in postgrestConf.IDENTIFIERS_LIST:
            sql_script = sql_script.replace(f"#{key}#", get_config_attribute(key))

        config.debug(f"Executing SQL for {schema_name}.{table_name}:\n{sql_script}")
        config.cumulativeSQL(sql_script)        
        cur.execute(sql.SQL(sql_script))
        config.info(f"Successfully created/verified table {schema_name}.{table_name}")
        created_tables.append(f"{schema_name}.{table_name}") # Add the table to the list of created tables
        return True
    
    except (Error, IOError) as e:
        config.error(f"Error creating table from file {file_path}: {e}")
        return False

def prepare_graph_for_schema(cur, schema_name: str) -> bool:
    """
    Helper function to process all .pgsql files in a given schema's 'tables' directory.

    Args:
        cur: Database cursor
        schema_name: The name of the schema to process.
    """

    if not check_schema_exists(cur, schema_name):
        config.warning(f"Schema {schema_name} does not exist.")
        return False

    config.info(f"---Preparing gragh for schema: {schema_name} ---")
    tables_dir = os.path.join(SCHEMA_DIR, schema_name, 'tables')
    pgsql_files = []

    if not os.path.isdir(tables_dir):
        config.warning(f"No 'tables' directory found for schema {schema_name}. Skipping.")
        return True
    
    config.debug(f"Tables directory found for schema {schema_name}.")
    # Find all .pgsql files in the tables directory
    pgsql_files = [f for f in os.listdir(tables_dir) if f.endswith('.pgsql')]

    if not pgsql_files:
        config.warning(f"No .pgsql files found in {tables_dir}. Skipping.")
        return True
    
    for file_name in pgsql_files:
        file_path = os.path.join(tables_dir, file_name)
        dependencies =  extract_table_references_from_pgsql_file(file_path)

        if dependencies:
            config.debug(f"Found {len(dependencies)} dependencies in {file_path}: {dependencies}")
            graph.add_edges_from([(file_path, get_table_path(dep.split('.')[0], dep.split('.')[1])) for dep in dependencies if file_path != get_table_path(dep.split('.')[0], dep.split('.')[1])])
            
        else:
            config.debug(f"No dependencies found in {file_path}. Adding self-reference.")
            direct_tables.append(get_table_path(schema_name, file_name[:-6]))  # Remove .pgsql extension            
    
    return True 

def get_table_path(schema_name: str, table_name: str) -> str:
    return os.path.join(SCHEMA_DIR, schema_name, 'tables', table_name + '.pgsql')

def create_tables() -> bool:
    """
    Main function to set up all tables from tables directory structure.
    
    Returns:
        bool: True if all tables were processed successfully, False otherwise
    """
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            schema_list = get_schema_list_from_dir()
            config.info("--- Starting tables creation process ---")

            # preparing nodes in the graph
            config.debug("Preparing graph with schema dependencies...")
            for schema_name in schema_list:
                if not prepare_graph_for_schema(cur, schema_name):
                    return False       

            config.debug("processing direct tables...")
            for file_path in direct_tables:
                config.debug(f"Processing direct table file: {file_path}")
                if not create_table_from_file(cur, file_path):
                    return False
                
            config.debug("processing graph for table creation...")
            for file_path in list(nx.topological_sort(graph))[::-1]:
                config.debug(f"Processing file: {file_path}")
                if not create_table_from_file(cur, file_path):
                   return False
                
            config.info("--- Tables creation process finished. ---")
            return True
        
        except NetworkXUnfeasible as e:
            config.error(f"processing graph for table creation failed: {e}")
            config.debug(nx.find_cycle(graph))
            return False
        
        except Exception as e:
            config.error(f"Error creating tables: {e}")
            return False

def drop_table(cur, tbl: str) -> bool:
    """
    Drop a table.
    
    Args:
        cur: Database cursor
        table_name: Name of the table to drop
        
    Returns:
        bool: True if table was dropped successfully
    """
    try:
        schema_name = tbl.split('.')[0]
        table_name = tbl.split('.')[1]
        run_sql = sql.SQL("DROP TABLE IF EXISTS {}.{} CASCADE").format(sql.Identifier(schema_name) ,sql.Identifier(table_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Dropped table {schema_name}.{table_name}")
        return True
    
    except Exception as e:
        config.error(f"Error dropping table {tbl}: {e}")
        return False
       
def drop_tables() -> bool:
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            config.info("--- Starting tables drop process ---")

            # Process regular tables first
            for tbl in reversed(created_tables):
                if not drop_table(cur, tbl):
                    return False       

            config.info("--- Tables drop process finished. ---")
            return True
        
        except Exception as e:
            config.error(f"Error dropping tables: {e}")
            return False