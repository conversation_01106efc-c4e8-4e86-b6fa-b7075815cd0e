import os
import networkx as nx
from psycopg2 import sql, Error

from config import config, postgrestConf, get_config_attribute
from .schemas import check_schema_exists, get_schema_list_from_dir, SCHEMA_DIR

created_tables : list[str] =[]
sql_files : list[str] =[]
graph = nx.DiGraph()

def check_table_exists(cur, schema_name: str, table_name: str) -> bool:
    """
    Check if a table exists in the database
    Args:
        cur: Database cursor
        table_name: Name of the table to check
        schema_name: Name of the schema the table belongs to (default: "public")
    Returns:
        True if the table exists, False otherwise
    """
    run_sql = sql.SQL("SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = {} AND table_schema = {} )").format(
                    sql.Literal(table_name),
                    sql.Literal(schema_name)
                )
    cur.execute(run_sql)
    return cur.fetchone()[0]

def create_table_from_file(cur, schema_name: str, file_path: str) -> bool:
    """
    Executes a SQL script to create a table.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema where the table belongs
        file_path: Path to the SQL file containing the CREATE TABLE statement
    Returns:
        bool: True if table was created successfully or already exists, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # Extract table name from file name (assuming file name is table_name.sql)
        table_name = os.path.splitext(os.path.basename(file_path))[0]

        if check_table_exists(cur, schema_name, table_name):
            config.warning(f"Table {schema_name}.{table_name} already exists. Skipping creation.")
            return True

        # Replace placeholders with actual values
        for key in postgrestConf.IDENTIFIERS_LIST:
            sql_script = sql_script.replace(f"#{key}#", get_config_attribute(key))

        config.debug(f"Executing SQL for {schema_name}.{table_name}:\n{sql_script}")
        config.cumulativeSQL(sql_script)        
        cur.execute(sql.SQL(sql_script))
        config.info(f"Successfully created/verified table {schema_name}.{table_name}")
        created_tables.append(f"{schema_name}.{table_name}") # Add the table to the list of created tables
        return True
    
    except (Error, IOError) as e:
        config.error(f"Error creating table from file {file_path}: {e}")
        return False

def process_tables_for_schema(cur, schema_name: str) -> bool:
    """
    Helper function to process all .pgsql files in a given schema's 'tables' directory.

    Args:
        cur: Database cursor
        schema_name: The name of the schema to process.
    """

    if not check_schema_exists(cur, schema_name):
        config.warning(f"Schema {schema_name} does not exist.")
        return False

    config.info(f"--- Processing tables for schema: {schema_name} ---")
    tables_dir = os.path.join(SCHEMA_DIR, schema_name, 'tables')

    if not os.path.isdir(tables_dir):
        config.warning(f"No 'tables' directory found for schema {schema_name}. Skipping.")
    else:
        ...

    return True # gracefully return true if no tables dir or no pgsql files found

def create_tables() -> bool:
    """
    Main function to set up all tables from tables directory structure.
    
    Returns:
        bool: True if all tables were processed successfully, False otherwise
    """
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            schema_list = get_schema_list_from_dir()
            config.info("--- Starting tables creation process ---")

            for schema_name in schema_list:
                if not process_tables_for_schema(cur, schema_name):
                    return False       

            for sql in sql_files:
                schema_name = sql.split('.')[0]
                tables_dir = os.path.join(SCHEMA_DIR, schema_name, 'tables')
                file_path = os.path.join(tables_dir, sql)
                if not create_table_from_file(cur, schema_name, file_path):
                    return False
                
            config.info("--- Tables creation process finished. ---")
            return True
        
        except Exception as e:
            config.error(f"Error creating tables: {e}")
            return False


def drop_table(cur, tbl: str) -> bool:
    """
    Drop a table.
    
    Args:
        cur: Database cursor
        table_name: Name of the table to drop
        
    Returns:
        bool: True if table was dropped successfully
    """
    try:
        schema_name = tbl.split('.')[0]
        table_name = tbl.split('.')[1]
        run_sql = sql.SQL("DROP TABLE IF EXISTS {}.{} CASCADE").format(sql.Identifier(schema_name) ,sql.Identifier(table_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Dropped table {schema_name}.{table_name}")
        return True
    
    except Exception as e:
        config.error(f"Error dropping table {tbl}: {e}")
        return False
       
def drop_tables() -> bool:
    with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
        cur = conn.cursor()
        try:
            config.info("--- Starting tables drop process ---")

            # Process regular tables first
            for tbl in created_tables[::-1]:
                if not drop_table(cur, tbl):
                    return False       

            config.info("--- Tables drop process finished. ---")
            return True
        
        except Exception as e:
            config.error(f"Error dropping tables: {e}")
            return False