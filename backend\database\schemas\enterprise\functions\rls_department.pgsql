CREATE OR REPLACE FUNCTION enterprise.rls_department(pkp SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
 user_group_ids SMALLINT[];
BEGIN

 -- Get all group_ids associated with the user
    SELECT ARRAY_AGG(group_id)
    INTO user_group_ids
    FROM auth.user_group
    WHERE user_id = current_setting('myapp.user_id')::SMALLINT;
  
   -- Check if the user belongs to any groups
    IF user_group_ids IS NULL THEN
        RETURN FALSE;
    END IF;

    IF EXISTS ( SELECT 1 FROM auth.group_department gd
    JOIN auth.group_branch gb ON gd.group_branch_id = gb.pk
    WHERE gd.department_id = pkp AND COALESCE(gd.allow, false) = true AND gb.group_id = ANY(user_group_ids)) THEN
        RETURN TRUE;
    END IF;

    RETURN FALSE;

END;
$$;

alter function enterprise.rls_department(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#;