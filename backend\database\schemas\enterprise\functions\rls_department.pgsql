CREATE OR REPLACE FUNCTION enterprise.rls_department(p_pk SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY INVOKER AS $$
BEGIN

    if current_setting('db.single_department')::BOOLEAN = True then
        raise EXCEPTION 'Single department is enabled this function should not be called';
    end if; 
    
    IF EXISTS ( SELECT 1 
                FROM auth.user_branch ub 
                     inner join auth.user_department ud on ub.pk = ud.user_branch_id  
                WHERE ub.user_id = current_setting('myapp.user_id')::SMALLINT and ud.department_id = p_pk
                ) THEN        
        RETURN TRUE;        
    END IF;

    RETURN FALSE;

END;
$$;

alter function enterprise.rls_department(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#;