CREATE TABLE "hr"."vacationMDocs" (
 "pk" BIGINT NOT NULL DEFAULT nextval('hr."vacationMDocs_id_seq"'::regclass) ,
 "decision_id" BIGINT NOT NULL REFERENCES "hr"."decision" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "ownerDepartment_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE
    CHECK (enterprise."opType_applicable"("opType_id", 'm_vacation')),
 "editowner_id" SMALLINT NOT NULL ,
 "editDate" TIMESTAMP WITHOUT TIME ZONE NOT NULL ,
 "options" JSONB NOT NULL ,
 CONSTRAINT "vacationMDocs_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."vacationMDocs" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "hr"."vacationMDocs_id_seq" OWNED BY "hr"."vacationMDocs"."pk";
