CREATE TABLE "auth"."group" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."group_id_seq"'::regclass) ,
 "app_id" SMALLINT NOT NULL REFERENCES "auth"."app" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "name_" VARCHAR(150) NOT NULL UNIQUE ,
 "description" TEXT UNIQUE ,
 "rolename" NAME NOT NULL UNIQUE ,
 CONSTRAINT "group_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."group_id_seq" OWNED BY "auth"."group"."pk";
