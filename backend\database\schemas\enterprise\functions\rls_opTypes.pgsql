CREATE OR REPLACE FUNCTION enterprise.rls_opTypes(p_pk SMALLINT)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY INVOKER AS $$
BEGIN
    
    IF EXISTS ( SELECT 1 
                FROM auth.user_branch ub 
                     inner join auth.user_department ud on ub.pk = ud.user_branch_id
                     inner join auth.user_department_optypes udo on ud.pk = udo.user_department_id  
                WHERE ub.user_id = current_setting('myapp.user_id')::SMALLINT and udo.optype_id = p_pk
                ) THEN        
        RETURN TRUE;        
    END IF;

    RETURN FALSE;

END;
$$;

alter function enterprise.rls_opTypes(pk SMALLINT) owner to #POSTGRES_DEFAULT_ADMIN#;