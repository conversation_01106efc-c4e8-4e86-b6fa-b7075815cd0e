CREATE OR R<PERSON>LACE FUNCTION auth.aiu_user_branch_insertDepartmentsForBranch()
RETURNS TRIGGER AS $$
BEGIN

   IF TG_OP = 'UPDATE' AND OLD.branch_id != NEW.branch_id THEN
     -- Delete from the "group_department" table
     DELETE FROM auth.user_department
     WHERE user_branch_id = OLD.pk;
   END IF;
   
   IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.branch_id != NEW.branch_id) THEN

    IF current_setting('db.single_department') = 'true' THEN
      insert into auth.user_department (user_branch_id, department_id) values (new.pk, 0);
    ELSE
     -- Insert into the "group_department" table
     INSERT INTO auth.user_department (user_branch_id, department_id)
      SELECT DISTINCT NEW.pk, department_id
      FROM enterprise.v_branch_department_optypes;
    END IF;

   END IF;

  RETURN NEW;
  
END;
$$ LANGUAGE plpgsql;

alter function auth.aiu_user_branch_insertDepartmentsForBranch() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_aiu_user_branch_insertDepartmentsForBranch
AFTER INSERT OR UPDATE OF branch_id ON auth.user_branch
FOR EACH ROW
EXECUTE FUNCTION auth.aiu_user_branch_insertDepartmentsForBranch();