CREATE OR <PERSON><PERSON>LACE FUNCTION machine.create_monthy_partition(target_date TIMESTAMP WITHOUT TIME ZONE) RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- Generate partition name based on the date
    partition_name := 'attendance_' || TO_CHAR(target_date, 'YYYY_MM');
    start_date := date_trunc('month', target_date);
    end_date := (start_date + INTERVAL '1 month') - INTERVAL '1 day';

    -- Construct and execute the CREATE TABLE statement
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS machine.%I PARTITION OF machine.attendance
        FOR VALUES FROM (%L) TO (%L);', 'machine', partition_name, start_date, end_date);

    -- Create an index on emp_id for the new partition
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_emp_id ON machine.%I(emp_id);', partition_name,  partition_name);

END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION machine.create_monthy_partition(TIMESTAMP WITHOUT TIME ZONE) owner to #POSTGRES_DEFAULT_ADMIN#;