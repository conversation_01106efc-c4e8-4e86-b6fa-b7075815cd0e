CREATE TABLE "hr"."detailAddress" (
 "pk" BIGINT NOT NULL REFERENCES "hr"."emp" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "floor_" SMALLINT ,
 "nationality" person.nationality ,
 "state_" VARCHAR(50) ,
 "city" VARCHAR(50) ,
 "region" VARCHAR(50) ,
 "street" VARCHAR(50) ,
 "building_project" VARCHAR(50) ,
 "building" VARCHAR(50) ,
 "addition_info" VARCHAR(100) ,
 CONSTRAINT "detailAddress_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "hr"."detailAddress" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
