import pathlib
import subprocess
import sys

def main():
    """
    Finds all .pgsql files under backend/database/schemas/*/tables and runs
    the format_whitespace.py script on each of them.
    """
    try:
        # --- Path Setup ---
        # Get the directory of the current script (e.g., /.../AAS2/backend/scripts)
        script_dir = pathlib.Path(__file__).parent.resolve()
        # Get the root of the backend project
        backend_root = script_dir.parent

        # Define the absolute path to the formatter script we want to run
        formatter_script_path = script_dir / "format_whitespace.py"
        if not formatter_script_path.is_file():
            print(f"Error: The formatter script was not found at '{formatter_script_path}'", file=sys.stderr)
            sys.exit(1)

        # Define the directory to search for SQL files
        search_dir = backend_root / "database" / "schemas"

        # --- File Discovery ---
        print(f"Searching for .pgsql files in: {search_dir}/**/tables/*.pgsql")
        # Use a glob pattern to find all matching files recursively
        sql_files = list(search_dir.glob("*/tables/*.pgsql"))

        if not sql_files:
            print("No .pgsql files found to format.")
            return

        # --- Execution ---
        print(f"Found {len(sql_files)} files. Applying formatting in-place...")
        for file_path in sql_files:
            print(f"  -> Processing: {file_path.relative_to(backend_root)}")
            # Build the command to execute. Using sys.executable ensures that the
            # same Python interpreter is used for the subprocess.
            command = [
                sys.executable,
                str(formatter_script_path),
                "--in-place",
                str(file_path)
            ]
            # Run the command, raising an error if it fails.
            subprocess.run(command, check=True, capture_output=True)

        print("\nFormatting complete for all files.")

    except subprocess.CalledProcessError as e:
        print(f"\nError: A script call failed.", file=sys.stderr)
        print(f"Command: {' '.join(e.cmd)}", file=sys.stderr)
        print(f"Output:\n{e.stderr.decode()}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()