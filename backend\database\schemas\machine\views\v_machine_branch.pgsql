CREATE OR REPLACE VIEW machine.v_machine_branch
    WITH (
        security_barrier = true, 
        security_invoker = true
        )
    AS
    -- the rls must take care of user specific records
SELECT br.name_, m.pk as machine_id, m.name_ as machine_name
FROM enterprise.branch br 
    inner join machine.machine m on br.pk = m.branch_id
;

ALTER TABLE machine.v_machine_branch owner to #POSTGRES_DEFAULT_ADMIN#;