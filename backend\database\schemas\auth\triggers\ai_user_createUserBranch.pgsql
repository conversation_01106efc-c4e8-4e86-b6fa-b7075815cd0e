create or replace function auth.ai_user_createUserBranch() returns trigger as $$
begin

  IF current_setting('db.single_branch') = 'true' THEN
    IF NOT EXISTS (
        SELECT 1 FROM auth.user_branch WHERE user_id = new.pk AND branch_id = 0
    ) THEN
      insert into auth.user_branch (user_id, branch_id) values (new.pk, 0);

    END IF;
  ELSE
    -- multi branch need thinking
    -- fisrt thought the admin specify the branches for user manualy
    
  END IF;

  -- Return NULL (required, but ignored)
    RETURN NULL;

end;
$$ language plpgsql;

alter function auth.ai_user_createUserBranch() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_ai_user_createUserBranch
  after insert on auth.user
  for each row
  execute procedure auth.ai_user_createUserBranch();