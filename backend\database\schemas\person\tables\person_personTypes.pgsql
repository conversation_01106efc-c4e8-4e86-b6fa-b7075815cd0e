CREATE TABLE "person"."person_personTypes" (
 "person_id" SMALLINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "personType_id" SMALLINT NOT NULL REFERENCES "person"."personType" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "person_personTypes_pkey" PRIMARY KEY ("person_id", "personType_id")
);
ALTER TABLE "person"."person_personTypes" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
