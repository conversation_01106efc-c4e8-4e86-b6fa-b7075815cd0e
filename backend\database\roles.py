from config import config , postgrestConf
from psycopg2 import sql

roles = [
    {
        "name": postgrestConf.DB_AUTHENTICATOR_USER,
        "options": f"NOINHERIT NOCREATEDB NOCREATEROLE NOSUPERUSER NOREPLICATION NOBYPASSRLS LOGIN password '{postgrestConf.DB_AUTHENTICATOR_PASSWORD}'"
    },
    {
        "name": postgrestConf.DB_ANONYMOUS_USER,
        "options": "NOLOGIN NOSUPERUSER NOINHERIT NOCREATEDB NOCREATEROLE NOREPLICATION NOBYPASSRLS"
    }
]

def create_roles() -> bool:
    try:
        with config.get_postgres_connection() as conn:
            with conn.cursor() as cur:
                for role in roles:
                    if not create_role(cur, role['name'], role['options']):
                        conn.rollback()
                        return False
                else:
                    conn.commit()
                    return True
                
    except Exception as e:
        config.error(f"Error creating roles: {str(e)}")
        return False
    
def drop_roles() -> bool:
    try:
        with config.get_postgres_connection() as conn:
            with conn.cursor() as cur:
                for role in roles:
                    if not drop_role(cur, role['name']):
                        conn.rollback()
                        return False
                else:
                    conn.commit()
                    return True
                
    except Exception as e:
        config.error(f"Error droping roles: {str(e)}")
        return False
        
def role_exists(cur, role_name: str) -> bool:
    """Check if a role already exists in the database."""
    cur.execute("SELECT 1 FROM pg_roles WHERE rolname = %s", (role_name,))
    return cur.fetchone() is not None

def create_role(cur, role_name: str, options: str = '') -> bool:
    """
    Create a new role if it doesn't exist.
    Returns:
        bool: True if role was created, False if it already exists
    """
    try:
                               
        if role_exists(cur, role_name):
            config.info(f"Role {role_name} already exists")
            return True
        
        # Create roles
        run_sql = sql.SQL("CREATE ROLE {} WITH {}").format(sql.Identifier(role_name), sql.SQL(options))
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Created role: {role_name}")
        
        # Grant role to authenticator to allow the authenticator to switch to the role
        if role_name != postgrestConf.DB_AUTHENTICATOR_USER:
            run_sql = sql.SQL("GRANT {} TO {}").format(sql.Identifier(role_name), sql.Identifier(postgrestConf.DB_AUTHENTICATOR_USER))
            config.debug(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Granted {role_name} to {postgrestConf.DB_AUTHENTICATOR_USER}")
            
        return True
    
    except Exception as e:
        config.error(f"Error creating role {role_name}: {str(e)}")
        return False

def drop_role(cur, role_name: str) -> bool:
    """
    Drop a role if it exists.
    Returns:
        bool: True if role was dropped, False if it didn't exist
    """
    try:
        if not role_exists(cur, role_name):
            config.info(f"Role {role_name} doesn't exist")
            return True      
        # Revoke all privileges from role
        # Revoke all permissions on all objects from the role 
        # object not existed, so we don't need to worry about it.
        run_sql = sql.SQL("REASSIGN OWNED BY {} TO postgres;").format(sql.Identifier(role_name))
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql) 
        
        run_sql = sql.SQL("DROP OWNED BY {};").format(sql.Identifier(role_name))   
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)
         
        run_sql = sql.SQL("DROP ROLE {};").format(sql.Identifier(role_name))
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)   
        
        config.info(f"Dropped role: {role_name}")                                                                    
        return True                                                                                   
    
    except Exception as e:
        config.error(f"Error dropping role {role_name}: {str(e)}")
        return False
  