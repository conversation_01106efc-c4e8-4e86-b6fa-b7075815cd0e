-- This trigger is used to maintain the group_user_membership table.
CREATE OR REPLACE FUNCTION auth.aiu_user_group_grantRevokeUserGroups()
RETURNS TRIGGER AS $$
DECLARE
  v_group_role NAME;
  v_user_role NAME;
BEGIN
    IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
    
        SELECT rolename INTO v_group_role FROM auth.group WHERE pk = OLD.group_id;
        SELECT rolename INTO v_user_role FROM auth.user WHERE pk = OLD.user_id;
        
        Execute format('REVOKE %s FROM %s', v_group_role, v_user_role);
    END IF;

    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        SELECT rolename INTO v_group_role FROM auth.group WHERE pk = NEW.group_id;
        SELECT rolename INTO v_user_role FROM auth.user WHERE pk = NEW.user_id;
        
        Execute format('GRANT %s TO %s', v_group_role, v_user_role);
    END IF;
END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION auth.aiu_user_group_grantRevokeUserGroups() owner to #POSTGRES_DEFAULT_ADMIN#;

CREATE TRIGGER trg_aiu_user_group_grantRevokeUserGroups
after INSERT OR UPDATE OR DELETE ON auth.user_group
FOR EACH ROW
EXECUTE PROCEDURE auth.aiu_user_group_grantRevokeUserGroups();
