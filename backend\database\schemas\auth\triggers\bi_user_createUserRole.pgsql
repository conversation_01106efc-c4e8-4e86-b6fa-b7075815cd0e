--*
-- text_injection
--*
create or replace function auth.bi_user_createUserRole() returns trigger as $$
declare
    role_name name;
begin

  -- generate a role name based on username
  role_name := current_setting('db.user_prefix') || quote_ident(new.username);

  -- create the role
  execute 'create role '|| quote_ident(role_name);
  execute 'grant ' || quote_ident(role_name) || ' to {DB_AUTHENTICATOR_USER}';

  new.password_ := {DB_SCHEMA_EXTENSION}.crypt(new.password_, {DB_SCHEMA_EXTENSION}.gen_salt('bf'));
  new.rolename := role_name;

  return new;

end;
$$ language plpgsql security definer;

alter function auth.bi_user_createUserRole() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_bi_user_createUserRole
  before insert on auth.user
  for each row
  execute procedure auth.bi_user_createUserRole();