{"version": "81400", "data": {"id": "dcec7db7-227c-47ed-b67c-d6c24eb702d4", "offsetX": -1055.814825823494, "offsetY": -698.0460183263706, "zoom": 78.33333333333337, "gridSize": 15, "layers": [{"id": "e667d835-40ff-4e24-8fca-2d5a85807dbf", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {}}, {"id": "fc7bd49e-826d-4d35-adca-0a984391b175", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"d2e35479-ba29-463d-98b9-483346ab0091": {"id": "d2e35479-ba29-463d-98b9-483346ab0091", "type": "table", "selected": false, "x": 2175, "y": 945, "ports": [{"id": "cfa40993-1471-43a9-b044-8adcde5f6567", "type": "one<PERSON><PERSON>", "x": 2176.0001537274647, "y": 1039.609424159994, "name": "coll-port-0-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "0f0269a6-ae6a-434e-acca-891de91edd0d", "type": "one<PERSON><PERSON>", "x": 2176.0001537274647, "y": 1274.0313303268822, "name": "coll-port-7-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "17afb56e-af3a-42e0-abb7-e7fb7f4297c5", "type": "one<PERSON><PERSON>", "x": 2349.0000706157625, "y": 1274.0313303268822, "name": "coll-port-7-right", "alignment": "right", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "3c4d669c-aebf-4859-805e-e2d6382f6e2b", "type": "one<PERSON><PERSON>", "x": 2175.9998923398425, "y": 1247.9843806816762, "name": "coll-port-9-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "d9e199c1-16be-4d25-9b81-c2e642d5dd8a", "type": "one<PERSON><PERSON>", "x": 2176.0000887505303, "y": 1326.1250392312613, "name": "coll-port-10-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "335b8d3b-5778-4e15-b702-8213e7621745", "type": "one<PERSON><PERSON>", "x": 2349.000115144111, "y": 1326.1250392312613, "name": "coll-port-10-right", "alignment": "right", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "2c5afe45-0f22-45ab-b764-8f1e86456345", "type": "one<PERSON><PERSON>", "x": 2176.0000887505303, "y": 1352.1719406248421, "name": "coll-port-12-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "2deb0420-69db-466a-8f71-7c20b4de3021", "type": "one<PERSON><PERSON>", "x": 2176.0000887505303, "y": 1221.9375326328664, "name": "coll-port-13-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "f6e1abf3-7c56-4d12-8c45-a2a13d62e574", "type": "one<PERSON><PERSON>", "x": 2176.000043176973, "y": 1345.109406392014, "name": "coll-port-14-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "76ac0cb4-89ea-4920-9d91-f476eaa197af", "type": "one<PERSON><PERSON>", "x": 2176.0000887505303, "y": 1378.2187430424947, "name": "coll-port-17-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "985134d1-636f-4d58-bac1-695860ee602d", "type": "one<PERSON><PERSON>", "x": 2349.000115144111, "y": 1378.2187430424947, "name": "coll-port-17-right", "alignment": "right", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "aedc60b2-be4a-4cc5-9260-0cef2d485eec", "type": "one<PERSON><PERSON>", "x": 15, "y": -90, "name": "coll-port-14-right", "alignment": "right", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "efa82907-2f25-4dd1-99ce-69e132b7fecf", "type": "one<PERSON><PERSON>", "x": 2176.0000887505303, "y": 1404.2656444360755, "name": "coll-port-19-left", "alignment": "left", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}, {"id": "73e9d61a-2ecc-453e-bcce-d4e67786b6d6", "type": "one<PERSON><PERSON>", "x": 2349.000115144111, "y": 1404.2656444360755, "name": "coll-port-19-right", "alignment": "right", "parentNode": "d2e35479-ba29-463d-98b9-483346ab0091", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["cfa40993-1471-43a9-b044-8adcde5f6567", "0f0269a6-ae6a-434e-acca-891de91edd0d", "17afb56e-af3a-42e0-abb7-e7fb7f4297c5", "3c4d669c-aebf-4859-805e-e2d6382f6e2b", "d9e199c1-16be-4d25-9b81-c2e642d5dd8a", "335b8d3b-5778-4e15-b702-8213e7621745", "2c5afe45-0f22-45ab-b764-8f1e86456345", "2deb0420-69db-466a-8f71-7c20b4de3021", "f6e1abf3-7c56-4d12-8c45-a2a13d62e574", "76ac0cb4-89ea-4920-9d91-f476eaa197af", "985134d1-636f-4d58-bac1-695860ee602d", "aedc60b2-be4a-4cc5-9260-0cef2d485eec", "efa82907-2f25-4dd1-99ce-69e132b7fecf", "73e9d61a-2ecc-453e-bcce-d4e67786b6d6"], "otherInfo": {"data": {"name": "posHead", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c1175"}], "include": [], "cid": "c1174"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totCost", "is_primary_key": false, "attnum": 5, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "VATRate", "is_primary_key": false, "attnum": 18, "cltype": "numeric", "attlen": "5", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "2", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totVAT", "is_primary_key": false, "attnum": 8, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totAmount", "is_primary_key": false, "attnum": 6, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "discountPer", "is_primary_key": false, "attnum": 21, "cltype": "numeric", "attlen": "5", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "2", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totDiscount", "is_primary_key": false, "attnum": 20, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "VATCurrencyRate", "is_primary_key": false, "attnum": 15, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totAmountTTC", "is_primary_key": false, "attnum": 16, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 7, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "owner_id", "is_primary_key": false, "attnum": 10, "cltype": "integer", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "wareHouse_id", "is_primary_key": false, "attnum": 17, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "opType_id", "is_primary_key": false, "attnum": 19, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "editdate", "is_primary_key": false, "attnum": 2, "cltype": "time without time zone", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "defval": "now()", "min_val_attlen": 0, "max_val_attlen": 6}, {"name": "option", "is_primary_key": false, "attnum": 11, "cltype": "jsonb", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "pos", "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "67dd039f-55f7-4dc0-b275-2e6f2307eb95": {"id": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "type": "table", "selected": false, "x": 2430, "y": 945, "ports": [{"id": "b4b45cc3-7409-447d-9bbc-c1bd3da512d8", "type": "one<PERSON><PERSON>", "x": 2431.0001537274647, "y": 1065.6562939655125, "name": "coll-port-7-left", "alignment": "left", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "6fa5c2ae-e82d-44e8-8eb0-35963db3ef0b", "type": "one<PERSON><PERSON>", "x": 2604.0000706157625, "y": 1065.6562939655125, "name": "coll-port-7-right", "alignment": "right", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "7b062830-d6c3-4eca-b594-97f5c1fc4557", "type": "one<PERSON><PERSON>", "x": 2431.0001537274647, "y": 1091.703163771031, "name": "coll-port-4-left", "alignment": "left", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "4a9379cd-5f09-4c96-aeab-f878bb22467b", "type": "one<PERSON><PERSON>", "x": 2604.0000706157625, "y": 1091.703163771031, "name": "coll-port-4-right", "alignment": "right", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "56889d29-f80b-4470-b467-4af984207683", "type": "one<PERSON><PERSON>", "x": 2431.0001537274647, "y": 1117.7500335765496, "name": "coll-port-5-left", "alignment": "left", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "1a495280-8214-43e0-af70-1ba8596f5802", "type": "one<PERSON><PERSON>", "x": 2604.0000706157625, "y": 1117.7500335765496, "name": "coll-port-5-right", "alignment": "right", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "314654b0-6df0-4c3d-8132-b1be375371f8", "type": "one<PERSON><PERSON>", "x": 2431.0001537274647, "y": 1404.2656014372544, "name": "coll-port-17-left", "alignment": "left", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}, {"id": "70a5e0e9-88ff-4eb5-8372-01d93b522bd6", "type": "one<PERSON><PERSON>", "x": 2604.0000706157625, "y": 1404.2656014372544, "name": "coll-port-17-right", "alignment": "right", "parentNode": "67dd039f-55f7-4dc0-b275-2e6f2307eb95", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["b4b45cc3-7409-447d-9bbc-c1bd3da512d8", "6fa5c2ae-e82d-44e8-8eb0-35963db3ef0b", "7b062830-d6c3-4eca-b594-97f5c1fc4557", "4a9379cd-5f09-4c96-aeab-f878bb22467b", "56889d29-f80b-4470-b467-4af984207683", "1a495280-8214-43e0-af70-1ba8596f5802", "314654b0-6df0-4c3d-8132-b1be375371f8", "70a5e0e9-88ff-4eb5-8372-01d93b522bd6"], "otherInfo": {"data": {"name": "posDetail", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk"}], "include": []}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 6, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "posHead_id", "is_primary_key": false, "attnum": 7, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "Item_id", "is_primary_key": false, "attnum": 4, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "unite_id", "is_primary_key": false, "attnum": 5, "cltype": "numeric", "attlen": "5", "attprecision": "2", "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "count", "is_primary_key": false, "attnum": 9, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "qty", "is_primary_key": false, "attnum": 10, "cltype": "numeric", "attlen": "10", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "unitePrice", "is_primary_key": false, "attnum": 11, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totCost", "is_primary_key": false, "attnum": 12, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totAmount", "is_primary_key": false, "attnum": 15, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "discountPer", "is_primary_key": false, "attnum": 18, "cltype": "numeric", "attlen": "5", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "2", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totDiscount", "is_primary_key": false, "attnum": 19, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totVat", "is_primary_key": false, "attnum": 13, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "totAmountTTC", "is_primary_key": false, "attnum": 14, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "exipreDate", "is_primary_key": false, "attnum": 20, "cltype": "date", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "wareHouse_id", "is_primary_key": false, "attnum": 17, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "offlineName", "is_primary_key": false, "attnum": 8, "cltype": "character varying", "attlen": "48", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "option", "is_primary_key": false, "attnum": 16, "cltype": "jsonb", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "pos", "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "ce5cf888-9f58-4ed7-89a7-6882590a0083": {"id": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "type": "table", "selected": true, "x": 2175, "y": 1470, "ports": [{"id": "1f6442e5-010c-4b21-b9ae-0e4ac143f8bf", "type": "one<PERSON><PERSON>", "x": 2176.0001537274647, "y": 1590.656371882733, "name": "coll-port-7-left", "alignment": "left", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}, {"id": "05ba4c50-cd68-4700-8ad4-38728ad4702c", "type": "one<PERSON><PERSON>", "x": 2349.0000706157625, "y": 1590.656371882733, "name": "coll-port-7-right", "alignment": "right", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}, {"id": "25612d0d-800b-4aa5-815f-53c6e025dafd", "type": "one<PERSON><PERSON>", "x": 2176.0001537274647, "y": 1739.8750179931053, "name": "coll-port-10-left", "alignment": "left", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}, {"id": "eeb1d275-77e6-4d80-a3c3-7deb917d17d6", "type": "one<PERSON><PERSON>", "x": 2349.0000706157625, "y": 1739.8750179931053, "name": "coll-port-10-right", "alignment": "right", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}, {"id": "ef0b1281-975a-40f8-b2f9-dab9ea1776e8", "type": "one<PERSON><PERSON>", "x": 2176.0001537274647, "y": 1642.7501114937702, "name": "coll-port-6-left", "alignment": "left", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}, {"id": "129d4a41-cb9d-4f1a-9272-e091e47450bd", "type": "one<PERSON><PERSON>", "x": 2349.0000706157625, "y": 1642.7501114937702, "name": "coll-port-6-right", "alignment": "right", "parentNode": "ce5cf888-9f58-4ed7-89a7-6882590a0083", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["1f6442e5-010c-4b21-b9ae-0e4ac143f8bf", "05ba4c50-cd68-4700-8ad4-38728ad4702c", "25612d0d-800b-4aa5-815f-53c6e025dafd", "eeb1d275-77e6-4d80-a3c3-7deb917d17d6", "ef0b1281-975a-40f8-b2f9-dab9ea1776e8", "129d4a41-cb9d-4f1a-9272-e091e47450bd"], "otherInfo": {"data": {"name": "posReciept", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c1207"}], "include": [], "cid": "c1206"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 9, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "posHead_id", "is_primary_key": false, "attnum": 7, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "posCard_id", "is_primary_key": false, "attnum": 13, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "cashAccount", "is_primary_key": false, "attnum": 6, "cltype": "bigint", "attlen": null, "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "attcompression": "", "collspcname": null, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "currencyRate", "is_primary_key": false, "attnum": 14, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "amount", "is_primary_key": false, "attnum": 15, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "currency_id", "is_primary_key": false, "attnum": 10, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "option", "is_primary_key": false, "attnum": 11, "cltype": "jsonb", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "pos", "unique_constraint": []}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "d42aafa5-55f7-4ad3-a9c7-7f5dbb04850d": {"id": "d42aafa5-55f7-4ad3-a9c7-7f5dbb04850d", "type": "table", "selected": false, "x": 1905, "y": 945, "ports": [{"id": "eaf71375-0bae-4888-a148-a500a42aa41f", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1039.609424159994, "name": "coll-port-0-left", "alignment": "left", "parentNode": "d42aafa5-55f7-4ad3-a9c7-7f5dbb04850d", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["eaf71375-0bae-4888-a148-a500a42aa41f"], "otherInfo": {"data": {"name": "posCard", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c970"}], "include": [], "cid": "c969"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "startdate", "is_primary_key": false, "attnum": 7, "cltype": "timestamp without time zone", "attlen": null, "min_val_attlen": 0, "max_val_attlen": 6, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "name_", "is_primary_key": false, "attnum": 2, "cltype": "character varying", "attlen": "50", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "phone", "is_primary_key": false, "attnum": 4, "cltype": "character varying", "attlen": "26", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "email", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "50", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "option", "is_primary_key": false, "attnum": 6, "cltype": "jsonb", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "pos", "unique_constraint": [{"columns": [{"column": "phone", "cid": "c978"}], "include": [], "cid": "c977"}, {"columns": [{"column": "email", "cid": "c980"}], "include": [], "cid": "c979"}]}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "d312daee-4dc8-407b-a41b-cbfa44411fdf": {"id": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "type": "table", "selected": false, "x": 1905, "y": 1290, "ports": [{"id": "10638f12-08a1-4554-8f6e-cbb7d538fc6a", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1545.7500283820682, "name": "coll-port-4-left", "alignment": "left", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "e81d92bc-5f77-4416-81cc-bcffa1e24ed5", "type": "one<PERSON><PERSON>", "x": 2079.0000706157625, "y": 1545.7500283820682, "name": "coll-port-4-right", "alignment": "right", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "bb8cefef-4c46-4496-bd6f-a5e8af4e5ea6", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1616.8281429931053, "name": "coll-port-8-left", "alignment": "left", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "202b13ca-7ce9-44cd-88b6-1aac2536dcb1", "type": "one<PERSON><PERSON>", "x": 2079.0000706157625, "y": 1616.8281429931053, "name": "coll-port-8-right", "alignment": "right", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "456eaa4f-995f-4e08-80a4-732e7ce3f4f0", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1642.8750907158446, "name": "coll-port-9-left", "alignment": "left", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "06c29827-b8f8-44d1-bca5-9a145cf4aa2e", "type": "one<PERSON><PERSON>", "x": 2079.0000706157625, "y": 1642.8750907158446, "name": "coll-port-9-right", "alignment": "right", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "6162736c-9869-4b75-bb2d-df47356d72ca", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1721.0156222151797, "name": "coll-port-12-left", "alignment": "left", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "f0d296fa-3956-4b95-af2f-7e86d3bfcc03", "type": "one<PERSON><PERSON>", "x": 2079.0000706157625, "y": 1721.0156222151797, "name": "coll-port-12-right", "alignment": "right", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "3420b0f4-6894-4743-b55a-49b2ca34d7c1", "type": "one<PERSON><PERSON>", "x": 1906.000153727465, "y": 1581.296955326882, "name": "coll-port-5-left", "alignment": "left", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}, {"id": "2e13ce17-c3e8-42bf-b588-a1b0667b1e79", "type": "one<PERSON><PERSON>", "x": 2079.0000706157625, "y": 1581.296955326882, "name": "coll-port-5-right", "alignment": "right", "parentNode": "d312daee-4dc8-407b-a41b-cbfa44411fdf", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["10638f12-08a1-4554-8f6e-cbb7d538fc6a", "e81d92bc-5f77-4416-81cc-bcffa1e24ed5", "bb8cefef-4c46-4496-bd6f-a5e8af4e5ea6", "202b13ca-7ce9-44cd-88b6-1aac2536dcb1", "456eaa4f-995f-4e08-80a4-732e7ce3f4f0", "06c29827-b8f8-44d1-bca5-9a145cf4aa2e", "6162736c-9869-4b75-bb2d-df47356d72ca", "f0d296fa-3956-4b95-af2f-7e86d3bfcc03", "3420b0f4-6894-4743-b55a-49b2ca34d7c1", "2e13ce17-c3e8-42bf-b588-a1b0667b1e79"], "otherInfo": {"data": {"name": "posExchange", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk"}], "include": []}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "manualRef", "is_primary_key": false, "attnum": 7, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "editDate", "is_primary_key": false, "attnum": 1, "cltype": "time without time zone", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 0, "max_val_attlen": 6}, {"name": "description", "is_primary_key": false, "attnum": 2, "cltype": "character varying", "attlen": "100", "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_is", "is_primary_key": false, "attnum": 4, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "defval": "1", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "JournalVoucherHead_PK", "is_primary_key": false, "attnum": 5, "cltype": "bigint", "attlen": null, "min_val_attlen": 1, "max_val_attlen": 2147483647, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "owner_id", "is_primary_key": false, "attnum": 8, "cltype": "integer", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "inCurrency_id", "is_primary_key": false, "attnum": 9, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "inAmount", "is_primary_key": false, "attnum": 10, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "exchangeRate", "is_primary_key": false, "attnum": 11, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "outCurrency_PK", "is_primary_key": false, "attnum": 12, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "outAmount", "is_primary_key": false, "attnum": 13, "cltype": "money", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "pos"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}}}]}}