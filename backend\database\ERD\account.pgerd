{"version": "81300", "data": {"id": "dcec7db7-227c-47ed-b67c-d6c24eb702d4", "offsetX": -1011.7013790307461, "offsetY": -285.4952796674637, "zoom": 131.66666666666694, "gridSize": 15, "layers": [{"id": "e667d835-40ff-4e24-8fca-2d5a85807dbf", "type": "diagram-links", "isSvg": true, "transformed": true, "models": {"15324dfb-0f70-44fc-bff1-c6711291a157": {"id": "15324dfb-0f70-44fc-bff1-c6711291a157", "locked": true, "type": "one<PERSON><PERSON>", "source": "d77e12a0-cec2-4033-92d3-409b2784b19c", "sourcePort": "aa3a24e6-8419-4f2f-ab64-176c57f4be34", "target": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "targetPort": "9b4ef112-dbf1-452b-9da8-4bed6f418727", "points": [{"id": "55dce90d-da5f-4c8d-a96f-f3aa783c50e4", "type": "point", "x": 1351.0000986161097, "y": 364.*************}, {"id": "4d2c0326-1376-4b4f-8691-34c3a2fe1445", "type": "point", "x": 1425.9999960500932, "y": 364.*************}, {"id": "c511320b-f531-4b45-a429-506bc229080d", "type": "point", "x": 1329.*************, "y": 364.*************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "local_column_attnum": 0, "referenced_table_uid": "d77e12a0-cec2-4033-92d3-409b2784b19c", "referenced_column_attnum": 0}}, "4cb97bdc-52d3-4098-8a8c-64936b482b67": {"id": "4cb97bdc-52d3-4098-8a8c-64936b482b67", "locked": true, "type": "one<PERSON><PERSON>", "source": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "sourcePort": "14b1c1a0-4c4b-4345-820d-5214279beefd", "target": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "targetPort": "44a09390-9295-4d14-98c3-3f43534fb48d", "points": [{"id": "e8858a52-5b3e-4ecd-a7a9-c69a83a3a672", "type": "point", "x": 1059.************, "y": 379.*************}, {"id": "4bd4c9b0-fba9-45b9-aa11-5e8638a3e86c", "type": "point", "x": 1059.************, "y": 550.*************}, {"id": "cd4bd4ca-7708-4f9c-90b3-24e39cae2d6d", "type": "point", "x": 1096.***********, "y": 550.*************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "local_column_attnum": 1, "referenced_table_uid": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "referenced_column_attnum": 0}}, "cdfafccc-39a3-45c9-9f58-922cc7f0aacf": {"id": "cdfafccc-39a3-45c9-9f58-922cc7f0aacf", "locked": true, "type": "one<PERSON><PERSON>", "source": "d77e12a0-cec2-4033-92d3-409b2784b19c", "sourcePort": "aa3a24e6-8419-4f2f-ab64-176c57f4be34", "target": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "targetPort": "4127cdf7-5a5b-4e6d-8cc3-4189a6798924", "points": [{"id": "3e4f67a9-5576-4512-8a3b-674e9ab1a56c", "type": "point", "x": 1351.0000986161097, "y": 364.*************}, {"id": "9713fe48-4158-4f5c-8028-430cd1ee08f4", "type": "point", "x": 1351.0000986161097, "y": 585.************}, {"id": "22c7e626-d9da-40d0-817b-c21ccfd03618", "type": "point", "x": 1329.*************, "y": 585.************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "local_column_attnum": 2, "referenced_table_uid": "d77e12a0-cec2-4033-92d3-409b2784b19c", "referenced_column_attnum": 0}}, "7f543deb-72f1-4a02-abaf-819ca181c369": {"id": "7f543deb-72f1-4a02-abaf-819ca181c369", "locked": true, "type": "one<PERSON><PERSON>", "source": "d77e12a0-cec2-4033-92d3-409b2784b19c", "sourcePort": "4a725edb-64d6-4b34-ae0c-bac1828d19e8", "target": "d77e12a0-cec2-4033-92d3-409b2784b19c", "targetPort": "45803830-06f3-43b2-a34b-b9b41f12ed95", "points": [{"id": "c65da8c1-58b6-4a7a-931a-3e7274ae795b", "type": "point", "x": 1584.000046079519, "y": 364.*************}, {"id": "35e80909-1460-408b-aa70-8d218f3d0caa", "type": "point", "x": 1658.9999278343298, "y": 390.**************}, {"id": "f7d9d602-bb1d-47cf-801c-abb5421feed1", "type": "point", "x": 1584.000046079519, "y": 390.**************}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "d77e12a0-cec2-4033-92d3-409b2784b19c", "local_column_attnum": 5, "referenced_table_uid": "d77e12a0-cec2-4033-92d3-409b2784b19c", "referenced_column_attnum": 0}}, "f7cd8ef0-35ed-4c81-a7e2-5b00b4281ce5": {"id": "f7cd8ef0-35ed-4c81-a7e2-5b00b4281ce5", "locked": true, "type": "one<PERSON><PERSON>", "source": "d77e12a0-cec2-4033-92d3-409b2784b19c", "sourcePort": "4a725edb-64d6-4b34-ae0c-bac1828d19e8", "target": "e236436c-5fbd-4e56-872b-ee0f0ffea5ec", "targetPort": "221c1154-836c-441f-b85b-7e07117b17ee", "points": [{"id": "8ec9e22a-242a-40a6-8f8f-7d005609b18c", "type": "point", "x": 1584.000046079519, "y": 364.*************}, {"id": "84c1e96e-dfd3-4832-82eb-6e977ccf5877", "type": "point", "x": 1584.000046079519, "y": 319.*************3}, {"id": "fed89a81-99ca-45af-8883-6f5fd3c35040", "type": "point", "x": 1605.*************, "y": 319.*************3}], "labels": [], "width": 1, "color": "gray", "curvyness": 50, "selectedColor": "rgb(0,192,255)", "data": {"local_table_uid": "e236436c-5fbd-4e56-872b-ee0f0ffea5ec", "local_column_attnum": 0, "referenced_table_uid": "d77e12a0-cec2-4033-92d3-409b2784b19c", "referenced_column_attnum": 0}}}}, {"id": "fc7bd49e-826d-4d35-adca-0a984391b175", "type": "diagram-nodes", "isSvg": false, "transformed": true, "models": {"d77e12a0-cec2-4033-92d3-409b2784b19c": {"id": "d77e12a0-cec2-4033-92d3-409b2784b19c", "type": "table", "selected": false, "x": 1380, "y": 270, "ports": [{"id": "aa3a24e6-8419-4f2f-ab64-176c57f4be34", "type": "one<PERSON><PERSON>", "x": 1381.0000986161097, "y": 364.*************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": ["15324dfb-0f70-44fc-bff1-c6711291a157", "cdfafccc-39a3-45c9-9f58-922cc7f0aacf"]}, {"id": "0fb52157-4098-41ee-adeb-017159e35280", "type": "one<PERSON><PERSON>", "x": 1381.0000986161097, "y": 416.*************, "name": "coll-port-2-left", "alignment": "left", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": []}, {"id": "c9a0f987-d483-4fc2-a6bd-d982a7db98d1", "type": "one<PERSON><PERSON>", "x": 1554.000046079519, "y": 416.*************, "name": "coll-port-2-right", "alignment": "right", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": []}, {"id": "4a725edb-64d6-4b34-ae0c-bac1828d19e8", "type": "one<PERSON><PERSON>", "x": 1554.000046079519, "y": 364.*************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": ["7f543deb-72f1-4a02-abaf-819ca181c369", "f7cd8ef0-35ed-4c81-a7e2-5b00b4281ce5"]}, {"id": "0d1e48d9-33d2-4e63-8b3c-9284b768b71b", "type": "one<PERSON><PERSON>", "x": 1381.0000986161097, "y": 390.**************, "name": "coll-port-5-left", "alignment": "left", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": []}, {"id": "45803830-06f3-43b2-a34b-b9b41f12ed95", "type": "one<PERSON><PERSON>", "x": 1554.000046079519, "y": 390.**************, "name": "coll-port-5-right", "alignment": "right", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": ["7f543deb-72f1-4a02-abaf-819ca181c369"]}, {"id": "eb6e5181-a313-4344-a854-cb8f768a3efa", "type": "one<PERSON><PERSON>", "x": 1381.0000986161097, "y": 442.75000442257834, "name": "coll-port-7-left", "alignment": "left", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": []}, {"id": "3e10e530-9ba0-41bd-91be-1b597d3a1ea7", "type": "one<PERSON><PERSON>", "x": 1554.000046079519, "y": 442.75000442257834, "name": "coll-port-7-right", "alignment": "right", "parentNode": "d77e12a0-cec2-4033-92d3-409b2784b19c", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["aa3a24e6-8419-4f2f-ab64-176c57f4be34", "0fb52157-4098-41ee-adeb-017159e35280", "c9a0f987-d483-4fc2-a6bd-d982a7db98d1", "4a725edb-64d6-4b34-ae0c-bac1828d19e8", "0d1e48d9-33d2-4e63-8b3c-9284b768b71b", "45803830-06f3-43b2-a34b-b9b41f12ed95", "eb6e5181-a313-4344-a854-cb8f768a3efa", "3e10e530-9ba0-41bd-91be-1b597d3a1ea7"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "parent_id", "is_primary_key": false, "attnum": 5, "cltype": "bigint", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "currency_id", "is_primary_key": false, "attnum": 2, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 7, "cltype": "smallint", "attlen": null, "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "balance", "is_primary_key": false, "attnum": 11, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "initial_balance", "is_primary_key": false, "attnum": 12, "cltype": "numeric", "attlen": "18", "min_val_attlen": 1, "max_val_attlen": 1000, "attprecision": "5", "min_val_attprecision": -1000, "max_val_attprecision": 1000, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "root", "is_primary_key": false, "attnum": 6, "cltype": "boolean", "attlen": null, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "name_", "is_primary_key": false, "attnum": 1, "cltype": "character varying", "attlen": "100", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "acc_num", "is_primary_key": false, "attnum": 4, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "ref_num", "is_primary_key": false, "attnum": 8, "cltype": "character varying", "attlen": "6", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "account", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c191"}], "include": [], "cid": "c190"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "parent_id", "referenced": "pk", "references": "d77e12a0-cec2-4033-92d3-409b2784b19c", "references_table_name": "account", "cid": "c193"}], "confupdtype": "c", "confdeltype": "a", "cid": "c192"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "fa0033d3-201c-4a2d-97f7-10c8ae842769": {"id": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "type": "table", "selected": false, "x": 1125, "y": 270, "ports": [{"id": "82049f4c-307f-48dc-83ea-87b77dcfdeb6", "type": "one<PERSON><PERSON>", "x": 1126.***********, "y": 364.*************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "links": []}, {"id": "9b4ef112-dbf1-452b-9da8-4bed6f418727", "type": "one<PERSON><PERSON>", "x": 1299.*************, "y": 364.*************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "links": ["15324dfb-0f70-44fc-bff1-c6711291a157"]}, {"id": "4528dab4-fd46-449e-ab94-d02a3c0d1595", "type": "one<PERSON><PERSON>", "x": 1126.***********, "y": 390.**************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "links": []}, {"id": "d61b241b-75de-4773-87ff-50f8f2bdc539", "type": "one<PERSON><PERSON>", "x": 1299.*************, "y": 390.**************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "fa0033d3-201c-4a2d-97f7-10c8ae842769", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["82049f4c-307f-48dc-83ea-87b77dcfdeb6", "9b4ef112-dbf1-452b-9da8-4bed6f418727", "4528dab4-fd46-449e-ab94-d02a3c0d1595", "d61b241b-75de-4773-87ff-50f8f2bdc539"], "otherInfo": {"data": {"columns": [{"name": "account_id", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "person_id", "is_primary_key": true, "attnum": 1, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "person_accounts", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "account_id"}, {"column": "person_id"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "account_id", "referenced": "pk", "references": "d77e12a0-cec2-4033-92d3-409b2784b19c", "references_table_name": "account", "cid": "c108"}], "confupdtype": "c", "confdeltype": "a", "cid": "c107"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}, "98dab65b-90fa-473b-a243-71b172bcf806": {"id": "98dab65b-90fa-473b-a243-71b172bcf806", "type": "table", "selected": true, "x": 1650, "y": 420, "ports": [{"id": "4a0ced11-3509-4f7a-8e95-e67854125243", "type": "one<PERSON><PERSON>", "x": 1650.*************, "y": 566.************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "98dab65b-90fa-473b-a243-71b172bcf806", "links": []}, {"id": "b9ef26e2-03db-4290-8bca-2d538c376f4f", "type": "one<PERSON><PERSON>", "x": 1824.*************, "y": 566.************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "98dab65b-90fa-473b-a243-71b172bcf806", "links": []}, {"id": "abfa9861-b091-41fa-a4f7-717f280c2562", "type": "one<PERSON><PERSON>", "x": 1650.*************, "y": 514.*************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "98dab65b-90fa-473b-a243-71b172bcf806", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["4a0ced11-3509-4f7a-8e95-e67854125243", "b9ef26e2-03db-4290-8bca-2d538c376f4f", "abfa9861-b091-41fa-a4f7-717f280c2562"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 7, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "currency_id", "is_primary_key": false, "attnum": 1, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "in_rate", "is_primary_key": false, "attnum": 2, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "out_rate", "is_primary_key": false, "attnum": 6, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "from_date", "is_primary_key": false, "attnum": 3, "cltype": "timestamp without time zone", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 0, "max_val_attlen": 6}, {"name": "to_date", "is_primary_key": false, "attnum": 4, "cltype": "timestamp without time zone", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 0, "max_val_attlen": 6}], "name": "currencyRates", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c128"}], "include": [], "cid": "c127"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "9ff78863-f73e-48be-838f-a0230f9ca3e7": {"id": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "type": "table", "selected": false, "x": 855, "y": 285, "ports": [{"id": "4d253a24-e2da-4a15-ac16-5a2809355ad6", "type": "one<PERSON><PERSON>", "x": 856.***********08, "y": 431.*************, "name": "coll-port-4-left", "alignment": "left", "parentNode": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "links": []}, {"id": "d9ae1c37-d25d-4fab-8940-1c4ab13cacd7", "type": "one<PERSON><PERSON>", "x": 1029.************, "y": 431.*************, "name": "coll-port-4-right", "alignment": "right", "parentNode": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "links": []}, {"id": "6765e27d-5f35-4622-b5ab-a22b27a0bceb", "type": "one<PERSON><PERSON>", "x": 856.***********08, "y": 379.*************, "name": "coll-port-0-left", "alignment": "left", "parentNode": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "links": []}, {"id": "14b1c1a0-4c4b-4345-820d-5214279beefd", "type": "one<PERSON><PERSON>", "x": 1029.************, "y": 379.*************, "name": "coll-port-0-right", "alignment": "right", "parentNode": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "links": ["4cb97bdc-52d3-4098-8a8c-64936b482b67"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["4d253a24-e2da-4a15-ac16-5a2809355ad6", "d9ae1c37-d25d-4fab-8940-1c4ab13cacd7", "6765e27d-5f35-4622-b5ab-a22b27a0bceb", "14b1c1a0-4c4b-4345-820d-5214279beefd"], "otherInfo": {"data": {"name": "journal_vouchers_head", "description": "if op_id is null this mean the record is manual", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c13"}], "include": [], "cid": "c12"}], "foreign_key": [], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "opType_id", "is_primary_key": false, "attnum": 15, "cltype": "smallint", "attlen": null, "attprecision": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "branch_id", "is_primary_key": false, "attnum": 4, "cltype": "smallint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "defval": "1", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "date_", "is_primary_key": false, "attnum": 1, "cltype": "date", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "debit_amount", "is_primary_key": false, "attnum": 9, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "credit_amount", "is_primary_key": false, "attnum": 10, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "debit_bca", "is_primary_key": false, "attnum": 11, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "credit_bca", "is_primary_key": false, "attnum": 12, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "debit_tca", "is_primary_key": false, "attnum": 13, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "credit_tca", "is_primary_key": false, "attnum": 14, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "description", "is_primary_key": false, "attnum": 2, "cltype": "character varying", "attlen": "100", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "manual_ref", "is_primary_key": false, "attnum": 7, "cltype": "character varying", "attlen": "10", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "reference_source", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "50", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "56ff7ebe-75a2-4ee5-b772-859124e58d3c": {"id": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "type": "table", "selected": false, "x": 1125, "y": 420, "ports": [{"id": "44a09390-9295-4d14-98c3-3f43534fb48d", "type": "one<PERSON><PERSON>", "x": 1126.***********, "y": 550.*************, "name": "coll-port-1-left", "alignment": "left", "parentNode": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "links": ["4cb97bdc-52d3-4098-8a8c-64936b482b67"]}, {"id": "c6b0dea6-8286-47b9-b022-a4fa1aeb80e4", "type": "one<PERSON><PERSON>", "x": 1299.*************, "y": 550.*************, "name": "coll-port-1-right", "alignment": "right", "parentNode": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "links": []}, {"id": "78bbc339-ebbb-4008-b8b1-e0cc9f720c26", "type": "one<PERSON><PERSON>", "x": 1126.***********, "y": 585.************, "name": "coll-port-2-left", "alignment": "left", "parentNode": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "links": []}, {"id": "4127cdf7-5a5b-4e6d-8cc3-4189a6798924", "type": "one<PERSON><PERSON>", "x": 1299.*************, "y": 585.************, "name": "coll-port-2-right", "alignment": "right", "parentNode": "56ff7ebe-75a2-4ee5-b772-859124e58d3c", "links": ["cdfafccc-39a3-45c9-9f58-922cc7f0aacf"]}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["44a09390-9295-4d14-98c3-3f43534fb48d", "c6b0dea6-8286-47b9-b022-a4fa1aeb80e4", "78bbc339-ebbb-4008-b8b1-e0cc9f720c26", "4127cdf7-5a5b-4e6d-8cc3-4189a6798924"], "otherInfo": {"data": {"columns": [{"name": "pk", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "journal_voucher_head_id", "is_primary_key": false, "attnum": 1, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "account_id", "is_primary_key": false, "attnum": 2, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "debit", "is_primary_key": false, "attnum": 3, "cltype": "boolean", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "defval": "False"}, {"name": "amount", "is_primary_key": false, "attnum": 4, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "base_currency_amount", "is_primary_key": false, "attnum": 6, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "tax_currency_amount", "is_primary_key": false, "attnum": 7, "cltype": "numeric", "attlen": "18", "attprecision": "5", "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": [], "min_val_attlen": 1, "max_val_attlen": 1000, "min_val_attprecision": -1000, "max_val_attprecision": 1000}, {"name": "description", "is_primary_key": false, "attnum": 5, "cltype": "character varying", "attlen": "200", "min_val_attlen": 1, "max_val_attlen": **********, "attprecision": null, "attnotnull": false, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "journal_vouchers_detail", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "pk", "cid": "c69"}], "include": [], "cid": "c68"}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "journal_voucher_head_id", "referenced": "pk", "references": "9ff78863-f73e-48be-838f-a0230f9ca3e7", "references_table_name": "journal_vouchers_head", "cid": "c71"}], "confupdtype": "c", "confdeltype": "a", "cid": "c70"}, {"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "account_id", "referenced": "pk", "references": "d77e12a0-cec2-4033-92d3-409b2784b19c", "references_table_name": "account", "cid": "c73"}], "confupdtype": "c", "confdeltype": "a", "cid": "c72"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false, "fillColor": null, "textColor": null}}}, "e236436c-5fbd-4e56-872b-ee0f0ffea5ec": {"id": "e236436c-5fbd-4e56-872b-ee0f0ffea5ec", "type": "table", "selected": false, "x": 1635, "y": 225, "ports": [{"id": "221c1154-836c-441f-b85b-7e07117b17ee", "type": "one<PERSON><PERSON>", "x": 1635.*************, "y": 319.*************3, "name": "coll-port-0-left", "alignment": "left", "parentNode": "e236436c-5fbd-4e56-872b-ee0f0ffea5ec", "links": ["f7cd8ef0-35ed-4c81-a7e2-5b00b4281ce5"]}, {"id": "2cdd749b-0f10-4a4a-aa5e-4af3925c3c50", "type": "one<PERSON><PERSON>", "x": 1809.*************, "y": 319.*************3, "name": "coll-port-0-right", "alignment": "right", "parentNode": "e236436c-5fbd-4e56-872b-ee0f0ffea5ec", "links": []}], "name": "Untitled", "color": "rgb(0,192,255)", "portsInOrder": [], "portsOutOrder": ["221c1154-836c-441f-b85b-7e07117b17ee", "2cdd749b-0f10-4a4a-aa5e-4af3925c3c50"], "otherInfo": {"data": {"columns": [{"name": "account_id", "is_primary_key": true, "attnum": 0, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}, {"name": "item_id", "is_primary_key": true, "attnum": 1, "cltype": "bigint", "attlen": null, "attprecision": null, "attcompression": "", "collspcname": null, "attnotnull": true, "colconstype": "n", "attidentity": "a", "attoptions": []}], "name": "item_accounts", "coll_inherits": [], "hastoasttable": true, "toast_autovacuum_enabled": "x", "autovacuum_enabled": "x", "primary_key": [{"columns": [{"column": "account_id"}, {"column": "item_id"}], "include": []}], "foreign_key": [{"confmatchtype": false, "autoindex": false, "columns": [{"local_column": "account_id", "referenced": "pk", "references": "d77e12a0-cec2-4033-92d3-409b2784b19c", "references_table_name": "account", "cid": "c126"}], "confupdtype": "c", "confdeltype": "a", "cid": "c125"}], "partition_keys": [], "partitions": [], "partition_type": "range", "is_partitioned": false, "schema": "accounting"}, "note": "", "metadata": {"data_failed": false, "is_promise": false}}}}}]}}