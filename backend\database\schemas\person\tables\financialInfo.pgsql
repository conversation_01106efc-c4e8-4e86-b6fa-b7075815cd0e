CREATE TABLE "person"."financialInfo" (
 "pk" BIGINT NOT NULL REFERENCES "person"."person" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "financial_number" BIGINT NOT NULL UNIQUE ,
 "commercial_register_number" BIGINT NOT NULL UNIQUE ,
 "TVA_registration_number" VARCHAR(20) NOT NULL UNIQUE ,
 "capital" MONEY ,
 "legalForm" SMALLINT NOT NULL REFERENCES "enterprise"."legalForm" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "financialInfo_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "person"."financialInfo" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
