create or replace function auth.ai_group_createGroup() returns trigger as $$
declare
    v_group_name name;
begin

  -- generate a group name based on name_
  v_group_name := current_setting('db.group_prefix') || quote_ident(new.name_);
  -- create the group
  execute 'create role '|| quote_ident(v_group_name);
  new.rolename = v_group_name;

  IF new.app_id != 0 THEN
    insert into auth."group_personType" (group_id, "personType_id") 
      select distinct NEW.pk, unnest(personTypeList) 
      from auth.app 
      where pk = new.app_id and active_ = true;

    insert into auth.group_permission (group_id, "appObjects_id")
        select NEW.pk, ap.pk
        from auth."appObjects" ap join auth.app a on ap.app_id = a.pk
        where a.active_ = true and ap.app_id = new.app_id;

  ELSE -- the all apps group case
    
    RAISE EXCEPTION 'Invalid input for app id this must not shown in production';

  END IF;

end;
$$ language plpgsql;

alter function auth.ai_group_createGroup() owner to #POSTGRES_DEFAULT_ADMIN#;

create or replace trigger trg_ai_group_createGroup
  after insert on auth.group
  for each row
  execute procedure auth.ai_group_createGroup();