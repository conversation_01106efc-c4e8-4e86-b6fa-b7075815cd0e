CREATE TABLE "auth"."user_permission" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."user_permission_seq"'::regclass) ,
 "user_id" SMALLINT NOT NULL REFERENCES "auth"."user" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "appObjects_id" SMALLINT NOT NULL REFERENCES "auth"."appObjects" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "user_permission_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_permission" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."user_permission_seq" OWNED BY "auth"."user_permission"."pk";
