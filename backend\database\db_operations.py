import warnings
import psycopg2
import time

from config import config, postgrestConf
from psycopg2 import Error, sql

# Configure warning handling
warnings.filterwarnings('always', category=Warning)

def refresh_collation_version(conn, database_name: str) -> None:
    """Refresh collation version for a specific database"""
    try:
        with conn.cursor() as cur:
            run_sql = sql.SQL("ALTER DATABASE {} REFRESH COLLATION VERSION").format(sql.Identifier(database_name))
            config.debug(run_sql.as_string(cur))
            cur.execute(run_sql)
            config.info(f"Refreshed collation version for database {database_name}")
    except psycopg2.Error as e:
        config.error(f"Failed to refresh collation for {database_name}: {e}")
        raise

def handle_collation_warning(conn) -> None:
    """Handle collation version mismatch by refreshing both postgres and target database"""
    try:
        refresh_collation_version(conn, "postgres")
        if config.DB_NAME != "postgres":
            refresh_collation_version(conn, config.DB_NAME)
    except psycopg2.Error as e:
        config.error(f"Failed to handle collation warning: {e}")
        raise

def create_database(retry_count: int = 0) -> bool | None:
    """
    Create database with retry mechanism and proper error handling
    Returns:
        bool: True if database was created or already exists, False on failure
        None: if validation fails
    """
    try:
        config.info(f"Attempting to connect to database (attempt {retry_count + 1}/{config.MAX_RETRIES + 1})")
        with config.get_postgres_connection() as conn: 
            try:
                with warnings.catch_warnings(record=True) as w:
                    with conn.cursor() as cur:
                        # Check if database exists
                        config.info(f"Checking if database '{config.DB_NAME}' exists")
                        run_sql = sql.SQL("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s"), [config.DB_NAME]
                        config.debug(run_sql.as_string(cur))
                        cur.execute(run_sql)
            
                        exists = cur.fetchone()
                        
                        if not exists:
                            try:
                                config.info(f"Creating database '{config.DB_NAME}'")
                                run_sql = sql.SQL(
                                                    "CREATE DATABASE {} WITH ENCODING 'UTF8' "
                                                    "LC_COLLATE 'en_US.UTF-8' LC_CTYPE 'en_US.UTF-8' "
                                                    "TEMPLATE template0"
                                                ).format(sql.Identifier(config.DB_NAME))
                                
                                config.debug(run_sql.as_string(cur))
                                cur.execute(run_sql)
                                config.info(f"Database '{config.DB_NAME}' created successfully!")
                                
                                run_sql = sql.SQL("ALTER DATABASE {} OWNER TO {}").format(sql.Identifier(config.DB_NAME),sql.Identifier(config.POSTGRES_DEFAULT_ADMIN))
                                config.debug(run_sql.as_string(cur))
                                cur.execute(run_sql)
                                config.info(f"Changed owner of database {config.DB_NAME} to {config.POSTGRES_DEFAULT_ADMIN}")
                                
                            except psycopg2.Error as e:
                                config.error(f"Failed to create database '{config.DB_NAME}': {e}")
                                raise
                        else:
                            config.info(f"Database '{config.DB_NAME}' already exists")
                            user_input = input(f"The database {config.DB_NAME} already exists. delete(d) , recreate(r), continue(c)?: ").strip().lower()
                            if user_input == "d":
                                try:
                                    config.info(f"Dropping database '{config.DB_NAME}'")
                                    drop_database()
                                    config.info(f"Database '{config.DB_NAME}' dropped successfully!")
                                    
                                except psycopg2.Error as e:
                                    config.error(f"Failed to drop database '{config.DB_NAME}': {e}")
                                    raise
                                
                            elif user_input=="r":
                                try:
                                    config.info(f"Recreating database '{config.DB_NAME}'")
                                    drop_database()
                                    config.info(f"Database '{config.DB_NAME}' dropped successfully!")
                                    return create_database(0)
                                except psycopg2.Error as e:
                                    config.error(f"Failed to Recreating database '{config.DB_NAME}': {e}")
                                    raise
                                
                            elif user_input=="c":
                                config.info(f"Continuing with existing database '{config.DB_NAME}'")
                                return True
                            
                            else:
                                config.info(f"Invalid input. Exiting...")
                                return False
                            
                        # JWT config set for database
                        if (set_db_jwt_config(cur)):
                            conn.commit()
                            config.info(f"JWT config set for database {config.DB_NAME}")
                        else:
                            conn.rollback()
                            config.error(f"Error setting JWT config for database {config.DB_NAME}")
                            return False
                        
                        # Custom Parameters Setting for dabase
                        if (set_custom_parameters(cur)):
                            conn.commit()
                            config.info(f"Custom Parameters set for database {config.DB_NAME}")
                        else:
                            conn.rollback()
                            config.error(f"Error setting Custom Parameters for database {config.DB_NAME}")
                            return False
                        
                     # Check if we caught any collation warnings
                    if any("collation version mismatch" in str(warning.message) for warning in w):
                        config.warning("Detected collation version mismatch. Attempting to refresh...")
                        handle_collation_warning(conn)
                        config.info("Collation refresh completed")
                        
                return db_after_create_ops()

            except (psycopg2.OperationalError, psycopg2.Error) as e:
                if retry_count < config.MAX_RETRIES:
                    config.warning(f"Database operation failed. Attempt {retry_count + 1} of {config.MAX_RETRIES}. Error: {e}")
                    config.info(f"Retrying in {config.RETRY_DELAY} seconds...")
                    time.sleep(config.RETRY_DELAY)
                    return create_database(retry_count + 1)
                else:
                    config.error(f"All retry attempts failed. Last error: {e}")
                    return False
                
    except Exception as e:
        config.error(f"Unexpected error during database operation: {e}")
        return False

def drop_database(retry_count: int = 0) -> bool | None:
    """
    Drop database with retry mechanism and proper error handling
    Returns:
        bool: True if database was dropped or did not exist, False on failure
        None: if validation fails
    """
    try:
        config.info(f"Attempting to connect to database (attempt {retry_count + 1}/{config.MAX_RETRIES + 1})")
        with config.get_postgres_connection() as conn:
            try:
                with conn.cursor() as cur:                    
                    config.info(f"Dropping database '{config.DB_NAME}'")
                    run_sql = sql.SQL("DROP DATABASE IF EXISTS {}").format(sql.Identifier(config.DB_NAME))
                    config.debug(run_sql.as_string(cur))
                    cur.execute(run_sql)
                    config.info(f"Database '{config.DB_NAME}' dropped successfully!")
                  
                return True

            except (psycopg2.OperationalError, psycopg2.Error) as e:
                if retry_count < config.MAX_RETRIES:
                    config.warning(f"Database operation failed. Attempt {retry_count + 1} of {config.MAX_RETRIES}. Error: {e}")
                    config.info(f"Retrying in {config.RETRY_DELAY} seconds...")
                    time.sleep(config.RETRY_DELAY)
                    return drop_database(retry_count + 1)
                else:
                    config.error(f"All retry attempts failed. Last error: {e}")
                    return False
    except Exception as e:
        config.error(f"Unexpected error during database operation: {e}")
        return False

def set_db_jwt_config(cur) -> bool:
    try:
        
        run_sql = sql.SQL("ALTER DATABASE {} SET app.jwt_secret TO {}").format(sql.Identifier(config.DB_NAME), sql.Literal(postgrestConf.JWT_SECRET))
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)
        
        cur.execute(sql.SQL("ALTER DATABASE {} SET app.jwt_expiry TO {}").format(sql.Identifier(config.DB_NAME), sql.Literal(postgrestConf.JWT_CACHE)))
        
        config.info(f"JWT config set for database {config.DB_NAME}")
        return True
    
    except (Error, Exception) as e:
        config.error(f"Error setting JWT config for database {config.DB_NAME}: {e}")
        return False

def set_custom_parameters(cur) -> bool:
    try:
        config.debug("Setting Custom Parameters for database")
        run_sql = sql.SQL("ALTER DATABASE {} SET db.user_prefix = {}").format(sql.Identifier(config.DB_NAME),sql.Literal(config.USERS_PREFIX))
        config.debug(run_sql.as_string(cur))
        cur.execute(run_sql)
        cur.execute(sql.SQL("ALTER DATABASE {} SET db.group_prefix = {}").format(sql.Identifier(config.DB_NAME),sql.Literal(config.GROUPS_PREFIX)))
        cur.execute(sql.SQL("ALTER DATABASE {} SET db.single_branch = {}").format(sql.Identifier(config.DB_NAME),sql.Literal(config.SINGLE_BRANCH)))
        cur.execute(sql.SQL("ALTER DATABASE {} SET db.single_department = {}").format(sql.Identifier(config.DB_NAME),sql.Literal(config.SINGLE_DEPARTMENT)))
        
        config.info("Custom Parameters for database set")
        return True
    
    except (Error, Exception) as e:
        config.error(f"Error setting Custom Parameters for database {config.DB_NAME}: {e}")
        return False    
    
def db_after_create_ops() -> bool:
    with config.get_postgres_connection() as conn:
        with conn.cursor() as cur:
            try:
                cur.execute(sql.SQL("GRANT ALL PRIVILEGES ON DATABASE {} TO {};").format(sql.Identifier(config.DB_NAME), sql.Identifier(config.POSTGRES_DEFAULT_ADMIN)))
                config.info(f"Granted all privileges on database '{config.DB_NAME}' to '{config.POSTGRES_DEFAULT_ADMIN}'")
                conn.commit()
            except (Error, Exception) as e:
                config.error(f"Error granting privileges on database '{config.DB_NAME}' to '{config.POSTGRES_DEFAULT_ADMIN}': {e}")
                conn.rollback()
                return False
            
            try:
                cur.execute(sql.SQL("REVOKE ALL PRIVILEGES ON DATABASE {} FROM public;").format(sql.Identifier(config.DB_NAME)))
                config.info(f"Revoked all privileges on database '{config.DB_NAME}' from 'public'")
                conn.commit()
            except (Error, Exception) as e:
                config.error(f"Error revoking privileges on database '{config.DB_NAME}' from 'public': {e}")
                conn.rollback()
                return False
            
            try:
                cur.execute(sql.SQL("ALTER DEFAULT PRIVILEGES REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC"))
                config.info(f"Revoked execute privileges on functions from public")
                conn.commit()
            except (Error, Exception) as e:
                config.error(f"Error revoking execute privileges on functions from public: {e}")
                conn.rollback()
                return False
                
            return True
