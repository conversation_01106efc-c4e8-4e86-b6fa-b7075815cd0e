CREATE TABLE "enterprise"."financialYear" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."financialYear_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "start_" DATE NOT NULL ,
 "end_" DATE ,
 "accounting_info" JSONB ,
 CONSTRAINT "financialYear_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."financialYear" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."financialYear_id_seq" OWNED BY "enterprise"."financialYear"."pk";
