CREATE TABLE "enterprise"."branch_department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('enterprise."branch_department_id_seq"'::regclass) ,
 "branch_id" SMALLINT NOT NULL DEFAULT '0'::SMALLINT REFERENCES "enterprise"."branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "branch_department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "enterprise"."branch_department" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "enterprise"."branch_department_id_seq" OWNED BY "enterprise"."branch_department"."pk";
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('1', '1', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('2', '2', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('3', '6', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('4', '31', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('5', '33', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('6', '32', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('7', '8', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('8', '35', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('9', '36', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('10', '9', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('11', '5', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('12', '24', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('13', '27', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('14', '25', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('15', '26', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('16', '30', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('17', '29', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('18', '28', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('19', '23', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('20', '3', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('21', '14', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('22', '13', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('23', '15', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('24', '37', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('25', '38', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('26', '12', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('27', '40', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('28', '4', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('29', '16', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('30', '21', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('31', '19', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('32', '41', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('33', '42', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('34', '22', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('35', '45', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('36', '46', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('37', '43', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('38', '44', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('39', '20', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('40', '18', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('41', '17', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('43', '7', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('44', '47', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('45', '34', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('46', '48', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('47', '11', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('48', '39', '0');
INSERT INTO "enterprise"."branch_department" ("pk", "department_id", "branch_id") VALUES ('49', '10', '0');
