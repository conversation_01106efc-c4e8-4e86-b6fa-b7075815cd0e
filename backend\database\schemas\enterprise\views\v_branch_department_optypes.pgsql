CREATE VIEW enterprise.v_branch_department_optypes
WITH (
  security_barrier=true,
  security_invoker=true
) AS
SELECT 
    brdep.branch_id, 
    br.name_ AS branch_name, 
    brdep.department_id, 
    dep.name_ AS department_name, 
    depop."opType_id" AS optype_id, 
    op.name_ AS optype_name
FROM 
enterprise.branch br  
    JOIN enterprise.branch_department brdep ON br.pk = brdep.branch_id
    JOIN enterprise.department dep ON brdep.department_id = dep.pk
    JOIN enterprise.department_optypes depop ON brdep.department_id = depop.department_id
    JOIN enterprise."opTypes" op ON depop."opType_id" = op.pk;

ALTER TABLE enterprise.v_branch_department_optypes OWNER TO #POSTGRES_DEFAULT_ADMIN#;