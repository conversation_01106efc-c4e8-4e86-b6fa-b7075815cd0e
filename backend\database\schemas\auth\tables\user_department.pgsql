CREATE TABLE "auth"."user_department" (
 "pk" SMALLINT NOT NULL DEFAULT nextval('auth."user_department_id_seq"'::regclass) ,
 "user_branch_id" SMALLINT NOT NULL REFERENCES "auth"."user_branch" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN NOT NULL DEFAULT 'false'::B<PERSON><PERSON><PERSON><PERSON> ,
 CONSTRAINT "user_department_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."user_department" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
