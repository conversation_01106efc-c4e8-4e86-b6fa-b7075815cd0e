import os
from psycopg2 import sql
from config import config , postgrestConf

SCHEMA_DIR = os.path.join(os.path.dirname(__file__), 'schemas')

def check_schema_exists(cur, schema_name: str) -> bool:
    """
    Check if a schema exists.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema to check
        
    Returns:
        bool: True if schema exists, False otherwise
    """
    cur.execute(sql.SQL("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s"), (schema_name,))
    return cur.fetchone()[0]

def get_schema_list_from_dir() -> list[str]:
    """Get list of schema directories from schemas folder."""
    # List of directories to exclude
    exclude_schemas = {'__pycache__', }
    schemas = []
    
    for item in os.listdir(SCHEMA_DIR):
        item_path = os.path.join(SCHEMA_DIR, item)
        if os.path.isdir(item_path) and item not in exclude_schemas:
            schemas.append(item.lower())
            # forcing schema name to be lower case
    
    return schemas

def create_schema(cur, schema_name: str) -> bool:
    """
    Create a new schema with authorization to specified role.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema to create
        role_name: Role name to authorize
        
    Returns:
        bool: True if schema was created or already exists with correct authorization
    """
    try:
        
        run_sql = sql.SQL("CREATE SCHEMA IF NOT EXISTS {} AUTHORIZATION {}").format(sql.Identifier(schema_name), sql.Identifier(config.POSTGRES_DEFAULT_ADMIN))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Ensured schema {schema_name} exists with authorization to {config.POSTGRES_DEFAULT_ADMIN}")
        
        run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA {} REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;").format(sql.Identifier(schema_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Revoked execute privilege on functions in schema {schema_name}")
        
        run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA {} REVOKE ALL ON TABLES FROM PUBLIC;").format(sql.Identifier(schema_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Revoked all privileges on tables in schema {schema_name}")
        
        run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA {} REVOKE USAGE ON SEQUENCES FROM PUBLIC;").format(sql.Identifier(schema_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Revoked all privileges on sequences in schema {schema_name}")
        
        return True
        
    except Exception as e:
        config.error(f"Error creating schema {schema_name}: {e}")
        return False

def drop_schema(cur, schema_name: str) -> bool:
    """
    Drop a schema.
    
    Args:
        cur: Database cursor
        schema_name: Name of the schema to drop
        
    Returns:
        bool: True if schema was dropped successfully
    """
    try:
        run_sql = sql.SQL("DROP SCHEMA IF EXISTS {} CASCADE").format(sql.Identifier(schema_name))
        config.debug(run_sql.as_string(cur))
        config.cumulativeSQL(run_sql.as_string(cur))
        cur.execute(run_sql)
        config.info(f"Dropped schema {schema_name}")
        return True
    
    except Exception as e:
        config.error(f"Error dropping schema {schema_name}: {e}")
        return False

def create_schemas() -> bool:
    """
    Main function to set up all schemas from schemas directory structure.
    
    Returns:
        bool: True if all schemas were processed successfully, False otherwise
    """
    try:
        schemas = get_schema_list_from_dir()
        
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
            
                for schema_name in schemas:
                    if not create_schema(cur, schema_name):
                        return False
                
                # create schema for extensions
                create_schema(cur, config.DB_SCHEMA_EXTENSION)
                        
                config.info("All schemas processed successfully")
                
                run_sql = sql.SQL("REVOKE ALL ON SCHEMA public FROM PUBLIC;")
                config.debug(run_sql.as_string(cur))
                config.cumulativeSQL(run_sql.as_string(cur))
                cur.execute(run_sql)
                config.info(f"Revoked all privileges on public schema")
                
                run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE ALL ON TABLES FROM PUBLIC;")
                config.debug(run_sql.as_string(cur))
                config.cumulativeSQL(run_sql.as_string(cur))
                cur.execute(run_sql)
                config.info(f"Revoked all privileges on tables in public schema")
                
                run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE ALL ON SEQUENCES FROM PUBLIC;")
                config.debug(run_sql.as_string(cur))
                config.cumulativeSQL(run_sql.as_string(cur))
                cur.execute(run_sql)
                config.info(f"Revoked all privileges on sequences in public schema")
                
                run_sql = sql.SQL("ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE ALL ON FUNCTIONS FROM PUBLIC;")
                config.debug(run_sql.as_string(cur))
                config.cumulativeSQL(run_sql.as_string(cur))
                cur.execute(run_sql)
                config.info(f"Revoked all privileges on functions in public schema")
                
                conn.commit()
                
                return True
                
    except Exception as e:
        config.error(f"Error during schema setup: {e}")
        return False

def drop_schemas() -> bool:
    
    """
    Main function to drop all schemas.
    
    Returns:
        bool: True if all schemas were dropped successfully, False otherwise
    """

    try:
        schemas = get_schema_list_from_dir() + [postgrestConf.DB_SCHEMA_EXTENSION]
        
        with config.get_postgres_connection(dbname=config.DB_NAME) as conn:
            with conn.cursor() as cur:
                for schema_name in schemas:
                    if not drop_schema(cur, schema_name):
                        return False
                        
                config.info("All schemas dropped successfully")
                return True
                
    except Exception as e:
        config.error(f"Error during schema drop: {e}")
        return False
    