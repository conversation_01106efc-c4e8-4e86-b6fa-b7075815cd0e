CREATE TABLE "enterprise"."department_optypes" (
 "department_id" SMALLINT NOT NULL REFERENCES "enterprise"."department" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "opType_id" SMALLINT NOT NULL REFERENCES "enterprise"."opTypes" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 CONSTRAINT "department_optypes_pkey" PRIMARY KEY ("department_id", "opType_id")
);
ALTER TABLE "enterprise"."department_optypes" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
