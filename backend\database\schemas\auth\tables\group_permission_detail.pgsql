CREATE TABLE "auth"."group_permission_detail" (
 "pk" BIGINT NOT NULL DEFAULT nextval('auth."group_permission_detail_id_seq"'::regclass) ,
 "group_permission_id" BIGINT NOT NULL REFERENCES "auth"."group_permission" ("pk") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "allow" BOOLEAN DEFAULT 'false'::B<PERSON><PERSON>EAN ,
 "permission_name_id" VARCHAR(10) NOT NULL REFERENCES "auth"."permission" ("name_") MATCH SIMPLE ON UPDATE CASCADE ON DELETE CASCADE,
 "fieldlist" VARCHAR(50)[] ,
 CONSTRAINT "group_permission_detail_pkey" PRIMARY KEY ("pk")
);
ALTER TABLE "auth"."group_permission_detail" ADD CONSTRAINT "group_permission_detail_check" CHECK (permission_name_id IN ('Select', 'Insert', 'Update','View','Append','Change') OR (permission_name_id NOT IN ('Select', 'Insert', 'Update','View','Append','Change') AND fieldlist IS NULL) OR (allow is false AND fieldlist is Null));
ALTER TABLE "auth"."group_permission_detail" OWNER TO #POSTGRES_DEFAULT_ADMIN#;
ALTER SEQUENCE "auth"."group_permission_detail_id_seq" OWNED BY "auth"."group_permission_detail"."pk";
